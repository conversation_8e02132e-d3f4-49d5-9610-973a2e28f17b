// Modern SVG Icon Library for Admin Dashboard
// Consistent with frontend design system using golden yellow (#F0B21B) accent color

import React from 'react';

const iconStyle = {
  width: '24px',
  height: '24px',
  fill: 'none',
  stroke: 'currentColor',
  strokeWidth: '2',
  strokeLinecap: 'round',
  strokeLinejoin: 'round'
};

export const DashboardIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <rect x="3" y="3" width="7" height="7"/>
    <rect x="14" y="3" width="7" height="7"/>
    <rect x="14" y="14" width="7" height="7"/>
    <rect x="3" y="14" width="7" height="7"/>
  </svg>
);

export const InventoryIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
    <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
    <line x1="12" y1="22.08" x2="12" y2="12"/>
  </svg>
);

export const ProductsIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
    <polyline points="9,22 9,12 15,12 15,22"/>
  </svg>
);

export const OrdersIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
    <polyline points="14,2 14,8 20,8"/>
    <line x1="16" y1="13" x2="8" y2="13"/>
    <line x1="16" y1="17" x2="8" y2="17"/>
    <polyline points="10,9 9,9 8,9"/>
  </svg>
);

export const SuppliersIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M3 21h18"/>
    <path d="M5 21V7l8-4v18"/>
    <path d="M19 21V11l-6-4"/>
  </svg>
);

export const UsersIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
    <circle cx="9" cy="7" r="4"/>
    <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
  </svg>
);

export const AnalyticsIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
  </svg>
);

export const LogoutIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
    <polyline points="16,17 21,12 16,7"/>
    <line x1="21" y1="12" x2="9" y2="12"/>
  </svg>
);

export const SettingsIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="3"/>
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
  </svg>
);

export const NotificationIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
    <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
  </svg>
);

export const SearchIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="11" cy="11" r="8"/>
    <path d="M21 21l-4.35-4.35"/>
  </svg>
);

export const FilterIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
  </svg>
);

export const EditIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
  </svg>
);

export const DeleteIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polyline points="3,6 5,6 21,6"/>
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
    <line x1="10" y1="11" x2="10" y2="17"/>
    <line x1="14" y1="11" x2="14" y2="17"/>
  </svg>
);

export const AddIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="12" y1="8" x2="12" y2="16"/>
    <line x1="8" y1="12" x2="16" y2="12"/>
  </svg>
);

export const ViewIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

export const DownloadIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="7,10 12,15 17,10"/>
    <line x1="12" y1="15" x2="12" y2="3"/>
  </svg>
);

export const UploadIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="17,8 12,3 7,8"/>
    <line x1="12" y1="3" x2="12" y2="15"/>
  </svg>
);

export const RefreshIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polyline points="23,4 23,10 17,10"/>
    <polyline points="1,20 1,14 7,14"/>
    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
  </svg>
);

// Status and Alert Icons
export const SuccessIcon = ({ className = '', color = '#10B981' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
    <polyline points="22,4 12,14.01 9,11.01"/>
  </svg>
);

export const WarningIcon = ({ className = '', color = '#F59E0B' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
    <line x1="12" y1="9" x2="12" y2="13"/>
    <line x1="12" y1="17" x2="12.01" y2="17"/>
  </svg>
);

export const ErrorIcon = ({ className = '', color = '#EF4444' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="15" y1="9" x2="9" y2="15"/>
    <line x1="9" y1="9" x2="15" y2="15"/>
  </svg>
);

export const InfoIcon = ({ className = '', color = '#3B82F6' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="12" y1="16" x2="12" y2="12"/>
    <line x1="12" y1="8" x2="12.01" y2="8"/>
  </svg>
);

// Connection Status Icons
export const ConnectedIcon = ({ className = '', color = '#10B981' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <polyline points="8,12 11,15 16,10"/>
  </svg>
);

export const DisconnectedIcon = ({ className = '', color = '#EF4444' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="15" y1="9" x2="9" y2="15"/>
    <line x1="9" y1="9" x2="15" y2="15"/>
  </svg>
);

// Additional icons for Order Management
export const PackageIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <line x1="16.5" y1="9.4" x2="7.5" y2="4.21"/>
    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
    <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
    <line x1="12" y1="22.08" x2="12" y2="12"/>
  </svg>
);

export const EyeIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

export const TrashIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <polyline points="3,6 5,6 21,6"/>
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
    <line x1="10" y1="11" x2="10" y2="17"/>
    <line x1="14" y1="11" x2="14" y2="17"/>
  </svg>
);

export const ExportIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="7,10 12,15 17,10"/>
    <line x1="12" y1="15" x2="12" y2="3"/>
  </svg>
);

export const ChevronLeftIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <polyline points="15,18 9,12 15,6"/>
  </svg>
);

export const ChevronRightIcon = ({ className = '', color = 'currentColor', size = 24 }) => (
  <svg className={className} style={{...iconStyle, width: size, height: size, stroke: color}} viewBox="0 0 24 24">
    <polyline points="9,18 15,12 9,6"/>
  </svg>
);

// Export all icons as a collection
export const AdminIcons = {
  Dashboard: DashboardIcon,
  Inventory: InventoryIcon,
  Products: ProductsIcon,
  Orders: OrdersIcon,
  Suppliers: SuppliersIcon,
  Users: UsersIcon,
  Analytics: AnalyticsIcon,
  Logout: LogoutIcon,
  Settings: SettingsIcon,
  Notification: NotificationIcon,
  Search: SearchIcon,
  Filter: FilterIcon,
  Edit: EditIcon,
  Delete: DeleteIcon,
  Add: AddIcon,
  View: ViewIcon,
  Download: DownloadIcon,
  Upload: UploadIcon,
  Refresh: RefreshIcon,
  Success: SuccessIcon,
  Warning: WarningIcon,
  Error: ErrorIcon,
  Info: InfoIcon,
  Connected: ConnectedIcon,
  Disconnected: DisconnectedIcon,
  Package: PackageIcon,
  Eye: EyeIcon,
  Trash: TrashIcon,
  Export: ExportIcon,
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon
};

export default AdminIcons;
