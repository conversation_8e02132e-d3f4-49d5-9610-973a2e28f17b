import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';

const OrderHistory = () => {
    const { user } = useAuth();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [filter, setFilter] = useState('all');

    useEffect(() => {
        fetchOrders();
    }, []);

    const fetchOrders = async () => {
        setLoading(true);
        try {
            // Mock order data - replace with actual API call
            const mockOrders = [
                {
                    id: 'ORD-001',
                    orderNumber: 'ORD-2024-001',
                    date: '2024-01-15',
                    status: 'delivered',
                    total: 1299.99,
                    currency: 'PHP',
                    items: [
                        {
                            id: 1,
                            name: 'Executive Office Chair',
                            quantity: 1,
                            price: 899.99,
                            image: '/images/chair-executive.jpg'
                        },
                        {
                            id: 2,
                            name: 'Desk Organizer Set',
                            quantity: 2,
                            price: 200.00,
                            image: '/images/organizer.jpg'
                        }
                    ],
                    shippingAddress: {
                        street: '123 Business Ave',
                        city: 'Manila',
                        state: 'Metro Manila',
                        zipCode: '1000',
                        country: 'Philippines'
                    },
                    tracking: {
                        number: 'TRK123456789',
                        carrier: 'LBC Express',
                        status: 'delivered',
                        estimatedDelivery: '2024-01-20'
                    }
                },
                {
                    id: 'ORD-002',
                    orderNumber: 'ORD-2024-002',
                    date: '2024-01-10',
                    status: 'processing',
                    total: 2499.99,
                    currency: 'PHP',
                    items: [
                        {
                            id: 3,
                            name: 'Standing Desk',
                            quantity: 1,
                            price: 2499.99,
                            image: '/images/desk-standing.jpg'
                        }
                    ],
                    shippingAddress: {
                        street: '456 Corporate St',
                        city: 'Quezon City',
                        state: 'Metro Manila',
                        zipCode: '1100',
                        country: 'Philippines'
                    },
                    tracking: {
                        number: 'TRK987654321',
                        carrier: 'J&T Express',
                        status: 'in_transit',
                        estimatedDelivery: '2024-01-25'
                    }
                }
            ];
            setOrders(mockOrders);
        } catch (error) {
            console.error('Failed to fetch orders:', error);
        } finally {
            setLoading(false);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending':
                return '#f39c12';
            case 'processing':
                return '#3498db';
            case 'shipped':
                return '#9b59b6';
            case 'delivered':
                return '#27ae60';
            case 'cancelled':
                return '#e74c3c';
            default:
                return '#95a5a6';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending':
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                    </svg>
                );
            case 'processing':
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                    </svg>
                );
            case 'shipped':
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="1" y="3" width="15" height="13"/>
                        <polygon points="16,8 20,8 23,11 23,16 16,16"/>
                        <circle cx="5.5" cy="18.5" r="2.5"/>
                        <circle cx="18.5" cy="18.5" r="2.5"/>
                    </svg>
                );
            case 'delivered':
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                );
            case 'cancelled':
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                );
            default:
                return (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                );
        }
    };

    const filteredOrders = orders.filter(order => {
        if (filter === 'all') return true;
        return order.status === filter;
    });

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatCurrency = (amount, currency = 'PHP') => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    if (loading) {
        return (
            <div className="order-history">
                <div className="section-header">
                    <div>
                        <h2 className="section-title">Order History</h2>
                        <p className="section-subtitle">Track your orders and view purchase history</p>
                    </div>
                </div>
                <div className="loading-state">
                    <svg className="spinner" width="32" height="32" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" strokeDasharray="32" strokeDashoffset="32">
                            <animate attributeName="stroke-dashoffset" dur="1s" values="32;0;32" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                    <p>Loading your orders...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="order-history">
            <div className="section-header">
                <div>
                    <h2 className="section-title">Order History</h2>
                    <p className="section-subtitle">Track your orders and view purchase history</p>
                </div>
                <div className="order-filters">
                    <select
                        className="filter-select"
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                    >
                        <option value="all">All Orders</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>

            {filteredOrders.length === 0 ? (
                <div className="empty-state">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                    </svg>
                    <h3>No orders found</h3>
                    <p>You haven't placed any orders yet or no orders match your filter.</p>
                </div>
            ) : (
                <div className="orders-list">
                    {filteredOrders.map(order => (
                        <div key={order.id} className="order-card">
                            <div className="order-header">
                                <div className="order-info">
                                    <h3 className="order-number">{order.orderNumber}</h3>
                                    <p className="order-date">{formatDate(order.date)}</p>
                                </div>
                                <div className="order-status">
                                    <span 
                                        className="status-badge"
                                        style={{ 
                                            backgroundColor: `${getStatusColor(order.status)}20`,
                                            color: getStatusColor(order.status),
                                            border: `1px solid ${getStatusColor(order.status)}40`
                                        }}
                                    >
                                        {getStatusIcon(order.status)}
                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                    </span>
                                </div>
                            </div>

                            <div className="order-items">
                                {order.items.map(item => (
                                    <div key={item.id} className="order-item">
                                        <div className="item-image">
                                            <div className="placeholder-image">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                                    <polyline points="21,15 16,10 5,21"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <div className="item-details">
                                            <h4 className="item-name">{item.name}</h4>
                                            <p className="item-quantity">Qty: {item.quantity}</p>
                                        </div>
                                        <div className="item-price">
                                            {formatCurrency(item.price, order.currency)}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="order-footer">
                                <div className="order-total">
                                    <strong>Total: {formatCurrency(order.total, order.currency)}</strong>
                                </div>
                                <div className="order-actions">
                                    <button
                                        className="btn-secondary"
                                        onClick={() => setSelectedOrder(order)}
                                    >
                                        View Details
                                    </button>
                                    {order.status === 'delivered' && (
                                        <button className="btn-primary">
                                            Reorder
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default OrderHistory;
