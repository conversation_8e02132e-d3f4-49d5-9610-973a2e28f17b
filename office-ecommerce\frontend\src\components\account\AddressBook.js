import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';

const AddressBook = () => {
    const { user } = useAuth();
    const [addresses, setAddresses] = useState([]);
    const [showAddForm, setShowAddForm] = useState(false);
    const [editingAddress, setEditingAddress] = useState(null);
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        type: 'shipping',
        firstName: '',
        lastName: '',
        company: '',
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'Philippines',
        phone: '',
        isDefault: false
    });

    useEffect(() => {
        fetchAddresses();
    }, []);

    const fetchAddresses = async () => {
        setLoading(true);
        try {
            // Mock address data - replace with actual API call
            const mockAddresses = [
                {
                    id: 1,
                    type: 'shipping',
                    firstName: 'John',
                    lastName: 'Doe',
                    company: 'DesignXcel Corp',
                    street: '123 Business Avenue, Suite 100',
                    city: 'Manila',
                    state: 'Metro Manila',
                    zipCode: '1000',
                    country: 'Philippines',
                    phone: '+63 ************',
                    isDefault: true
                },
                {
                    id: 2,
                    type: 'billing',
                    firstName: 'John',
                    lastName: 'Doe',
                    company: '',
                    street: '456 Home Street',
                    city: 'Quezon City',
                    state: 'Metro Manila',
                    zipCode: '1100',
                    country: 'Philippines',
                    phone: '+63 ************',
                    isDefault: false
                }
            ];
            setAddresses(mockAddresses);
        } catch (error) {
            console.error('Failed to fetch addresses:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            if (editingAddress) {
                // Update existing address
                setAddresses(prev => prev.map(addr => 
                    addr.id === editingAddress.id 
                        ? { ...formData, id: editingAddress.id }
                        : addr
                ));
            } else {
                // Add new address
                const newAddress = {
                    ...formData,
                    id: Date.now() // Mock ID generation
                };
                setAddresses(prev => [...prev, newAddress]);
            }

            resetForm();
        } catch (error) {
            console.error('Failed to save address:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (address) => {
        setEditingAddress(address);
        setFormData(address);
        setShowAddForm(true);
    };

    const handleDelete = async (addressId) => {
        if (window.confirm('Are you sure you want to delete this address?')) {
            setAddresses(prev => prev.filter(addr => addr.id !== addressId));
        }
    };

    const handleSetDefault = async (addressId) => {
        setAddresses(prev => prev.map(addr => ({
            ...addr,
            isDefault: addr.id === addressId
        })));
    };

    const resetForm = () => {
        setFormData({
            type: 'shipping',
            firstName: '',
            lastName: '',
            company: '',
            street: '',
            city: '',
            state: '',
            zipCode: '',
            country: 'Philippines',
            phone: '',
            isDefault: false
        });
        setEditingAddress(null);
        setShowAddForm(false);
    };

    const formatAddress = (address) => {
        const parts = [
            address.street,
            address.city,
            address.state,
            address.zipCode,
            address.country
        ].filter(Boolean);
        return parts.join(', ');
    };

    return (
        <div className="address-book">
            <div className="section-header">
                <div>
                    <h2 className="section-title">Address Book</h2>
                    <p className="section-subtitle">
                        Manage your shipping and billing addresses
                    </p>
                </div>
                <button
                    className="btn-primary"
                    onClick={() => setShowAddForm(true)}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    Add New Address
                </button>
            </div>

            {showAddForm && (
                <div className="address-form-container">
                    <div className="form-header">
                        <h3>{editingAddress ? 'Edit Address' : 'Add New Address'}</h3>
                        <button
                            className="close-btn"
                            onClick={resetForm}
                        >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>

                    <form className="address-form" onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label className="form-label">Address Type</label>
                            <select
                                name="type"
                                className="form-input"
                                value={formData.type}
                                onChange={handleChange}
                                required
                            >
                                <option value="shipping">Shipping Address</option>
                                <option value="billing">Billing Address</option>
                            </select>
                        </div>

                        <div className="form-row">
                            <div className="form-group">
                                <label className="form-label">First Name</label>
                                <input
                                    type="text"
                                    name="firstName"
                                    className="form-input"
                                    value={formData.firstName}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label className="form-label">Last Name</label>
                                <input
                                    type="text"
                                    name="lastName"
                                    className="form-input"
                                    value={formData.lastName}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                        </div>

                        <div className="form-group">
                            <label className="form-label">Company (Optional)</label>
                            <input
                                type="text"
                                name="company"
                                className="form-input"
                                value={formData.company}
                                onChange={handleChange}
                            />
                        </div>

                        <div className="form-group">
                            <label className="form-label">Street Address</label>
                            <input
                                type="text"
                                name="street"
                                className="form-input"
                                value={formData.street}
                                onChange={handleChange}
                                required
                            />
                        </div>

                        <div className="form-row">
                            <div className="form-group">
                                <label className="form-label">City</label>
                                <input
                                    type="text"
                                    name="city"
                                    className="form-input"
                                    value={formData.city}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label className="form-label">State/Province</label>
                                <input
                                    type="text"
                                    name="state"
                                    className="form-input"
                                    value={formData.state}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                        </div>

                        <div className="form-row">
                            <div className="form-group">
                                <label className="form-label">ZIP/Postal Code</label>
                                <input
                                    type="text"
                                    name="zipCode"
                                    className="form-input"
                                    value={formData.zipCode}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label className="form-label">Country</label>
                                <select
                                    name="country"
                                    className="form-input"
                                    value={formData.country}
                                    onChange={handleChange}
                                    required
                                >
                                    <option value="Philippines">Philippines</option>
                                    <option value="United States">United States</option>
                                    <option value="Canada">Canada</option>
                                    <option value="United Kingdom">United Kingdom</option>
                                    <option value="Australia">Australia</option>
                                </select>
                            </div>
                        </div>

                        <div className="form-group">
                            <label className="form-label">Phone Number</label>
                            <input
                                type="tel"
                                name="phone"
                                className="form-input"
                                value={formData.phone}
                                onChange={handleChange}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label className="checkbox-label">
                                <input
                                    type="checkbox"
                                    name="isDefault"
                                    checked={formData.isDefault}
                                    onChange={handleChange}
                                />
                                <span className="checkmark"></span>
                                Set as default address
                            </label>
                        </div>

                        <div className="form-actions">
                            <button
                                type="submit"
                                className="btn-primary"
                                disabled={loading}
                            >
                                {loading ? 'Saving...' : (editingAddress ? 'Update Address' : 'Add Address')}
                            </button>
                            <button
                                type="button"
                                className="btn-secondary"
                                onClick={resetForm}
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            )}

            <div className="addresses-list">
                {addresses.length === 0 ? (
                    <div className="empty-state">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                            <circle cx="12" cy="10" r="3"/>
                        </svg>
                        <h3>No addresses saved</h3>
                        <p>Add your first address to make checkout faster and easier.</p>
                    </div>
                ) : (
                    <div className="address-grid">
                        {addresses.map(address => (
                            <div key={address.id} className="address-card">
                                <div className="address-header">
                                    <div className="address-type">
                                        <span className={`type-badge ${address.type}`}>
                                            {address.type === 'shipping' ? (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                    <rect x="1" y="3" width="15" height="13"/>
                                                    <polygon points="16,8 20,8 23,11 23,16 16,16"/>
                                                    <circle cx="5.5" cy="18.5" r="2.5"/>
                                                    <circle cx="18.5" cy="18.5" r="2.5"/>
                                                </svg>
                                            ) : (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                                                    <line x1="1" y1="10" x2="23" y2="10"/>
                                                </svg>
                                            )}
                                            {address.type.charAt(0).toUpperCase() + address.type.slice(1)}
                                        </span>
                                    </div>
                                    {address.isDefault && (
                                        <span className="default-badge">Default</span>
                                    )}
                                </div>

                                <div className="address-content">
                                    <h4 className="address-name">
                                        {address.firstName} {address.lastName}
                                    </h4>
                                    {address.company && (
                                        <p className="address-company">{address.company}</p>
                                    )}
                                    <p className="address-text">{formatAddress(address)}</p>
                                    <p className="address-phone">{address.phone}</p>
                                </div>

                                <div className="address-actions">
                                    <button
                                        className="action-btn edit"
                                        onClick={() => handleEdit(address)}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                        </svg>
                                        Edit
                                    </button>
                                    {!address.isDefault && (
                                        <button
                                            className="action-btn default"
                                            onClick={() => handleSetDefault(address.id)}
                                        >
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                                            </svg>
                                            Set Default
                                        </button>
                                    )}
                                    <button
                                        className="action-btn delete"
                                        onClick={() => handleDelete(address.id)}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <polyline points="3,6 5,6 21,6"/>
                                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"/>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AddressBook;
