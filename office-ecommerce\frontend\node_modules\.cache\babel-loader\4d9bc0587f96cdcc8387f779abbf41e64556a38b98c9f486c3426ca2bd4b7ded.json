{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\payment\\\\PayMongoCheckout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport './PayMongoCheckout.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PayMongoCheckout = ({\n  orderData,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    clearCart\n  } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [{\n    id: 'card',\n    name: 'Credit/Debit Card',\n    icon: '💳',\n    description: 'Visa, Mastercard, JCB, American Express',\n    fee: '3.5% + ₱15'\n  }, {\n    id: 'gcash',\n    name: 'GCash',\n    icon: '📱',\n    description: 'Pay using your GCash wallet',\n    fee: '2.5%'\n  }, {\n    id: 'grabpay',\n    name: 'GrabPay',\n    icon: '🚗',\n    description: 'Pay using your GrabPay wallet',\n    fee: '2.5%'\n  }, {\n    id: 'bank',\n    name: 'Online Banking',\n    icon: '🏦',\n    description: 'Direct bank transfer',\n    fee: '1.5%'\n  }];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData !== null && orderData !== void 0 && orderData.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount, selectedMethod]);\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      var _orderData$customer, _orderData$shippingAd, _orderData$shippingAd2, _orderData$customer2, _orderData$shippingAd3;\n      // Prepare order data for PayMongo\n      const paymentData = {\n        orderId: orderData.orderId || `ORDER_${Date.now()}`,\n        totalAmount: Math.round(orderData.totalAmount * 100),\n        // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: ((_orderData$customer = orderData.customer) === null || _orderData$customer === void 0 ? void 0 : _orderData$customer.name) || ((_orderData$shippingAd = orderData.shippingAddress) === null || _orderData$shippingAd === void 0 ? void 0 : _orderData$shippingAd.firstName) + ' ' + ((_orderData$shippingAd2 = orderData.shippingAddress) === null || _orderData$shippingAd2 === void 0 ? void 0 : _orderData$shippingAd2.lastName),\n          email: ((_orderData$customer2 = orderData.customer) === null || _orderData$customer2 === void 0 ? void 0 : _orderData$customer2.email) || ((_orderData$shippingAd3 = orderData.shippingAddress) === null || _orderData$shippingAd3 === void 0 ? void 0 : _orderData$shippingAd3.email)\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          ...orderData.metadata\n        }\n      };\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(paymentData.orderId, result.paymentLink.id);\n        if (onSuccess) {\n          onSuccess(result);\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('Payment link creation error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data,\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status\n      }); // Debug log\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        if (statusResult.success) {\n          const status = statusResult.data.status;\n          if (status === 'paid') {\n            var _orderData$metadata;\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: ((_orderData$metadata = orderData.metadata) === null || _orderData$metadata === void 0 ? void 0 : _orderData$metadata.currency) || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n      }\n    }, 5000); // Poll every 5 seconds\n\n    // Stop polling after 10 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  };\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paymongo-checkout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Complete Your Payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Secure payment powered by PayMongo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Order Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subtotal:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatCurrency(orderData.totalAmount || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), fees && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Payment Fee:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row total\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Amount:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.amount + fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-methods\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Select Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"method-grid\",\n        children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `payment-method ${selectedMethod === method.id ? 'selected' : ''}`,\n          onClick: () => setSelectedMethod(method.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-icon\",\n            children: method.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: method.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-description\",\n              children: method.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-fee\",\n              children: [\"Fee: \", method.fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, method.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: handleCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleCreatePaymentLink,\n        disabled: loading || !(orderData !== null && orderData !== void 0 && orderData.totalAmount),\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), \"Creating Payment Link...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"payment-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), \"Pay \", fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), paymentLink && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-link-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Payment link created successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-link-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Reference:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reference-number\",\n            children: paymentLink.reference\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            children: paymentLink.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"security-icon\",\n          children: \"\\uD83D\\uDD10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Secure Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n};\n_s(PayMongoCheckout, \"86+uGYKNFyWUeaqIj86xKtQyg4w=\", false, function () {\n  return [useNavigate, useCart];\n});\n_c = PayMongoCheckout;\nexport default PayMongoCheckout;\nvar _c;\n$RefreshReg$(_c, \"PayMongoCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCart", "paymentService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PayMongoCheckout", "orderData", "onSuccess", "onError", "onCancel", "_s", "navigate", "clearCart", "loading", "setLoading", "paymentLink", "setPaymentLink", "error", "setError", "fees", "setFees", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "paymentMethods", "id", "name", "icon", "description", "fee", "totalAmount", "calculatePaymentFees", "amountInCentavos", "Math", "round", "result", "calculateFees", "success", "data", "console", "handleCreatePaymentLink", "_orderData$customer", "_orderData$shippingAd", "_orderData$shippingAd2", "_orderData$customer2", "_orderData$shippingAd3", "paymentData", "orderId", "Date", "now", "items", "customer", "shippingAddress", "firstName", "lastName", "email", "metadata", "paymentMethod", "source", "createPaymentLink", "url", "window", "open", "startPaymentStatusPolling", "Error", "_error$response", "_error$response2", "message", "response", "status", "paymentLinkId", "pollInterval", "setInterval", "statusResult", "getPaymentStatus", "_orderData$metadata", "clearInterval", "state", "order", "order_number", "total_amount", "currency", "created_at", "toISOString", "shipping_address", "paymentStatus", "setTimeout", "handleCancel", "cancelPaymentLink", "catch", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalFee", "map", "method", "onClick", "disabled", "reference", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/payment/PayMongoCheckout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport './PayMongoCheckout.css';\n\nconst PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const { clearCart } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      icon: '💳',\n      description: 'Visa, Mastercard, JCB, American Express',\n      fee: '3.5% + ₱15'\n    },\n    {\n      id: 'gcash',\n      name: 'GCash',\n      icon: '📱',\n      description: 'Pay using your GCash wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'grabpay',\n      name: 'GrabPay',\n      icon: '🚗',\n      description: 'Pay using your GrabPay wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'bank',\n      name: 'Online Banking',\n      icon: '🏦',\n      description: 'Direct bank transfer',\n      fee: '1.5%'\n    }\n  ];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData?.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData?.totalAmount, selectedMethod]);\n\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // Prepare order data for PayMongo\n      const paymentData = {\n        orderId: orderData.orderId || `ORDER_${Date.now()}`,\n        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: orderData.customer?.name || orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n          email: orderData.customer?.email || orderData.shippingAddress?.email\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          ...orderData.metadata\n        }\n      };\n\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(paymentData.orderId, result.paymentLink.id);\n\n        if (onSuccess) {\n          onSuccess(result);\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: error.response?.data,\n        status: error.response?.status\n      }); // Debug log\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        \n        if (statusResult.success) {\n          const status = statusResult.data.status;\n          \n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: orderData.metadata?.currency || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n      }\n    }, 5000); // Poll every 5 seconds\n\n    // Stop polling after 10 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  };\n\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paymongo-checkout\">\n      <div className=\"checkout-header\">\n        <h2>Complete Your Payment</h2>\n        <p>Secure payment powered by PayMongo</p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div className=\"summary-row\">\n          <span>Subtotal:</span>\n          <span>{formatCurrency(orderData.totalAmount || 0)}</span>\n        </div>\n        {fees && (\n          <>\n            <div className=\"summary-row\">\n              <span>Payment Fee:</span>\n              <span>{formatCurrency(fees.totalFee)}</span>\n            </div>\n            <div className=\"summary-row total\">\n              <span>Total Amount:</span>\n              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"payment-methods\">\n        <h3>Select Payment Method</h3>\n        <div className=\"method-grid\">\n          {paymentMethods.map((method) => (\n            <div\n              key={method.id}\n              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}\n              onClick={() => setSelectedMethod(method.id)}\n            >\n              <div className=\"method-icon\">{method.icon}</div>\n              <div className=\"method-info\">\n                <div className=\"method-name\">{method.name}</div>\n                <div className=\"method-description\">{method.description}</div>\n                <div className=\"method-fee\">Fee: {method.fee}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* Payment Actions */}\n      <div className=\"payment-actions\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={handleCancel}\n          disabled={loading}\n        >\n          Cancel\n        </button>\n        <button\n          className=\"btn btn-primary\"\n          onClick={handleCreatePaymentLink}\n          disabled={loading || !orderData?.totalAmount}\n        >\n          {loading ? (\n            <>\n              <span className=\"loading-spinner\"></span>\n              Creating Payment Link...\n            </>\n          ) : (\n            <>\n              <span className=\"payment-icon\">🔒</span>\n              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Payment Link Status */}\n      {paymentLink && (\n        <div className=\"payment-link-status\">\n          <div className=\"status-header\">\n            <span className=\"status-icon\">✅</span>\n            <span>Payment link created successfully!</span>\n          </div>\n          <p>\n            A new tab has opened with your secure payment page. \n            Complete your payment there and return here to see the confirmation.\n          </p>\n          <div className=\"payment-link-info\">\n            <div className=\"info-row\">\n              <span>Reference:</span>\n              <span className=\"reference-number\">{paymentLink.reference}</span>\n            </div>\n            <div className=\"info-row\">\n              <span>Status:</span>\n              <span className=\"status-badge\">{paymentLink.status}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Security Notice */}\n      <div className=\"security-notice\">\n        <div className=\"security-header\">\n          <span className=\"security-icon\">🔐</span>\n          <span>Secure Payment</span>\n        </div>\n        <p>\n          Your payment information is encrypted and secure. \n          PayMongo is PCI DSS compliant and follows international security standards.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default PayMongoCheckout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAU,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,MAAM,CAAC;;EAE5D;EACA,MAAM2B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,yCAAyC;IACtDC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,6BAA6B;IAC1CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sBAAsB;IACnCC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIS,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,EAAE;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,EAAER,cAAc,CAAC,CAAC;EAE5C,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAClE,MAAMK,MAAM,GAAG,MAAMlC,cAAc,CAACmC,aAAa,CAACJ,gBAAgB,EAAEV,cAAc,CAAC;MACnF,IAAIa,MAAM,CAACE,OAAO,EAAE;QAClBhB,OAAO,CAACc,MAAM,CAACG,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMsB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CzB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAsB,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,WAAW,GAAG;QAClBC,OAAO,EAAExC,SAAS,CAACwC,OAAO,IAAI,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACnDnB,WAAW,EAAEG,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC;QAAE;QACtDoB,KAAK,EAAE3C,SAAS,CAAC2C,KAAK,IAAI,EAAE;QAC5BC,QAAQ,EAAE;UACRzB,IAAI,EAAE,EAAAe,mBAAA,GAAAlC,SAAS,CAAC4C,QAAQ,cAAAV,mBAAA,uBAAlBA,mBAAA,CAAoBf,IAAI,KAAI,EAAAgB,qBAAA,GAAAnC,SAAS,CAAC6C,eAAe,cAAAV,qBAAA,uBAAzBA,qBAAA,CAA2BW,SAAS,IAAG,GAAG,KAAAV,sBAAA,GAAGpC,SAAS,CAAC6C,eAAe,cAAAT,sBAAA,uBAAzBA,sBAAA,CAA2BW,QAAQ;UAClHC,KAAK,EAAE,EAAAX,oBAAA,GAAArC,SAAS,CAAC4C,QAAQ,cAAAP,oBAAA,uBAAlBA,oBAAA,CAAoBW,KAAK,OAAAV,sBAAA,GAAItC,SAAS,CAAC6C,eAAe,cAAAP,sBAAA,uBAAzBA,sBAAA,CAA2BU,KAAK;QACtE,CAAC;QACDH,eAAe,EAAE7C,SAAS,CAAC6C,eAAe,IAAI,CAAC,CAAC;QAChDI,QAAQ,EAAE;UACRC,aAAa,EAAEnC,cAAc;UAC7BoC,MAAM,EAAE,qBAAqB;UAC7B,GAAGnD,SAAS,CAACiD;QACf;MACF,CAAC;MAED,MAAMrB,MAAM,GAAG,MAAMlC,cAAc,CAAC0D,iBAAiB,CAACb,WAAW,CAAC;;MAElE;MACA,IAAIX,MAAM,IAAIA,MAAM,CAACnB,WAAW,IAAImB,MAAM,CAACnB,WAAW,CAAC4C,GAAG,EAAE;QAC1D3C,cAAc,CAACkB,MAAM,CAACnB,WAAW,CAAC;;QAElC;QACA6C,MAAM,CAACC,IAAI,CAAC3B,MAAM,CAACnB,WAAW,CAAC4C,GAAG,EAAE,QAAQ,CAAC;;QAE7C;QACAG,yBAAyB,CAACjB,WAAW,CAACC,OAAO,EAAEZ,MAAM,CAACnB,WAAW,CAACS,EAAE,CAAC;QAErE,IAAIjB,SAAS,EAAE;UACbA,SAAS,CAAC2B,MAAM,CAAC;QACnB;MACF,CAAC,MAAM;QACL,MAAM,IAAI6B,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MAAA,IAAA+C,eAAA,EAAAC,gBAAA;MACd3B,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDqB,OAAO,CAACrB,KAAK,CAAC,gBAAgB,EAAE;QAC9BiD,OAAO,EAAEjD,KAAK,CAACiD,OAAO;QACtBC,QAAQ,GAAAH,eAAA,GAAE/C,KAAK,CAACkD,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAgB3B,IAAI;QAC9B+B,MAAM,GAAAH,gBAAA,GAAEhD,KAAK,CAACkD,QAAQ,cAAAF,gBAAA,uBAAdA,gBAAA,CAAgBG;MAC1B,CAAC,CAAC,CAAC,CAAC;MACJlD,QAAQ,CAACD,KAAK,CAACiD,OAAO,IAAI,+BAA+B,CAAC;MAC1D,IAAI1D,OAAO,EAAE;QACXA,OAAO,CAACS,KAAK,CAAC;MAChB;IACF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgD,yBAAyB,GAAGA,CAAChB,OAAO,EAAEuB,aAAa,KAAK;IAC5D,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMxE,cAAc,CAACyE,gBAAgB,CAAC3B,OAAO,EAAEuB,aAAa,CAAC;QAElF,IAAIG,YAAY,CAACpC,OAAO,EAAE;UACxB,MAAMgC,MAAM,GAAGI,YAAY,CAACnC,IAAI,CAAC+B,MAAM;UAEvC,IAAIA,MAAM,KAAK,MAAM,EAAE;YAAA,IAAAM,mBAAA;YACrBC,aAAa,CAACL,YAAY,CAAC;;YAE3B;YACA1D,SAAS,CAAC,CAAC;;YAEX;YACAD,QAAQ,CAAC,gBAAgB,EAAE;cACzBiE,KAAK,EAAE;gBACLC,KAAK,EAAE;kBACLrD,EAAE,EAAEsB,OAAO;kBACXgC,YAAY,EAAE,OAAOhC,OAAO,EAAE;kBAC9BiC,YAAY,EAAEzE,SAAS,CAACuB,WAAW;kBACnCmD,QAAQ,EAAE,EAAAN,mBAAA,GAAApE,SAAS,CAACiD,QAAQ,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBM,QAAQ,KAAI,KAAK;kBAC/CZ,MAAM,EAAE,MAAM;kBACda,UAAU,EAAE,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC;kBACpCC,gBAAgB,EAAE7E,SAAS,CAAC6C,eAAe;kBAC3CF,KAAK,EAAE3C,SAAS,CAAC2C;gBACnB,CAAC;gBACDiB,OAAO,EAAE,+CAA+C;gBACxDkB,aAAa,EAAE,WAAW;gBAC1B5B,aAAa,EAAE;cACjB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIY,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,SAAS,EAAE;YAChFO,aAAa,CAACL,YAAY,CAAC;YAC3BpD,QAAQ,CAAC,WAAWkD,MAAM,qBAAqB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAOnD,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACAoE,UAAU,CAAC,MAAM;MACfV,aAAa,CAACL,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvE,WAAW,EAAE;MACff,cAAc,CAACuF,iBAAiB,CAACxE,WAAW,CAACS,EAAE,CAAC,CAACgE,KAAK,CAAClD,OAAO,CAACrB,KAAK,CAAC;IACvE;IACA,IAAIR,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMgF,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBb,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACc,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACExF,OAAA;IAAK6F,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC9F,OAAA;MAAK6F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9F,OAAA;QAAA8F,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BlG,OAAA;QAAA8F,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9F,OAAA;QAAA8F,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBlG,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9F,OAAA;UAAA8F,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtBlG,OAAA;UAAA8F,QAAA,EAAOP,cAAc,CAACnF,SAAS,CAACuB,WAAW,IAAI,CAAC;QAAC;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACLjF,IAAI,iBACHjB,OAAA,CAAAE,SAAA;QAAA4F,QAAA,gBACE9F,OAAA;UAAK6F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9F,OAAA;YAAA8F,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBlG,OAAA;YAAA8F,QAAA,EAAOP,cAAc,CAACtE,IAAI,CAACkF,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9F,OAAA;YAAA8F,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BlG,OAAA;YAAA8F,QAAA,EAAOP,cAAc,CAACtE,IAAI,CAACuE,MAAM,GAAGvE,IAAI,CAACkF,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9F,OAAA;QAAA8F,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BlG,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBzE,cAAc,CAAC+E,GAAG,CAAEC,MAAM,iBACzBrG,OAAA;UAEE6F,SAAS,EAAE,kBAAkB1E,cAAc,KAAKkF,MAAM,CAAC/E,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9EgF,OAAO,EAAEA,CAAA,KAAMlF,iBAAiB,CAACiF,MAAM,CAAC/E,EAAE,CAAE;UAAAwE,QAAA,gBAE5C9F,OAAA;YAAK6F,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEO,MAAM,CAAC7E;UAAI;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDlG,OAAA;YAAK6F,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9F,OAAA;cAAK6F,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,MAAM,CAAC9E;YAAI;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDlG,OAAA;cAAK6F,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEO,MAAM,CAAC5E;YAAW;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DlG,OAAA;cAAK6F,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,OAAK,EAACO,MAAM,CAAC3E,GAAG;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GATDG,MAAM,CAAC/E,EAAE;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnF,KAAK,iBACJf,OAAA;MAAK6F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9F,OAAA;QAAM6F,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtClG,OAAA;QAAA8F,QAAA,EAAO/E;MAAK;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDlG,OAAA;MAAK6F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9F,OAAA;QACE6F,SAAS,EAAC,mBAAmB;QAC7BS,OAAO,EAAElB,YAAa;QACtBmB,QAAQ,EAAE5F,OAAQ;QAAAmF,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlG,OAAA;QACE6F,SAAS,EAAC,iBAAiB;QAC3BS,OAAO,EAAEjE,uBAAwB;QACjCkE,QAAQ,EAAE5F,OAAO,IAAI,EAACP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,CAAC;QAAAmE,QAAA,EAE5CnF,OAAO,gBACNX,OAAA,CAAAE,SAAA;UAAA4F,QAAA,gBACE9F,OAAA;YAAM6F,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA,eAAE,CAAC,gBAEHlG,OAAA,CAAAE,SAAA;UAAA4F,QAAA,gBACE9F,OAAA;YAAM6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,QACpC,EAACjF,IAAI,GAAGsE,cAAc,CAACtE,IAAI,CAACuE,MAAM,GAAGvE,IAAI,CAACkF,QAAQ,CAAC,GAAGZ,cAAc,CAACnF,SAAS,CAACuB,WAAW,IAAI,CAAC,CAAC;QAAA,eACpG;MACH;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLrF,WAAW,iBACVb,OAAA;MAAK6F,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC9F,OAAA;QAAK6F,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9F,OAAA;UAAM6F,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtClG,OAAA;UAAA8F,QAAA,EAAM;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNlG,OAAA;QAAA8F,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlG,OAAA;QAAK6F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9F,OAAA;UAAK6F,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9F,OAAA;YAAA8F,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvBlG,OAAA;YAAM6F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEjF,WAAW,CAAC2F;UAAS;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9F,OAAA;YAAA8F,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpBlG,OAAA;YAAM6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEjF,WAAW,CAACqD;UAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlG,OAAA;MAAK6F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9F,OAAA;QAAK6F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9F,OAAA;UAAM6F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzClG,OAAA;UAAA8F,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNlG,OAAA;QAAA8F,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CA9SIL,gBAAgB;EAAA,QACHP,WAAW,EACNC,OAAO;AAAA;AAAA4G,EAAA,GAFzBtG,gBAAgB;AAgTtB,eAAeA,gBAAgB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}