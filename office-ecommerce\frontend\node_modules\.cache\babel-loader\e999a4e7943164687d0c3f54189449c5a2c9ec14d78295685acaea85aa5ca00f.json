{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\payment\\\\PayMongoCheckout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport './PayMongoCheckout.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PayMongoCheckout = ({\n  orderData,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    clearCart\n  } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [{\n    id: 'card',\n    name: 'Credit/Debit Card',\n    icon: '💳',\n    description: 'Visa, Mastercard, JCB, American Express',\n    fee: '3.5% + ₱15'\n  }, {\n    id: 'gcash',\n    name: 'GCash',\n    icon: '📱',\n    description: 'Pay using your GCash wallet',\n    fee: '2.5%'\n  }, {\n    id: 'grabpay',\n    name: 'GrabPay',\n    icon: '🚗',\n    description: 'Pay using your GrabPay wallet',\n    fee: '2.5%'\n  }, {\n    id: 'bank',\n    name: 'Online Banking',\n    icon: '🏦',\n    description: 'Direct bank transfer',\n    fee: '1.5%'\n  }];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData !== null && orderData !== void 0 && orderData.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount, selectedMethod]);\n  const calculatePaymentFees = async () => {\n    try {\n      const result = await paymentService.calculateFees(orderData.totalAmount, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      var _orderData$customer, _orderData$shippingAd, _orderData$shippingAd2, _orderData$customer2, _orderData$shippingAd3;\n      // Prepare order data for PayMongo\n      const paymentData = {\n        orderId: orderData.orderId || `ORDER_${Date.now()}`,\n        totalAmount: Math.round(orderData.totalAmount * 100),\n        // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: ((_orderData$customer = orderData.customer) === null || _orderData$customer === void 0 ? void 0 : _orderData$customer.name) || ((_orderData$shippingAd = orderData.shippingAddress) === null || _orderData$shippingAd === void 0 ? void 0 : _orderData$shippingAd.firstName) + ' ' + ((_orderData$shippingAd2 = orderData.shippingAddress) === null || _orderData$shippingAd2 === void 0 ? void 0 : _orderData$shippingAd2.lastName),\n          email: ((_orderData$customer2 = orderData.customer) === null || _orderData$customer2 === void 0 ? void 0 : _orderData$customer2.email) || ((_orderData$shippingAd3 = orderData.shippingAddress) === null || _orderData$shippingAd3 === void 0 ? void 0 : _orderData$shippingAd3.email)\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          ...orderData.metadata\n        }\n      };\n      const result = await paymentService.createPaymentLink(paymentData);\n      if (result.success) {\n        setPaymentLink(result.data.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.data.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(paymentData.orderId, result.data.paymentLink.id);\n        if (onSuccess) {\n          onSuccess(result.data);\n        }\n      } else {\n        throw new Error(result.message || 'Failed to create payment link');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        if (statusResult.success) {\n          const status = statusResult.data.status;\n          if (status === 'paid') {\n            var _orderData$metadata;\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: ((_orderData$metadata = orderData.metadata) === null || _orderData$metadata === void 0 ? void 0 : _orderData$metadata.currency) || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n      }\n    }, 5000); // Poll every 5 seconds\n\n    // Stop polling after 10 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  };\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paymongo-checkout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Complete Your Payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Secure payment powered by PayMongo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Order Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subtotal:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatCurrency(orderData.totalAmount || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), fees && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Payment Fee:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row total\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Amount:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.amount + fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-methods\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Select Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"method-grid\",\n        children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `payment-method ${selectedMethod === method.id ? 'selected' : ''}`,\n          onClick: () => setSelectedMethod(method.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-icon\",\n            children: method.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: method.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-description\",\n              children: method.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-fee\",\n              children: [\"Fee: \", method.fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, method.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: handleCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleCreatePaymentLink,\n        disabled: loading || !(orderData !== null && orderData !== void 0 && orderData.totalAmount),\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), \"Creating Payment Link...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"payment-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), \"Pay \", fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), paymentLink && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-link-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Payment link created successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-link-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Reference:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reference-number\",\n            children: paymentLink.reference\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            children: paymentLink.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"security-icon\",\n          children: \"\\uD83D\\uDD10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Secure Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(PayMongoCheckout, \"86+uGYKNFyWUeaqIj86xKtQyg4w=\", false, function () {\n  return [useNavigate, useCart];\n});\n_c = PayMongoCheckout;\nexport default PayMongoCheckout;\nvar _c;\n$RefreshReg$(_c, \"PayMongoCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCart", "paymentService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PayMongoCheckout", "orderData", "onSuccess", "onError", "onCancel", "_s", "navigate", "clearCart", "loading", "setLoading", "paymentLink", "setPaymentLink", "error", "setError", "fees", "setFees", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "paymentMethods", "id", "name", "icon", "description", "fee", "totalAmount", "calculatePaymentFees", "result", "calculateFees", "success", "data", "console", "handleCreatePaymentLink", "_orderData$customer", "_orderData$shippingAd", "_orderData$shippingAd2", "_orderData$customer2", "_orderData$shippingAd3", "paymentData", "orderId", "Date", "now", "Math", "round", "items", "customer", "shippingAddress", "firstName", "lastName", "email", "metadata", "paymentMethod", "source", "createPaymentLink", "window", "open", "url", "startPaymentStatusPolling", "Error", "message", "paymentLinkId", "pollInterval", "setInterval", "statusResult", "getPaymentStatus", "status", "_orderData$metadata", "clearInterval", "state", "order", "order_number", "total_amount", "currency", "created_at", "toISOString", "shipping_address", "paymentStatus", "setTimeout", "handleCancel", "cancelPaymentLink", "catch", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalFee", "map", "method", "onClick", "disabled", "reference", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/payment/PayMongoCheckout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport './PayMongoCheckout.css';\n\nconst PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const { clearCart } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      icon: '💳',\n      description: 'Visa, Mastercard, JCB, American Express',\n      fee: '3.5% + ₱15'\n    },\n    {\n      id: 'gcash',\n      name: 'GCash',\n      icon: '📱',\n      description: 'Pay using your GCash wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'grabpay',\n      name: 'GrabPay',\n      icon: '🚗',\n      description: 'Pay using your GrabPay wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'bank',\n      name: 'Online Banking',\n      icon: '🏦',\n      description: 'Direct bank transfer',\n      fee: '1.5%'\n    }\n  ];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData?.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData?.totalAmount, selectedMethod]);\n\n  const calculatePaymentFees = async () => {\n    try {\n      const result = await paymentService.calculateFees(orderData.totalAmount, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // Prepare order data for PayMongo\n      const paymentData = {\n        orderId: orderData.orderId || `ORDER_${Date.now()}`,\n        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: orderData.customer?.name || orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n          email: orderData.customer?.email || orderData.shippingAddress?.email\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          ...orderData.metadata\n        }\n      };\n\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      if (result.success) {\n        setPaymentLink(result.data.paymentLink);\n        \n        // Redirect to PayMongo checkout\n        window.open(result.data.paymentLink.url, '_blank');\n        \n        // Start polling for payment status\n        startPaymentStatusPolling(paymentData.orderId, result.data.paymentLink.id);\n        \n        if (onSuccess) {\n          onSuccess(result.data);\n        }\n      } else {\n        throw new Error(result.message || 'Failed to create payment link');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        \n        if (statusResult.success) {\n          const status = statusResult.data.status;\n          \n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: orderData.metadata?.currency || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n      }\n    }, 5000); // Poll every 5 seconds\n\n    // Stop polling after 10 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  };\n\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paymongo-checkout\">\n      <div className=\"checkout-header\">\n        <h2>Complete Your Payment</h2>\n        <p>Secure payment powered by PayMongo</p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div className=\"summary-row\">\n          <span>Subtotal:</span>\n          <span>{formatCurrency(orderData.totalAmount || 0)}</span>\n        </div>\n        {fees && (\n          <>\n            <div className=\"summary-row\">\n              <span>Payment Fee:</span>\n              <span>{formatCurrency(fees.totalFee)}</span>\n            </div>\n            <div className=\"summary-row total\">\n              <span>Total Amount:</span>\n              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"payment-methods\">\n        <h3>Select Payment Method</h3>\n        <div className=\"method-grid\">\n          {paymentMethods.map((method) => (\n            <div\n              key={method.id}\n              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}\n              onClick={() => setSelectedMethod(method.id)}\n            >\n              <div className=\"method-icon\">{method.icon}</div>\n              <div className=\"method-info\">\n                <div className=\"method-name\">{method.name}</div>\n                <div className=\"method-description\">{method.description}</div>\n                <div className=\"method-fee\">Fee: {method.fee}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* Payment Actions */}\n      <div className=\"payment-actions\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={handleCancel}\n          disabled={loading}\n        >\n          Cancel\n        </button>\n        <button\n          className=\"btn btn-primary\"\n          onClick={handleCreatePaymentLink}\n          disabled={loading || !orderData?.totalAmount}\n        >\n          {loading ? (\n            <>\n              <span className=\"loading-spinner\"></span>\n              Creating Payment Link...\n            </>\n          ) : (\n            <>\n              <span className=\"payment-icon\">🔒</span>\n              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Payment Link Status */}\n      {paymentLink && (\n        <div className=\"payment-link-status\">\n          <div className=\"status-header\">\n            <span className=\"status-icon\">✅</span>\n            <span>Payment link created successfully!</span>\n          </div>\n          <p>\n            A new tab has opened with your secure payment page. \n            Complete your payment there and return here to see the confirmation.\n          </p>\n          <div className=\"payment-link-info\">\n            <div className=\"info-row\">\n              <span>Reference:</span>\n              <span className=\"reference-number\">{paymentLink.reference}</span>\n            </div>\n            <div className=\"info-row\">\n              <span>Status:</span>\n              <span className=\"status-badge\">{paymentLink.status}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Security Notice */}\n      <div className=\"security-notice\">\n        <div className=\"security-header\">\n          <span className=\"security-icon\">🔐</span>\n          <span>Secure Payment</span>\n        </div>\n        <p>\n          Your payment information is encrypted and secure. \n          PayMongo is PCI DSS compliant and follows international security standards.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default PayMongoCheckout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAU,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,MAAM,CAAC;;EAE5D;EACA,MAAM2B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,yCAAyC;IACtDC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,6BAA6B;IAC1CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sBAAsB;IACnCC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIS,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,EAAE;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,EAAER,cAAc,CAAC,CAAC;EAE5C,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM/B,cAAc,CAACgC,aAAa,CAAC1B,SAAS,CAACuB,WAAW,EAAER,cAAc,CAAC;MACxF,IAAIU,MAAM,CAACE,OAAO,EAAE;QAClBb,OAAO,CAACW,MAAM,CAACG,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMmB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CtB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAmB,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,WAAW,GAAG;QAClBC,OAAO,EAAErC,SAAS,CAACqC,OAAO,IAAI,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACnDhB,WAAW,EAAEiB,IAAI,CAACC,KAAK,CAACzC,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC;QAAE;QACtDmB,KAAK,EAAE1C,SAAS,CAAC0C,KAAK,IAAI,EAAE;QAC5BC,QAAQ,EAAE;UACRxB,IAAI,EAAE,EAAAY,mBAAA,GAAA/B,SAAS,CAAC2C,QAAQ,cAAAZ,mBAAA,uBAAlBA,mBAAA,CAAoBZ,IAAI,KAAI,EAAAa,qBAAA,GAAAhC,SAAS,CAAC4C,eAAe,cAAAZ,qBAAA,uBAAzBA,qBAAA,CAA2Ba,SAAS,IAAG,GAAG,KAAAZ,sBAAA,GAAGjC,SAAS,CAAC4C,eAAe,cAAAX,sBAAA,uBAAzBA,sBAAA,CAA2Ba,QAAQ;UAClHC,KAAK,EAAE,EAAAb,oBAAA,GAAAlC,SAAS,CAAC2C,QAAQ,cAAAT,oBAAA,uBAAlBA,oBAAA,CAAoBa,KAAK,OAAAZ,sBAAA,GAAInC,SAAS,CAAC4C,eAAe,cAAAT,sBAAA,uBAAzBA,sBAAA,CAA2BY,KAAK;QACtE,CAAC;QACDH,eAAe,EAAE5C,SAAS,CAAC4C,eAAe,IAAI,CAAC,CAAC;QAChDI,QAAQ,EAAE;UACRC,aAAa,EAAElC,cAAc;UAC7BmC,MAAM,EAAE,qBAAqB;UAC7B,GAAGlD,SAAS,CAACgD;QACf;MACF,CAAC;MAED,MAAMvB,MAAM,GAAG,MAAM/B,cAAc,CAACyD,iBAAiB,CAACf,WAAW,CAAC;MAElE,IAAIX,MAAM,CAACE,OAAO,EAAE;QAClBjB,cAAc,CAACe,MAAM,CAACG,IAAI,CAACnB,WAAW,CAAC;;QAEvC;QACA2C,MAAM,CAACC,IAAI,CAAC5B,MAAM,CAACG,IAAI,CAACnB,WAAW,CAAC6C,GAAG,EAAE,QAAQ,CAAC;;QAElD;QACAC,yBAAyB,CAACnB,WAAW,CAACC,OAAO,EAAEZ,MAAM,CAACG,IAAI,CAACnB,WAAW,CAACS,EAAE,CAAC;QAE1E,IAAIjB,SAAS,EAAE;UACbA,SAAS,CAACwB,MAAM,CAACG,IAAI,CAAC;QACxB;MACF,CAAC,MAAM;QACL,MAAM,IAAI4B,KAAK,CAAC/B,MAAM,CAACgC,OAAO,IAAI,+BAA+B,CAAC;MACpE;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAACD,KAAK,CAAC8C,OAAO,IAAI,+BAA+B,CAAC;MAC1D,IAAIvD,OAAO,EAAE;QACXA,OAAO,CAACS,KAAK,CAAC;MAChB;IACF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,yBAAyB,GAAGA,CAAClB,OAAO,EAAEqB,aAAa,KAAK;IAC5D,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMnE,cAAc,CAACoE,gBAAgB,CAACzB,OAAO,EAAEqB,aAAa,CAAC;QAElF,IAAIG,YAAY,CAAClC,OAAO,EAAE;UACxB,MAAMoC,MAAM,GAAGF,YAAY,CAACjC,IAAI,CAACmC,MAAM;UAEvC,IAAIA,MAAM,KAAK,MAAM,EAAE;YAAA,IAAAC,mBAAA;YACrBC,aAAa,CAACN,YAAY,CAAC;;YAE3B;YACArD,SAAS,CAAC,CAAC;;YAEX;YACAD,QAAQ,CAAC,gBAAgB,EAAE;cACzB6D,KAAK,EAAE;gBACLC,KAAK,EAAE;kBACLjD,EAAE,EAAEmB,OAAO;kBACX+B,YAAY,EAAE,OAAO/B,OAAO,EAAE;kBAC9BgC,YAAY,EAAErE,SAAS,CAACuB,WAAW;kBACnC+C,QAAQ,EAAE,EAAAN,mBAAA,GAAAhE,SAAS,CAACgD,QAAQ,cAAAgB,mBAAA,uBAAlBA,mBAAA,CAAoBM,QAAQ,KAAI,KAAK;kBAC/CP,MAAM,EAAE,MAAM;kBACdQ,UAAU,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC;kBACpCC,gBAAgB,EAAEzE,SAAS,CAAC4C,eAAe;kBAC3CF,KAAK,EAAE1C,SAAS,CAAC0C;gBACnB,CAAC;gBACDe,OAAO,EAAE,+CAA+C;gBACxDiB,aAAa,EAAE,WAAW;gBAC1BzB,aAAa,EAAE;cACjB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIc,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,SAAS,EAAE;YAChFE,aAAa,CAACN,YAAY,CAAC;YAC3B/C,QAAQ,CAAC,WAAWmD,MAAM,qBAAqB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;QACdkB,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACAgE,UAAU,CAAC,MAAM;MACfV,aAAa,CAACN,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInE,WAAW,EAAE;MACff,cAAc,CAACmF,iBAAiB,CAACpE,WAAW,CAACS,EAAE,CAAC,CAAC4D,KAAK,CAACjD,OAAO,CAAClB,KAAK,CAAC;IACvE;IACA,IAAIR,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAM4E,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBb,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACc,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACEpF,OAAA;IAAKyF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC1F,OAAA;MAAKyF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1F,OAAA;QAAA0F,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B9F,OAAA;QAAA0F,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGN9F,OAAA;MAAKyF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1F,OAAA;QAAA0F,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB9F,OAAA;QAAKyF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1F,OAAA;UAAA0F,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtB9F,OAAA;UAAA0F,QAAA,EAAOP,cAAc,CAAC/E,SAAS,CAACuB,WAAW,IAAI,CAAC;QAAC;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACL7E,IAAI,iBACHjB,OAAA,CAAAE,SAAA;QAAAwF,QAAA,gBACE1F,OAAA;UAAKyF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1F,OAAA;YAAA0F,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB9F,OAAA;YAAA0F,QAAA,EAAOP,cAAc,CAAClE,IAAI,CAAC8E,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN9F,OAAA;UAAKyF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1F,OAAA;YAAA0F,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1B9F,OAAA;YAAA0F,QAAA,EAAOP,cAAc,CAAClE,IAAI,CAACmE,MAAM,GAAGnE,IAAI,CAAC8E,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN9F,OAAA;MAAKyF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1F,OAAA;QAAA0F,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B9F,OAAA;QAAKyF,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBrE,cAAc,CAAC2E,GAAG,CAAEC,MAAM,iBACzBjG,OAAA;UAEEyF,SAAS,EAAE,kBAAkBtE,cAAc,KAAK8E,MAAM,CAAC3E,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9E4E,OAAO,EAAEA,CAAA,KAAM9E,iBAAiB,CAAC6E,MAAM,CAAC3E,EAAE,CAAE;UAAAoE,QAAA,gBAE5C1F,OAAA;YAAKyF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEO,MAAM,CAACzE;UAAI;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChD9F,OAAA;YAAKyF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B1F,OAAA;cAAKyF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,MAAM,CAAC1E;YAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD9F,OAAA;cAAKyF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEO,MAAM,CAACxE;YAAW;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D9F,OAAA;cAAKyF,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,OAAK,EAACO,MAAM,CAACvE,GAAG;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GATDG,MAAM,CAAC3E,EAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/E,KAAK,iBACJf,OAAA;MAAKyF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1F,OAAA;QAAMyF,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtC9F,OAAA;QAAA0F,QAAA,EAAO3E;MAAK;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD9F,OAAA;MAAKyF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1F,OAAA;QACEyF,SAAS,EAAC,mBAAmB;QAC7BS,OAAO,EAAElB,YAAa;QACtBmB,QAAQ,EAAExF,OAAQ;QAAA+E,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9F,OAAA;QACEyF,SAAS,EAAC,iBAAiB;QAC3BS,OAAO,EAAEhE,uBAAwB;QACjCiE,QAAQ,EAAExF,OAAO,IAAI,EAACP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,CAAC;QAAA+D,QAAA,EAE5C/E,OAAO,gBACNX,OAAA,CAAAE,SAAA;UAAAwF,QAAA,gBACE1F,OAAA;YAAMyF,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA,eAAE,CAAC,gBAEH9F,OAAA,CAAAE,SAAA;UAAAwF,QAAA,gBACE1F,OAAA;YAAMyF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,QACpC,EAAC7E,IAAI,GAAGkE,cAAc,CAAClE,IAAI,CAACmE,MAAM,GAAGnE,IAAI,CAAC8E,QAAQ,CAAC,GAAGZ,cAAc,CAAC/E,SAAS,CAACuB,WAAW,IAAI,CAAC,CAAC;QAAA,eACpG;MACH;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLjF,WAAW,iBACVb,OAAA;MAAKyF,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1F,OAAA;QAAKyF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1F,OAAA;UAAMyF,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC9F,OAAA;UAAA0F,QAAA,EAAM;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACN9F,OAAA;QAAA0F,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9F,OAAA;QAAKyF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1F,OAAA;UAAKyF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1F,OAAA;YAAA0F,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvB9F,OAAA;YAAMyF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAE7E,WAAW,CAACuF;UAAS;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN9F,OAAA;UAAKyF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1F,OAAA;YAAA0F,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpB9F,OAAA;YAAMyF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE7E,WAAW,CAACsD;UAAM;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9F,OAAA;MAAKyF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1F,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1F,OAAA;UAAMyF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzC9F,OAAA;UAAA0F,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACN9F,OAAA;QAAA0F,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CAvSIL,gBAAgB;EAAA,QACHP,WAAW,EACNC,OAAO;AAAA;AAAAwG,EAAA,GAFzBlG,gBAAgB;AAyStB,eAAeA,gBAAgB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}