import apiClient from './apiClient';

export const authService = {
    async login(email, password) {
        const response = await apiClient.post('/api/auth/login', { email, password });
        return response;
    },

    async register(userData) {
        // TODO: Implement register endpoint in backend
        throw new Error('Registration not implemented yet');
    },

    async getProfile() {
        // Mock profile data
        return {
            success: true,
            data: {
                id: 1,
                name: 'Demo User',
                email: '<EMAIL>',
                phone: '(*************',
                address: '123 Demo Street, Demo City, DC 12345'
            }
        };
    },

    async updateProfile(profileData) {
        // Mock profile update (always succeeds)
        return {
            success: true,
            data: {
                ...profileData,
                id: 1
            }
        };
    }
};
