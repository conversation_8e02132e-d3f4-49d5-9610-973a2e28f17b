{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\account\\\\AccountPreferences.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AccountPreferences = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [preferences, setPreferences] = useState({\n    // Communication Preferences\n    emailNotifications: true,\n    smsNotifications: false,\n    marketingEmails: true,\n    orderUpdates: true,\n    promotionalOffers: false,\n    // Display Preferences\n    currency: 'PHP',\n    language: 'en',\n    timezone: 'Asia/Manila',\n    theme: 'light',\n    // Privacy Preferences\n    profileVisibility: 'private',\n    dataSharing: false,\n    analyticsTracking: true,\n    // Shopping Preferences\n    savePaymentMethods: true,\n    autoSaveAddresses: true,\n    wishlistPublic: false,\n    recommendationsEnabled: true\n  });\n  useEffect(() => {\n    fetchPreferences();\n  }, []);\n  const fetchPreferences = async () => {\n    setLoading(true);\n    try {\n      // Mock preferences data - replace with actual API call\n      // In a real app, this would fetch user preferences from the backend\n      setLoading(false);\n    } catch (error) {\n      console.error('Failed to fetch preferences:', error);\n      setLoading(false);\n    }\n  };\n  const handleChange = (category, key, value) => {\n    setPreferences(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      // Mock API call - replace with actual API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setMessage('Preferences updated successfully!');\n    } catch (error) {\n      setMessage('Failed to update preferences. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const PreferenceSection = ({\n    title,\n    description,\n    children\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"preference-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preference-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"preference-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"preference-description\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preference-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 9\n  }, this);\n  const ToggleSwitch = ({\n    label,\n    description,\n    checked,\n    onChange\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"preference-item\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preference-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"preference-label\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"preference-desc\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"toggle-switch\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"checkbox\",\n        checked: checked,\n        onChange: e => onChange(e.target.checked)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"toggle-slider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n  const SelectOption = ({\n    label,\n    description,\n    value,\n    options,\n    onChange\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"preference-item\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preference-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"preference-label\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"preference-desc\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      className: \"preference-select\",\n      value: value,\n      onChange: e => onChange(e.target.value),\n      children: options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: option.value,\n        children: option.label\n      }, option.value, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-preferences\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Account Preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Customize your account settings and preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('success') ? 'success' : 'error'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"preferences-form\",\n      children: [/*#__PURE__*/_jsxDEV(PreferenceSection, {\n        title: \"Communication Preferences\",\n        description: \"Choose how you want to receive updates and notifications\",\n        children: [/*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Email Notifications\",\n          description: \"Receive important account updates via email\",\n          checked: preferences.emailNotifications,\n          onChange: value => handleChange('communication', 'emailNotifications', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"SMS Notifications\",\n          description: \"Receive order updates and alerts via SMS\",\n          checked: preferences.smsNotifications,\n          onChange: value => handleChange('communication', 'smsNotifications', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Marketing Emails\",\n          description: \"Receive newsletters and product updates\",\n          checked: preferences.marketingEmails,\n          onChange: value => handleChange('communication', 'marketingEmails', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Order Updates\",\n          description: \"Get notified about order status changes\",\n          checked: preferences.orderUpdates,\n          onChange: value => handleChange('communication', 'orderUpdates', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Promotional Offers\",\n          description: \"Receive special deals and discount notifications\",\n          checked: preferences.promotionalOffers,\n          onChange: value => handleChange('communication', 'promotionalOffers', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(PreferenceSection, {\n        title: \"Display Preferences\",\n        description: \"Customize how information is displayed\",\n        children: [/*#__PURE__*/_jsxDEV(SelectOption, {\n          label: \"Currency\",\n          description: \"Choose your preferred currency for pricing\",\n          value: preferences.currency,\n          options: [{\n            value: 'PHP',\n            label: 'Philippine Peso (₱)'\n          }, {\n            value: 'USD',\n            label: 'US Dollar ($)'\n          }, {\n            value: 'EUR',\n            label: 'Euro (€)'\n          }, {\n            value: 'GBP',\n            label: 'British Pound (£)'\n          }],\n          onChange: value => handleChange('display', 'currency', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SelectOption, {\n          label: \"Language\",\n          description: \"Choose your preferred language\",\n          value: preferences.language,\n          options: [{\n            value: 'en',\n            label: 'English'\n          }, {\n            value: 'fil',\n            label: 'Filipino'\n          }, {\n            value: 'es',\n            label: 'Spanish'\n          }, {\n            value: 'zh',\n            label: 'Chinese'\n          }],\n          onChange: value => handleChange('display', 'language', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SelectOption, {\n          label: \"Timezone\",\n          description: \"Set your local timezone\",\n          value: preferences.timezone,\n          options: [{\n            value: 'Asia/Manila',\n            label: 'Manila (GMT+8)'\n          }, {\n            value: 'America/New_York',\n            label: 'New York (GMT-5)'\n          }, {\n            value: 'Europe/London',\n            label: 'London (GMT+0)'\n          }, {\n            value: 'Asia/Tokyo',\n            label: 'Tokyo (GMT+9)'\n          }],\n          onChange: value => handleChange('display', 'timezone', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(SelectOption, {\n          label: \"Theme\",\n          description: \"Choose your preferred color theme\",\n          value: preferences.theme,\n          options: [{\n            value: 'light',\n            label: 'Light Theme'\n          }, {\n            value: 'dark',\n            label: 'Dark Theme'\n          }, {\n            value: 'auto',\n            label: 'Auto (System)'\n          }],\n          onChange: value => handleChange('display', 'theme', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(PreferenceSection, {\n        title: \"Privacy Preferences\",\n        description: \"Control your privacy and data sharing settings\",\n        children: [/*#__PURE__*/_jsxDEV(SelectOption, {\n          label: \"Profile Visibility\",\n          description: \"Control who can see your profile information\",\n          value: preferences.profileVisibility,\n          options: [{\n            value: 'private',\n            label: 'Private'\n          }, {\n            value: 'friends',\n            label: 'Friends Only'\n          }, {\n            value: 'public',\n            label: 'Public'\n          }],\n          onChange: value => handleChange('privacy', 'profileVisibility', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Data Sharing\",\n          description: \"Allow sharing of anonymized data for research\",\n          checked: preferences.dataSharing,\n          onChange: value => handleChange('privacy', 'dataSharing', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Analytics Tracking\",\n          description: \"Help improve our service with usage analytics\",\n          checked: preferences.analyticsTracking,\n          onChange: value => handleChange('privacy', 'analyticsTracking', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(PreferenceSection, {\n        title: \"Shopping Preferences\",\n        description: \"Customize your shopping experience\",\n        children: [/*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Save Payment Methods\",\n          description: \"Securely save payment methods for faster checkout\",\n          checked: preferences.savePaymentMethods,\n          onChange: value => handleChange('shopping', 'savePaymentMethods', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Auto-Save Addresses\",\n          description: \"Automatically save new addresses during checkout\",\n          checked: preferences.autoSaveAddresses,\n          onChange: value => handleChange('shopping', 'autoSaveAddresses', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Public Wishlist\",\n          description: \"Allow others to see your wishlist\",\n          checked: preferences.wishlistPublic,\n          onChange: value => handleChange('shopping', 'wishlistPublic', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ToggleSwitch, {\n          label: \"Product Recommendations\",\n          description: \"Show personalized product recommendations\",\n          checked: preferences.recommendationsEnabled,\n          onChange: value => handleChange('shopping', 'recommendationsEnabled', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-primary\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"spinner\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"4\",\n                fill: \"none\",\n                strokeDasharray: \"32\",\n                strokeDashoffset: \"32\",\n                children: /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"stroke-dashoffset\",\n                  dur: \"1s\",\n                  values: \"32;0;32\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this), \"Saving Preferences...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"20,6 9,17 4,12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 33\n            }, this), \"Save Preferences\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 9\n  }, this);\n};\n_s(AccountPreferences, \"PdYY3CbhPbp4agkPrn0DuR9BgKc=\", false, function () {\n  return [useAuth];\n});\n_c = AccountPreferences;\nexport default AccountPreferences;\nvar _c;\n$RefreshReg$(_c, \"AccountPreferences\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AccountPreferences", "_s", "user", "loading", "setLoading", "message", "setMessage", "preferences", "setPreferences", "emailNotifications", "smsNotifications", "marketingEmails", "orderUpdates", "promotionalOffers", "currency", "language", "timezone", "theme", "profileVisibility", "dataSharing", "analyticsTracking", "savePaymentMethods", "autoSaveAddresses", "wishlistPublic", "recommendationsEnabled", "fetchPreferences", "error", "console", "handleChange", "category", "key", "value", "prev", "handleSubmit", "e", "preventDefault", "Promise", "resolve", "setTimeout", "PreferenceSection", "title", "description", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ToggleSwitch", "label", "checked", "onChange", "type", "target", "SelectOption", "options", "map", "option", "includes", "onSubmit", "disabled", "width", "height", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "attributeName", "dur", "values", "repeatCount", "points", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/account/AccountPreferences.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst AccountPreferences = () => {\n    const { user } = useAuth();\n    const [loading, setLoading] = useState(false);\n    const [message, setMessage] = useState('');\n    const [preferences, setPreferences] = useState({\n        // Communication Preferences\n        emailNotifications: true,\n        smsNotifications: false,\n        marketingEmails: true,\n        orderUpdates: true,\n        promotionalOffers: false,\n        \n        // Display Preferences\n        currency: 'PHP',\n        language: 'en',\n        timezone: 'Asia/Manila',\n        theme: 'light',\n        \n        // Privacy Preferences\n        profileVisibility: 'private',\n        dataSharing: false,\n        analyticsTracking: true,\n        \n        // Shopping Preferences\n        savePaymentMethods: true,\n        autoSaveAddresses: true,\n        wishlistPublic: false,\n        recommendationsEnabled: true\n    });\n\n    useEffect(() => {\n        fetchPreferences();\n    }, []);\n\n    const fetchPreferences = async () => {\n        setLoading(true);\n        try {\n            // Mock preferences data - replace with actual API call\n            // In a real app, this would fetch user preferences from the backend\n            setLoading(false);\n        } catch (error) {\n            console.error('Failed to fetch preferences:', error);\n            setLoading(false);\n        }\n    };\n\n    const handleChange = (category, key, value) => {\n        setPreferences(prev => ({\n            ...prev,\n            [key]: value\n        }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setLoading(true);\n        setMessage('');\n\n        try {\n            // Mock API call - replace with actual API call\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            setMessage('Preferences updated successfully!');\n        } catch (error) {\n            setMessage('Failed to update preferences. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const PreferenceSection = ({ title, description, children }) => (\n        <div className=\"preference-section\">\n            <div className=\"preference-header\">\n                <h3 className=\"preference-title\">{title}</h3>\n                <p className=\"preference-description\">{description}</p>\n            </div>\n            <div className=\"preference-content\">\n                {children}\n            </div>\n        </div>\n    );\n\n    const ToggleSwitch = ({ label, description, checked, onChange }) => (\n        <div className=\"preference-item\">\n            <div className=\"preference-info\">\n                <label className=\"preference-label\">{label}</label>\n                {description && <p className=\"preference-desc\">{description}</p>}\n            </div>\n            <label className=\"toggle-switch\">\n                <input\n                    type=\"checkbox\"\n                    checked={checked}\n                    onChange={(e) => onChange(e.target.checked)}\n                />\n                <span className=\"toggle-slider\"></span>\n            </label>\n        </div>\n    );\n\n    const SelectOption = ({ label, description, value, options, onChange }) => (\n        <div className=\"preference-item\">\n            <div className=\"preference-info\">\n                <label className=\"preference-label\">{label}</label>\n                {description && <p className=\"preference-desc\">{description}</p>}\n            </div>\n            <select\n                className=\"preference-select\"\n                value={value}\n                onChange={(e) => onChange(e.target.value)}\n            >\n                {options.map(option => (\n                    <option key={option.value} value={option.value}>\n                        {option.label}\n                    </option>\n                ))}\n            </select>\n        </div>\n    );\n\n    return (\n        <div className=\"account-preferences\">\n            <div className=\"section-header\">\n                <div>\n                    <h2 className=\"section-title\">Account Preferences</h2>\n                    <p className=\"section-subtitle\">\n                        Customize your account settings and preferences\n                    </p>\n                </div>\n            </div>\n\n            {message && (\n                <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>\n                    {message}\n                </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"preferences-form\">\n                <PreferenceSection\n                    title=\"Communication Preferences\"\n                    description=\"Choose how you want to receive updates and notifications\"\n                >\n                    <ToggleSwitch\n                        label=\"Email Notifications\"\n                        description=\"Receive important account updates via email\"\n                        checked={preferences.emailNotifications}\n                        onChange={(value) => handleChange('communication', 'emailNotifications', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"SMS Notifications\"\n                        description=\"Receive order updates and alerts via SMS\"\n                        checked={preferences.smsNotifications}\n                        onChange={(value) => handleChange('communication', 'smsNotifications', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Marketing Emails\"\n                        description=\"Receive newsletters and product updates\"\n                        checked={preferences.marketingEmails}\n                        onChange={(value) => handleChange('communication', 'marketingEmails', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Order Updates\"\n                        description=\"Get notified about order status changes\"\n                        checked={preferences.orderUpdates}\n                        onChange={(value) => handleChange('communication', 'orderUpdates', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Promotional Offers\"\n                        description=\"Receive special deals and discount notifications\"\n                        checked={preferences.promotionalOffers}\n                        onChange={(value) => handleChange('communication', 'promotionalOffers', value)}\n                    />\n                </PreferenceSection>\n\n                <PreferenceSection\n                    title=\"Display Preferences\"\n                    description=\"Customize how information is displayed\"\n                >\n                    <SelectOption\n                        label=\"Currency\"\n                        description=\"Choose your preferred currency for pricing\"\n                        value={preferences.currency}\n                        options={[\n                            { value: 'PHP', label: 'Philippine Peso (₱)' },\n                            { value: 'USD', label: 'US Dollar ($)' },\n                            { value: 'EUR', label: 'Euro (€)' },\n                            { value: 'GBP', label: 'British Pound (£)' }\n                        ]}\n                        onChange={(value) => handleChange('display', 'currency', value)}\n                    />\n                    <SelectOption\n                        label=\"Language\"\n                        description=\"Choose your preferred language\"\n                        value={preferences.language}\n                        options={[\n                            { value: 'en', label: 'English' },\n                            { value: 'fil', label: 'Filipino' },\n                            { value: 'es', label: 'Spanish' },\n                            { value: 'zh', label: 'Chinese' }\n                        ]}\n                        onChange={(value) => handleChange('display', 'language', value)}\n                    />\n                    <SelectOption\n                        label=\"Timezone\"\n                        description=\"Set your local timezone\"\n                        value={preferences.timezone}\n                        options={[\n                            { value: 'Asia/Manila', label: 'Manila (GMT+8)' },\n                            { value: 'America/New_York', label: 'New York (GMT-5)' },\n                            { value: 'Europe/London', label: 'London (GMT+0)' },\n                            { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' }\n                        ]}\n                        onChange={(value) => handleChange('display', 'timezone', value)}\n                    />\n                    <SelectOption\n                        label=\"Theme\"\n                        description=\"Choose your preferred color theme\"\n                        value={preferences.theme}\n                        options={[\n                            { value: 'light', label: 'Light Theme' },\n                            { value: 'dark', label: 'Dark Theme' },\n                            { value: 'auto', label: 'Auto (System)' }\n                        ]}\n                        onChange={(value) => handleChange('display', 'theme', value)}\n                    />\n                </PreferenceSection>\n\n                <PreferenceSection\n                    title=\"Privacy Preferences\"\n                    description=\"Control your privacy and data sharing settings\"\n                >\n                    <SelectOption\n                        label=\"Profile Visibility\"\n                        description=\"Control who can see your profile information\"\n                        value={preferences.profileVisibility}\n                        options={[\n                            { value: 'private', label: 'Private' },\n                            { value: 'friends', label: 'Friends Only' },\n                            { value: 'public', label: 'Public' }\n                        ]}\n                        onChange={(value) => handleChange('privacy', 'profileVisibility', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Data Sharing\"\n                        description=\"Allow sharing of anonymized data for research\"\n                        checked={preferences.dataSharing}\n                        onChange={(value) => handleChange('privacy', 'dataSharing', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Analytics Tracking\"\n                        description=\"Help improve our service with usage analytics\"\n                        checked={preferences.analyticsTracking}\n                        onChange={(value) => handleChange('privacy', 'analyticsTracking', value)}\n                    />\n                </PreferenceSection>\n\n                <PreferenceSection\n                    title=\"Shopping Preferences\"\n                    description=\"Customize your shopping experience\"\n                >\n                    <ToggleSwitch\n                        label=\"Save Payment Methods\"\n                        description=\"Securely save payment methods for faster checkout\"\n                        checked={preferences.savePaymentMethods}\n                        onChange={(value) => handleChange('shopping', 'savePaymentMethods', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Auto-Save Addresses\"\n                        description=\"Automatically save new addresses during checkout\"\n                        checked={preferences.autoSaveAddresses}\n                        onChange={(value) => handleChange('shopping', 'autoSaveAddresses', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Public Wishlist\"\n                        description=\"Allow others to see your wishlist\"\n                        checked={preferences.wishlistPublic}\n                        onChange={(value) => handleChange('shopping', 'wishlistPublic', value)}\n                    />\n                    <ToggleSwitch\n                        label=\"Product Recommendations\"\n                        description=\"Show personalized product recommendations\"\n                        checked={preferences.recommendationsEnabled}\n                        onChange={(value) => handleChange('shopping', 'recommendationsEnabled', value)}\n                    />\n                </PreferenceSection>\n\n                <div className=\"form-actions\">\n                    <button\n                        type=\"submit\"\n                        className=\"btn-primary\"\n                        disabled={loading}\n                    >\n                        {loading ? (\n                            <>\n                                <svg className=\"spinner\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                                    <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" strokeDasharray=\"32\" strokeDashoffset=\"32\">\n                                        <animate attributeName=\"stroke-dashoffset\" dur=\"1s\" values=\"32;0;32\" repeatCount=\"indefinite\"/>\n                                    </circle>\n                                </svg>\n                                Saving Preferences...\n                            </>\n                        ) : (\n                            <>\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                    <polyline points=\"20,6 9,17 4,12\"/>\n                                </svg>\n                                Save Preferences\n                            </>\n                        )}\n                    </button>\n                </div>\n            </form>\n        </div>\n    );\n};\n\nexport default AccountPreferences;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC3C;IACAgB,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE,KAAK;IAExB;IACAC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,OAAO;IAEd;IACAC,iBAAiB,EAAE,SAAS;IAC5BC,WAAW,EAAE,KAAK;IAClBC,iBAAiB,EAAE,IAAI;IAEvB;IACAC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE,IAAI;IACvBC,cAAc,EAAE,KAAK;IACrBC,sBAAsB,EAAE;EAC5B,CAAC,CAAC;EAEF9B,SAAS,CAAC,MAAM;IACZ+B,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjCrB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA;MACA;MACAA,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDtB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,KAAK;IAC3CvB,cAAc,CAACwB,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACX,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB/B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA;MACA,MAAM,IAAI8B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD/B,UAAU,CAAC,mCAAmC,CAAC;IACnD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACZpB,UAAU,CAAC,iDAAiD,CAAC;IACjE,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMmC,iBAAiB,GAAGA,CAAC;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAS,CAAC,kBACvD7C,OAAA;IAAK8C,SAAS,EAAC,oBAAoB;IAAAD,QAAA,gBAC/B7C,OAAA;MAAK8C,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAC9B7C,OAAA;QAAI8C,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAEF;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7ClD,OAAA;QAAG8C,SAAS,EAAC,wBAAwB;QAAAD,QAAA,EAAED;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eACNlD,OAAA;MAAK8C,SAAS,EAAC,oBAAoB;MAAAD,QAAA,EAC9BA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMC,YAAY,GAAGA,CAAC;IAAEC,KAAK;IAAER,WAAW;IAAES,OAAO;IAAEC;EAAS,CAAC,kBAC3DtD,OAAA;IAAK8C,SAAS,EAAC,iBAAiB;IAAAD,QAAA,gBAC5B7C,OAAA;MAAK8C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5B7C,OAAA;QAAO8C,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAEO;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAClDN,WAAW,iBAAI5C,OAAA;QAAG8C,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAAED;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eACNlD,OAAA;MAAO8C,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5B7C,OAAA;QACIuD,IAAI,EAAC,UAAU;QACfF,OAAO,EAAEA,OAAQ;QACjBC,QAAQ,EAAGjB,CAAC,IAAKiB,QAAQ,CAACjB,CAAC,CAACmB,MAAM,CAACH,OAAO;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACFlD,OAAA;QAAM8C,SAAS,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CACR;EAED,MAAMO,YAAY,GAAGA,CAAC;IAAEL,KAAK;IAAER,WAAW;IAAEV,KAAK;IAAEwB,OAAO;IAAEJ;EAAS,CAAC,kBAClEtD,OAAA;IAAK8C,SAAS,EAAC,iBAAiB;IAAAD,QAAA,gBAC5B7C,OAAA;MAAK8C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5B7C,OAAA;QAAO8C,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAEO;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAClDN,WAAW,iBAAI5C,OAAA;QAAG8C,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAAED;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eACNlD,OAAA;MACI8C,SAAS,EAAC,mBAAmB;MAC7BZ,KAAK,EAAEA,KAAM;MACboB,QAAQ,EAAGjB,CAAC,IAAKiB,QAAQ,CAACjB,CAAC,CAACmB,MAAM,CAACtB,KAAK,CAAE;MAAAW,QAAA,EAEzCa,OAAO,CAACC,GAAG,CAACC,MAAM,iBACf5D,OAAA;QAA2BkC,KAAK,EAAE0B,MAAM,CAAC1B,KAAM;QAAAW,QAAA,EAC1Ce,MAAM,CAACR;MAAK,GADJQ,MAAM,CAAC1B,KAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEjB,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACR;EAED,oBACIlD,OAAA;IAAK8C,SAAS,EAAC,qBAAqB;IAAAD,QAAA,gBAChC7C,OAAA;MAAK8C,SAAS,EAAC,gBAAgB;MAAAD,QAAA,eAC3B7C,OAAA;QAAA6C,QAAA,gBACI7C,OAAA;UAAI8C,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDlD,OAAA;UAAG8C,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAEhC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL1C,OAAO,iBACJR,OAAA;MAAK8C,SAAS,EAAE,WAAWtC,OAAO,CAACqD,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,OAAO,EAAG;MAAAhB,QAAA,EAC1ErC;IAAO;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAEDlD,OAAA;MAAM8D,QAAQ,EAAE1B,YAAa;MAACU,SAAS,EAAC,kBAAkB;MAAAD,QAAA,gBACtD7C,OAAA,CAAC0C,iBAAiB;QACdC,KAAK,EAAC,2BAA2B;QACjCC,WAAW,EAAC,0DAA0D;QAAAC,QAAA,gBAEtE7C,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,qBAAqB;UAC3BR,WAAW,EAAC,6CAA6C;UACzDS,OAAO,EAAE3C,WAAW,CAACE,kBAAmB;UACxC0C,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,eAAe,EAAE,oBAAoB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,mBAAmB;UACzBR,WAAW,EAAC,0CAA0C;UACtDS,OAAO,EAAE3C,WAAW,CAACG,gBAAiB;UACtCyC,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,eAAe,EAAE,kBAAkB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,kBAAkB;UACxBR,WAAW,EAAC,yCAAyC;UACrDS,OAAO,EAAE3C,WAAW,CAACI,eAAgB;UACrCwC,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,eAAe,EAAE,iBAAiB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,eAAe;UACrBR,WAAW,EAAC,yCAAyC;UACrDS,OAAO,EAAE3C,WAAW,CAACK,YAAa;UAClCuC,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,eAAe,EAAE,cAAc,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,oBAAoB;UAC1BR,WAAW,EAAC,kDAAkD;UAC9DS,OAAO,EAAE3C,WAAW,CAACM,iBAAkB;UACvCsC,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,eAAe,EAAE,mBAAmB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAEpBlD,OAAA,CAAC0C,iBAAiB;QACdC,KAAK,EAAC,qBAAqB;QAC3BC,WAAW,EAAC,wCAAwC;QAAAC,QAAA,gBAEpD7C,OAAA,CAACyD,YAAY;UACTL,KAAK,EAAC,UAAU;UAChBR,WAAW,EAAC,4CAA4C;UACxDV,KAAK,EAAExB,WAAW,CAACO,QAAS;UAC5ByC,OAAO,EAAE,CACL;YAAExB,KAAK,EAAE,KAAK;YAAEkB,KAAK,EAAE;UAAsB,CAAC,EAC9C;YAAElB,KAAK,EAAE,KAAK;YAAEkB,KAAK,EAAE;UAAgB,CAAC,EACxC;YAAElB,KAAK,EAAE,KAAK;YAAEkB,KAAK,EAAE;UAAW,CAAC,EACnC;YAAElB,KAAK,EAAE,KAAK;YAAEkB,KAAK,EAAE;UAAoB,CAAC,CAC9C;UACFE,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,UAAU,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFlD,OAAA,CAACyD,YAAY;UACTL,KAAK,EAAC,UAAU;UAChBR,WAAW,EAAC,gCAAgC;UAC5CV,KAAK,EAAExB,WAAW,CAACQ,QAAS;UAC5BwC,OAAO,EAAE,CACL;YAAExB,KAAK,EAAE,IAAI;YAAEkB,KAAK,EAAE;UAAU,CAAC,EACjC;YAAElB,KAAK,EAAE,KAAK;YAAEkB,KAAK,EAAE;UAAW,CAAC,EACnC;YAAElB,KAAK,EAAE,IAAI;YAAEkB,KAAK,EAAE;UAAU,CAAC,EACjC;YAAElB,KAAK,EAAE,IAAI;YAAEkB,KAAK,EAAE;UAAU,CAAC,CACnC;UACFE,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,UAAU,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFlD,OAAA,CAACyD,YAAY;UACTL,KAAK,EAAC,UAAU;UAChBR,WAAW,EAAC,yBAAyB;UACrCV,KAAK,EAAExB,WAAW,CAACS,QAAS;UAC5BuC,OAAO,EAAE,CACL;YAAExB,KAAK,EAAE,aAAa;YAAEkB,KAAK,EAAE;UAAiB,CAAC,EACjD;YAAElB,KAAK,EAAE,kBAAkB;YAAEkB,KAAK,EAAE;UAAmB,CAAC,EACxD;YAAElB,KAAK,EAAE,eAAe;YAAEkB,KAAK,EAAE;UAAiB,CAAC,EACnD;YAAElB,KAAK,EAAE,YAAY;YAAEkB,KAAK,EAAE;UAAgB,CAAC,CACjD;UACFE,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,UAAU,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFlD,OAAA,CAACyD,YAAY;UACTL,KAAK,EAAC,OAAO;UACbR,WAAW,EAAC,mCAAmC;UAC/CV,KAAK,EAAExB,WAAW,CAACU,KAAM;UACzBsC,OAAO,EAAE,CACL;YAAExB,KAAK,EAAE,OAAO;YAAEkB,KAAK,EAAE;UAAc,CAAC,EACxC;YAAElB,KAAK,EAAE,MAAM;YAAEkB,KAAK,EAAE;UAAa,CAAC,EACtC;YAAElB,KAAK,EAAE,MAAM;YAAEkB,KAAK,EAAE;UAAgB,CAAC,CAC3C;UACFE,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,OAAO,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAEpBlD,OAAA,CAAC0C,iBAAiB;QACdC,KAAK,EAAC,qBAAqB;QAC3BC,WAAW,EAAC,gDAAgD;QAAAC,QAAA,gBAE5D7C,OAAA,CAACyD,YAAY;UACTL,KAAK,EAAC,oBAAoB;UAC1BR,WAAW,EAAC,8CAA8C;UAC1DV,KAAK,EAAExB,WAAW,CAACW,iBAAkB;UACrCqC,OAAO,EAAE,CACL;YAAExB,KAAK,EAAE,SAAS;YAAEkB,KAAK,EAAE;UAAU,CAAC,EACtC;YAAElB,KAAK,EAAE,SAAS;YAAEkB,KAAK,EAAE;UAAe,CAAC,EAC3C;YAAElB,KAAK,EAAE,QAAQ;YAAEkB,KAAK,EAAE;UAAS,CAAC,CACtC;UACFE,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,mBAAmB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,cAAc;UACpBR,WAAW,EAAC,+CAA+C;UAC3DS,OAAO,EAAE3C,WAAW,CAACY,WAAY;UACjCgC,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,aAAa,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,oBAAoB;UAC1BR,WAAW,EAAC,+CAA+C;UAC3DS,OAAO,EAAE3C,WAAW,CAACa,iBAAkB;UACvC+B,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,SAAS,EAAE,mBAAmB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAEpBlD,OAAA,CAAC0C,iBAAiB;QACdC,KAAK,EAAC,sBAAsB;QAC5BC,WAAW,EAAC,oCAAoC;QAAAC,QAAA,gBAEhD7C,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,sBAAsB;UAC5BR,WAAW,EAAC,mDAAmD;UAC/DS,OAAO,EAAE3C,WAAW,CAACc,kBAAmB;UACxC8B,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,UAAU,EAAE,oBAAoB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,qBAAqB;UAC3BR,WAAW,EAAC,kDAAkD;UAC9DS,OAAO,EAAE3C,WAAW,CAACe,iBAAkB;UACvC6B,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,UAAU,EAAE,mBAAmB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,iBAAiB;UACvBR,WAAW,EAAC,mCAAmC;UAC/CS,OAAO,EAAE3C,WAAW,CAACgB,cAAe;UACpC4B,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,UAAU,EAAE,gBAAgB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACFlD,OAAA,CAACmD,YAAY;UACTC,KAAK,EAAC,yBAAyB;UAC/BR,WAAW,EAAC,2CAA2C;UACvDS,OAAO,EAAE3C,WAAW,CAACiB,sBAAuB;UAC5C2B,QAAQ,EAAGpB,KAAK,IAAKH,YAAY,CAAC,UAAU,EAAE,wBAAwB,EAAEG,KAAK;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAEpBlD,OAAA;QAAK8C,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzB7C,OAAA;UACIuD,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,aAAa;UACvBiB,QAAQ,EAAEzD,OAAQ;UAAAuC,QAAA,EAEjBvC,OAAO,gBACJN,OAAA,CAAAE,SAAA;YAAA2C,QAAA,gBACI7C,OAAA;cAAK8C,SAAS,EAAC,SAAS;cAACkB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAArB,QAAA,eAC/D7C,OAAA;gBAAQmE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,IAAI,EAAC,MAAM;gBAACC,eAAe,EAAC,IAAI;gBAACC,gBAAgB,EAAC,IAAI;gBAAA7B,QAAA,eACvH7C,OAAA;kBAAS2E,aAAa,EAAC,mBAAmB;kBAACC,GAAG,EAAC,IAAI;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAY;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,yBAEV;UAAA,eAAE,CAAC,gBAEHlD,OAAA,CAAAE,SAAA;YAAA2C,QAAA,gBACI7C,OAAA;cAAKgE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACM,IAAI,EAAC,MAAM;cAACF,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAA1B,QAAA,eAC7F7C,OAAA;gBAAU+E,MAAM,EAAC;cAAgB;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,oBAEV;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAC9C,EAAA,CAxTID,kBAAkB;EAAA,QACHL,OAAO;AAAA;AAAAkF,EAAA,GADtB7E,kBAAkB;AA0TxB,eAAeA,kBAAkB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}