{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\payment\\\\PayMongoCheckout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PayMongoCheckout = ({\n  orderData,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    clearCart\n  } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [{\n    id: 'card',\n    name: 'Credit/Debit Card',\n    icon: '💳',\n    description: 'Visa, Mastercard, JCB, American Express',\n    fee: '3.5% + ₱15'\n  }, {\n    id: 'gcash',\n    name: 'GCash',\n    icon: '📱',\n    description: 'Pay using your GCash wallet',\n    fee: '2.5%'\n  }, {\n    id: 'grabpay',\n    name: 'GrabPay',\n    icon: '🚗',\n    description: 'Pay using your GrabPay wallet',\n    fee: '2.5%'\n  }, {\n    id: 'bank',\n    name: 'Online Banking',\n    icon: '🏦',\n    description: 'Direct bank transfer',\n    fee: '1.5%'\n  }];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData !== null && orderData !== void 0 && orderData.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount, selectedMethod]);\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      var _orderData$shippingAd, _orderData$shippingAd2, _orderData$shippingAd3, _orderData$shippingAd4, _orderData$shippingAd5, _orderData$shippingAd6, _orderData$shippingAd7, _orderData$shippingAd8, _orderData$shippingAd9, _orderData$shippingAd0, _orderData$shippingAd1, _orderData$shippingAd10, _orderData$shippingAd11, _orderData$shippingAd12, _orderData$shippingAd13, _orderData$shippingAd14, _orderData$shippingAd15;\n      // First, create a pending order in the backend\n      const shippingAddressString = `${((_orderData$shippingAd = orderData.shippingAddress) === null || _orderData$shippingAd === void 0 ? void 0 : _orderData$shippingAd.address) || ''}, ${((_orderData$shippingAd2 = orderData.shippingAddress) === null || _orderData$shippingAd2 === void 0 ? void 0 : _orderData$shippingAd2.city) || ''}, ${((_orderData$shippingAd3 = orderData.shippingAddress) === null || _orderData$shippingAd3 === void 0 ? void 0 : _orderData$shippingAd3.state) || ''} ${((_orderData$shippingAd4 = orderData.shippingAddress) === null || _orderData$shippingAd4 === void 0 ? void 0 : _orderData$shippingAd4.zipCode) || ''}, ${((_orderData$shippingAd5 = orderData.shippingAddress) === null || _orderData$shippingAd5 === void 0 ? void 0 : _orderData$shippingAd5.country) || ''}`.trim();\n      const orderCreationData = {\n        customerEmail: (_orderData$shippingAd6 = orderData.shippingAddress) === null || _orderData$shippingAd6 === void 0 ? void 0 : _orderData$shippingAd6.email,\n        customerName: `${((_orderData$shippingAd7 = orderData.shippingAddress) === null || _orderData$shippingAd7 === void 0 ? void 0 : _orderData$shippingAd7.firstName) || ''} ${((_orderData$shippingAd8 = orderData.shippingAddress) === null || _orderData$shippingAd8 === void 0 ? void 0 : _orderData$shippingAd8.lastName) || ''}`.trim(),\n        customerPhone: ((_orderData$shippingAd9 = orderData.shippingAddress) === null || _orderData$shippingAd9 === void 0 ? void 0 : _orderData$shippingAd9.phone) || undefined,\n        // Use undefined instead of empty string\n        shippingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Address not provided, will be updated from payment metadata',\n        billingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Same as shipping address',\n        items: orderData.items.map(item => {\n          var _item$product, _item$product2, _item$product3, _item$product4, _item$product5;\n          return {\n            variantId: ((_item$product = item.product) === null || _item$product === void 0 ? void 0 : _item$product.id) || item.variantId || item.id || '00000000-0000-0000-0000-000000000000',\n            // Use product ID or fallback\n            quantity: item.quantity || 1,\n            unitPrice: item.price || ((_item$product2 = item.product) === null || _item$product2 === void 0 ? void 0 : _item$product2.price) || ((_item$product3 = item.product) === null || _item$product3 === void 0 ? void 0 : _item$product3.discountPrice) || 0,\n            totalPrice: (item.quantity || 1) * (item.price || ((_item$product4 = item.product) === null || _item$product4 === void 0 ? void 0 : _item$product4.price) || ((_item$product5 = item.product) === null || _item$product5 === void 0 ? void 0 : _item$product5.discountPrice) || 0),\n            customConfiguration: item.customization ? JSON.stringify(item.customization) : undefined\n          };\n        }),\n        subTotal: orderData.subtotal || orderData.totalAmount || 0,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount || 0,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Debug: Log the order creation data\n      console.log('Order creation data being sent:', JSON.stringify(orderCreationData, null, 2));\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100),\n        // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: ((_orderData$shippingAd0 = orderData.shippingAddress) === null || _orderData$shippingAd0 === void 0 ? void 0 : _orderData$shippingAd0.firstName) + ' ' + ((_orderData$shippingAd1 = orderData.shippingAddress) === null || _orderData$shippingAd1 === void 0 ? void 0 : _orderData$shippingAd1.lastName),\n          email: (_orderData$shippingAd10 = orderData.shippingAddress) === null || _orderData$shippingAd10 === void 0 ? void 0 : _orderData$shippingAd10.email,\n          phone: (_orderData$shippingAd11 = orderData.shippingAddress) === null || _orderData$shippingAd11 === void 0 ? void 0 : _orderData$shippingAd11.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: ((_orderData$shippingAd12 = orderData.shippingAddress) === null || _orderData$shippingAd12 === void 0 ? void 0 : _orderData$shippingAd12.firstName) + ' ' + ((_orderData$shippingAd13 = orderData.shippingAddress) === null || _orderData$shippingAd13 === void 0 ? void 0 : _orderData$shippingAd13.lastName),\n            email: (_orderData$shippingAd14 = orderData.shippingAddress) === null || _orderData$shippingAd14 === void 0 ? void 0 : _orderData$shippingAd14.email,\n            phone: (_orderData$shippingAd15 = orderData.shippingAddress) === null || _orderData$shippingAd15 === void 0 ? void 0 : _orderData$shippingAd15.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n\n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          var _statusResult$data;\n          const status = ((_statusResult$data = statusResult.data) === null || _statusResult$data === void 0 ? void 0 : _statusResult$data.status) || statusResult.status;\n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page - the backend order will be updated via webhook\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: orderId,\n                  total_amount: orderData.totalAmount,\n                  currency: 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully! Your order is being processed.',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paymongo-checkout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Complete Your Payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Secure payment powered by PayMongo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Order Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subtotal:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatCurrency(orderData.totalAmount || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), fees && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Payment Fee:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row total\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Amount:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.amount + fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-methods\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Select Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"method-grid\",\n        children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `payment-method ${selectedMethod === method.id ? 'selected' : ''}`,\n          onClick: () => setSelectedMethod(method.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-icon\",\n            children: method.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: method.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-description\",\n              children: method.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-fee\",\n              children: [\"Fee: \", method.fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, method.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: handleCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleCreatePaymentLink,\n        disabled: loading || !(orderData !== null && orderData !== void 0 && orderData.totalAmount),\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), \"Creating Payment Link...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"payment-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), \"Pay \", fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), paymentLink && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-link-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Payment link created successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-link-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Reference:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reference-number\",\n            children: paymentLink.reference\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            children: paymentLink.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"security-icon\",\n          children: \"\\uD83D\\uDD10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Secure Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(PayMongoCheckout, \"86+uGYKNFyWUeaqIj86xKtQyg4w=\", false, function () {\n  return [useNavigate, useCart];\n});\n_c = PayMongoCheckout;\nexport default PayMongoCheckout;\nvar _c;\n$RefreshReg$(_c, \"PayMongoCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCart", "paymentService", "apiClient", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PayMongoCheckout", "orderData", "onSuccess", "onError", "onCancel", "_s", "navigate", "clearCart", "loading", "setLoading", "paymentLink", "setPaymentLink", "error", "setError", "fees", "setFees", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "paymentMethods", "id", "name", "icon", "description", "fee", "totalAmount", "calculatePaymentFees", "amountInCentavos", "Math", "round", "result", "calculateFees", "success", "data", "console", "handleCreatePaymentLink", "_orderData$shippingAd", "_orderData$shippingAd2", "_orderData$shippingAd3", "_orderData$shippingAd4", "_orderData$shippingAd5", "_orderData$shippingAd6", "_orderData$shippingAd7", "_orderData$shippingAd8", "_orderData$shippingAd9", "_orderData$shippingAd0", "_orderData$shippingAd1", "_orderData$shippingAd10", "_orderData$shippingAd11", "_orderData$shippingAd12", "_orderData$shippingAd13", "_orderData$shippingAd14", "_orderData$shippingAd15", "shippingAddressString", "shippingAddress", "address", "city", "state", "zipCode", "country", "trim", "orderCreationData", "customerEmail", "email", "customerName", "firstName", "lastName", "customerPhone", "phone", "undefined", "length", "billing<PERSON><PERSON>ress", "items", "map", "item", "_item$product", "_item$product2", "_item$product3", "_item$product4", "_item$product5", "variantId", "product", "quantity", "unitPrice", "price", "discountPrice", "totalPrice", "customConfiguration", "customization", "JSON", "stringify", "subTotal", "subtotal", "taxAmount", "tax", "shippingAmount", "shipping", "discountAmount", "currency", "notes", "log", "orderResult", "post", "Error", "message", "createdOrder", "order", "OrderNumber", "paymentData", "orderId", "customer", "metadata", "paymentMethod", "source", "backendOrderId", "OrderID", "createPaymentLink", "url", "window", "open", "startPaymentStatusPolling", "backendOrder", "paymentLinkId", "pollInterval", "setInterval", "statusResult", "getPaymentStatus", "status", "_statusResult$data", "clearInterval", "order_number", "total_amount", "created_at", "Date", "toISOString", "shipping_address", "paymentStatus", "includes", "setTimeout", "handleCancel", "cancelPaymentLink", "catch", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalFee", "method", "onClick", "disabled", "reference", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/payment/PayMongoCheckout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\n\nconst PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const { clearCart } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      icon: '💳',\n      description: 'Visa, Mastercard, JCB, American Express',\n      fee: '3.5% + ₱15'\n    },\n    {\n      id: 'gcash',\n      name: 'GCash',\n      icon: '📱',\n      description: 'Pay using your GCash wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'grabpay',\n      name: 'GrabPay',\n      icon: '🚗',\n      description: 'Pay using your GrabPay wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'bank',\n      name: 'Online Banking',\n      icon: '🏦',\n      description: 'Direct bank transfer',\n      fee: '1.5%'\n    }\n  ];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData?.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData?.totalAmount, selectedMethod]);\n\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // First, create a pending order in the backend\n      const shippingAddressString = `${orderData.shippingAddress?.address || ''}, ${orderData.shippingAddress?.city || ''}, ${orderData.shippingAddress?.state || ''} ${orderData.shippingAddress?.zipCode || ''}, ${orderData.shippingAddress?.country || ''}`.trim();\n\n      const orderCreationData = {\n        customerEmail: orderData.shippingAddress?.email,\n        customerName: `${orderData.shippingAddress?.firstName || ''} ${orderData.shippingAddress?.lastName || ''}`.trim(),\n        customerPhone: orderData.shippingAddress?.phone || undefined, // Use undefined instead of empty string\n        shippingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Address not provided, will be updated from payment metadata',\n        billingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Same as shipping address',\n        items: orderData.items.map(item => ({\n          variantId: item.product?.id || item.variantId || item.id || '00000000-0000-0000-0000-000000000000', // Use product ID or fallback\n          quantity: item.quantity || 1,\n          unitPrice: item.price || item.product?.price || item.product?.discountPrice || 0,\n          totalPrice: (item.quantity || 1) * (item.price || item.product?.price || item.product?.discountPrice || 0),\n          customConfiguration: item.customization ? JSON.stringify(item.customization) : undefined\n        })),\n        subTotal: orderData.subtotal || orderData.totalAmount || 0,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount || 0,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Debug: Log the order creation data\n      console.log('Order creation data being sent:', JSON.stringify(orderCreationData, null, 2));\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n          email: orderData.shippingAddress?.email,\n          phone: orderData.shippingAddress?.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n            email: orderData.shippingAddress?.email,\n            phone: orderData.shippingAddress?.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        \n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          const status = statusResult.data?.status || statusResult.status;\n          \n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page - the backend order will be updated via webhook\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: orderId,\n                  total_amount: orderData.totalAmount,\n                  currency: 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully! Your order is being processed.',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paymongo-checkout\">\n      <div className=\"checkout-header\">\n        <h2>Complete Your Payment</h2>\n        <p>Secure payment powered by PayMongo</p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div className=\"summary-row\">\n          <span>Subtotal:</span>\n          <span>{formatCurrency(orderData.totalAmount || 0)}</span>\n        </div>\n        {fees && (\n          <>\n            <div className=\"summary-row\">\n              <span>Payment Fee:</span>\n              <span>{formatCurrency(fees.totalFee)}</span>\n            </div>\n            <div className=\"summary-row total\">\n              <span>Total Amount:</span>\n              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"payment-methods\">\n        <h3>Select Payment Method</h3>\n        <div className=\"method-grid\">\n          {paymentMethods.map((method) => (\n            <div\n              key={method.id}\n              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}\n              onClick={() => setSelectedMethod(method.id)}\n            >\n              <div className=\"method-icon\">{method.icon}</div>\n              <div className=\"method-info\">\n                <div className=\"method-name\">{method.name}</div>\n                <div className=\"method-description\">{method.description}</div>\n                <div className=\"method-fee\">Fee: {method.fee}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* Payment Actions */}\n      <div className=\"payment-actions\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={handleCancel}\n          disabled={loading}\n        >\n          Cancel\n        </button>\n        <button\n          className=\"btn btn-primary\"\n          onClick={handleCreatePaymentLink}\n          disabled={loading || !orderData?.totalAmount}\n        >\n          {loading ? (\n            <>\n              <span className=\"loading-spinner\"></span>\n              Creating Payment Link...\n            </>\n          ) : (\n            <>\n              <span className=\"payment-icon\">🔒</span>\n              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Payment Link Status */}\n      {paymentLink && (\n        <div className=\"payment-link-status\">\n          <div className=\"status-header\">\n            <span className=\"status-icon\">✅</span>\n            <span>Payment link created successfully!</span>\n          </div>\n          <p>\n            A new tab has opened with your secure payment page. \n            Complete your payment there and return here to see the confirmation.\n          </p>\n          <div className=\"payment-link-info\">\n            <div className=\"info-row\">\n              <span>Reference:</span>\n              <span className=\"reference-number\">{paymentLink.reference}</span>\n            </div>\n            <div className=\"info-row\">\n              <span>Status:</span>\n              <span className=\"status-badge\">{paymentLink.status}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Security Notice */}\n      <div className=\"security-notice\">\n        <div className=\"security-header\">\n          <span className=\"security-icon\">🔐</span>\n          <span>Secure Payment</span>\n        </div>\n        <p>\n          Your payment information is encrypted and secure. \n          PayMongo is PCI DSS compliant and follows international security standards.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default PayMongoCheckout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAU,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC;;EAE5D;EACA,MAAM4B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,yCAAyC;IACtDC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,6BAA6B;IAC1CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sBAAsB;IACnCC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,EAAE;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,EAAER,cAAc,CAAC,CAAC;EAE5C,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAClE,MAAMK,MAAM,GAAG,MAAMnC,cAAc,CAACoC,aAAa,CAACJ,gBAAgB,EAAEV,cAAc,CAAC;MACnF,IAAIa,MAAM,CAACE,OAAO,EAAE;QAClBhB,OAAO,CAACc,MAAM,CAACG,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMsB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CzB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAsB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACF;MACA,MAAMC,qBAAqB,GAAG,GAAG,EAAAjB,qBAAA,GAAAlC,SAAS,CAACoD,eAAe,cAAAlB,qBAAA,uBAAzBA,qBAAA,CAA2BmB,OAAO,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAAnC,SAAS,CAACoD,eAAe,cAAAjB,sBAAA,uBAAzBA,sBAAA,CAA2BmB,IAAI,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAApC,SAAS,CAACoD,eAAe,cAAAhB,sBAAA,uBAAzBA,sBAAA,CAA2BmB,KAAK,KAAI,EAAE,IAAI,EAAAlB,sBAAA,GAAArC,SAAS,CAACoD,eAAe,cAAAf,sBAAA,uBAAzBA,sBAAA,CAA2BmB,OAAO,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAAtC,SAAS,CAACoD,eAAe,cAAAd,sBAAA,uBAAzBA,sBAAA,CAA2BmB,OAAO,KAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;MAEhQ,MAAMC,iBAAiB,GAAG;QACxBC,aAAa,GAAArB,sBAAA,GAAEvC,SAAS,CAACoD,eAAe,cAAAb,sBAAA,uBAAzBA,sBAAA,CAA2BsB,KAAK;QAC/CC,YAAY,EAAE,GAAG,EAAAtB,sBAAA,GAAAxC,SAAS,CAACoD,eAAe,cAAAZ,sBAAA,uBAAzBA,sBAAA,CAA2BuB,SAAS,KAAI,EAAE,IAAI,EAAAtB,sBAAA,GAAAzC,SAAS,CAACoD,eAAe,cAAAX,sBAAA,uBAAzBA,sBAAA,CAA2BuB,QAAQ,KAAI,EAAE,EAAE,CAACN,IAAI,CAAC,CAAC;QACjHO,aAAa,EAAE,EAAAvB,sBAAA,GAAA1C,SAAS,CAACoD,eAAe,cAAAV,sBAAA,uBAAzBA,sBAAA,CAA2BwB,KAAK,KAAIC,SAAS;QAAE;QAC9Df,eAAe,EAAED,qBAAqB,CAACiB,MAAM,GAAG,EAAE,GAAGjB,qBAAqB,GAAG,6DAA6D;QAC1IkB,cAAc,EAAElB,qBAAqB,CAACiB,MAAM,GAAG,EAAE,GAAGjB,qBAAqB,GAAG,0BAA0B;QACtGmB,KAAK,EAAEtE,SAAS,CAACsE,KAAK,CAACC,GAAG,CAACC,IAAI;UAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;UAAA,OAAK;YAClCC,SAAS,EAAE,EAAAL,aAAA,GAAAD,IAAI,CAACO,OAAO,cAAAN,aAAA,uBAAZA,aAAA,CAAcvD,EAAE,KAAIsD,IAAI,CAACM,SAAS,IAAIN,IAAI,CAACtD,EAAE,IAAI,sCAAsC;YAAE;YACpG8D,QAAQ,EAAER,IAAI,CAACQ,QAAQ,IAAI,CAAC;YAC5BC,SAAS,EAAET,IAAI,CAACU,KAAK,MAAAR,cAAA,GAAIF,IAAI,CAACO,OAAO,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,KAAK,OAAAP,cAAA,GAAIH,IAAI,CAACO,OAAO,cAAAJ,cAAA,uBAAZA,cAAA,CAAcQ,aAAa,KAAI,CAAC;YAChFC,UAAU,EAAE,CAACZ,IAAI,CAACQ,QAAQ,IAAI,CAAC,KAAKR,IAAI,CAACU,KAAK,MAAAN,cAAA,GAAIJ,IAAI,CAACO,OAAO,cAAAH,cAAA,uBAAZA,cAAA,CAAcM,KAAK,OAAAL,cAAA,GAAIL,IAAI,CAACO,OAAO,cAAAF,cAAA,uBAAZA,cAAA,CAAcM,aAAa,KAAI,CAAC,CAAC;YAC1GE,mBAAmB,EAAEb,IAAI,CAACc,aAAa,GAAGC,IAAI,CAACC,SAAS,CAAChB,IAAI,CAACc,aAAa,CAAC,GAAGnB;UACjF,CAAC;QAAA,CAAC,CAAC;QACHsB,QAAQ,EAAEzF,SAAS,CAAC0F,QAAQ,IAAI1F,SAAS,CAACuB,WAAW,IAAI,CAAC;QAC1DoE,SAAS,EAAE3F,SAAS,CAAC4F,GAAG,IAAI,CAAC;QAC7BC,cAAc,EAAE7F,SAAS,CAAC8F,QAAQ,IAAI,CAAC;QACvCC,cAAc,EAAE,CAAC;QACjBxE,WAAW,EAAEvB,SAAS,CAACuB,WAAW,IAAI,CAAC;QACvCyE,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE;MACT,CAAC;;MAED;MACAjE,OAAO,CAACkE,GAAG,CAAC,iCAAiC,EAAEX,IAAI,CAACC,SAAS,CAAC7B,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE1F;MACA,MAAMwC,WAAW,GAAG,MAAMzG,SAAS,CAAC0G,IAAI,CAAC,aAAa,EAAEzC,iBAAiB,CAAC;MAE1E,IAAI,CAACwC,WAAW,CAACrE,OAAO,EAAE;QACxB,MAAM,IAAIuE,KAAK,CAACF,WAAW,CAACG,OAAO,IAAI,wBAAwB,CAAC;MAClE;MAEA,MAAMC,YAAY,GAAGJ,WAAW,CAACpE,IAAI,CAACyE,KAAK;MAC3CxE,OAAO,CAACkE,GAAG,CAAC,2BAA2B,EAAEK,YAAY,CAACE,WAAW,CAAC;;MAElE;MACA,MAAMC,WAAW,GAAG;QAClBC,OAAO,EAAEJ,YAAY,CAACE,WAAW;QACjClF,WAAW,EAAEG,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC;QAAE;QACtD+C,KAAK,EAAEtE,SAAS,CAACsE,KAAK,IAAI,EAAE;QAC5BsC,QAAQ,EAAE;UACRzF,IAAI,EAAE,EAAAwB,sBAAA,GAAA3C,SAAS,CAACoD,eAAe,cAAAT,sBAAA,uBAAzBA,sBAAA,CAA2BoB,SAAS,IAAG,GAAG,KAAAnB,sBAAA,GAAG5C,SAAS,CAACoD,eAAe,cAAAR,sBAAA,uBAAzBA,sBAAA,CAA2BoB,QAAQ;UACtFH,KAAK,GAAAhB,uBAAA,GAAE7C,SAAS,CAACoD,eAAe,cAAAP,uBAAA,uBAAzBA,uBAAA,CAA2BgB,KAAK;UACvCK,KAAK,GAAApB,uBAAA,GAAE9C,SAAS,CAACoD,eAAe,cAAAN,uBAAA,uBAAzBA,uBAAA,CAA2BoB;QACpC,CAAC;QACDd,eAAe,EAAEpD,SAAS,CAACoD,eAAe,IAAI,CAAC,CAAC;QAChDyD,QAAQ,EAAE;UACRC,aAAa,EAAE/F,cAAc;UAC7BgG,MAAM,EAAE,qBAAqB;UAC7BC,cAAc,EAAET,YAAY,CAACU,OAAO;UACpC3C,KAAK,EAAEiB,IAAI,CAACC,SAAS,CAACxF,SAAS,CAACsE,KAAK,CAAC;UACtClB,eAAe,EAAEmC,IAAI,CAACC,SAAS,CAACxF,SAAS,CAACoD,eAAe,CAAC;UAC1DwD,QAAQ,EAAErB,IAAI,CAACC,SAAS,CAAC;YACvBrE,IAAI,EAAE,EAAA4B,uBAAA,GAAA/C,SAAS,CAACoD,eAAe,cAAAL,uBAAA,uBAAzBA,uBAAA,CAA2BgB,SAAS,IAAG,GAAG,KAAAf,uBAAA,GAAGhD,SAAS,CAACoD,eAAe,cAAAJ,uBAAA,uBAAzBA,uBAAA,CAA2BgB,QAAQ;YACtFH,KAAK,GAAAZ,uBAAA,GAAEjD,SAAS,CAACoD,eAAe,cAAAH,uBAAA,uBAAzBA,uBAAA,CAA2BY,KAAK;YACvCK,KAAK,GAAAhB,uBAAA,GAAElD,SAAS,CAACoD,eAAe,cAAAF,uBAAA,uBAAzBA,uBAAA,CAA2BgB;UACpC,CAAC,CAAC;UACFyB,SAAS,EAAE3F,SAAS,CAAC4F,GAAG,IAAI,CAAC;UAC7BC,cAAc,EAAE7F,SAAS,CAAC8F,QAAQ,IAAI,CAAC;UACvCC,cAAc,EAAE,CAAC;UACjB,GAAG/F,SAAS,CAAC6G;QACf;MACF,CAAC;MAED,MAAMjF,MAAM,GAAG,MAAMnC,cAAc,CAACyH,iBAAiB,CAACR,WAAW,CAAC;;MAElE;MACA,IAAI9E,MAAM,IAAIA,MAAM,CAACnB,WAAW,IAAImB,MAAM,CAACnB,WAAW,CAAC0G,GAAG,EAAE;QAC1DzG,cAAc,CAACkB,MAAM,CAACnB,WAAW,CAAC;;QAElC;QACA2G,MAAM,CAACC,IAAI,CAACzF,MAAM,CAACnB,WAAW,CAAC0G,GAAG,EAAE,QAAQ,CAAC;;QAE7C;QACAG,yBAAyB,CAACf,YAAY,CAACE,WAAW,EAAE7E,MAAM,CAACnB,WAAW,CAACS,EAAE,CAAC;QAE1E,IAAIjB,SAAS,EAAE;UACbA,SAAS,CAAC;YACR,GAAG2B,MAAM;YACT2F,YAAY,EAAEhB;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAACD,KAAK,CAAC2F,OAAO,IAAI,+BAA+B,CAAC;MAC1D,IAAIpG,OAAO,EAAE;QACXA,OAAO,CAACS,KAAK,CAAC;MAChB;IACF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8G,yBAAyB,GAAGA,CAACX,OAAO,EAAEa,aAAa,KAAK;IAC5D,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMlI,cAAc,CAACmI,gBAAgB,CAACjB,OAAO,EAAEa,aAAa,CAAC;;QAElF;QACA,IAAIG,YAAY,KAAKA,YAAY,CAAC7F,OAAO,IAAI6F,YAAY,CAACE,MAAM,CAAC,EAAE;UAAA,IAAAC,kBAAA;UACjE,MAAMD,MAAM,GAAG,EAAAC,kBAAA,GAAAH,YAAY,CAAC5F,IAAI,cAAA+F,kBAAA,uBAAjBA,kBAAA,CAAmBD,MAAM,KAAIF,YAAY,CAACE,MAAM;UAE/D,IAAIA,MAAM,KAAK,MAAM,EAAE;YACrBE,aAAa,CAACN,YAAY,CAAC;;YAE3B;YACAnH,SAAS,CAAC,CAAC;;YAEX;YACAD,QAAQ,CAAC,gBAAgB,EAAE;cACzBkD,KAAK,EAAE;gBACLiD,KAAK,EAAE;kBACLtF,EAAE,EAAEyF,OAAO;kBACXqB,YAAY,EAAErB,OAAO;kBACrBsB,YAAY,EAAEjI,SAAS,CAACuB,WAAW;kBACnCyE,QAAQ,EAAE,KAAK;kBACf6B,MAAM,EAAE,MAAM;kBACdK,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACpCC,gBAAgB,EAAErI,SAAS,CAACoD,eAAe;kBAC3CkB,KAAK,EAAEtE,SAAS,CAACsE;gBACnB,CAAC;gBACDgC,OAAO,EAAE,8EAA8E;gBACvFgC,aAAa,EAAE,WAAW;gBAC1BxB,aAAa,EAAE;cACjB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIe,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,SAAS,EAAE;YAChFE,aAAa,CAACN,YAAY,CAAC;YAC3B7G,QAAQ,CAAC,WAAWiH,MAAM,qBAAqB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAOlH,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAIA,KAAK,CAAC2F,OAAO,IAAI3F,KAAK,CAAC2F,OAAO,CAACiC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAChEvG,OAAO,CAACkE,GAAG,CAAC,gDAAgD,CAAC;UAC7D6B,aAAa,CAACN,YAAY,CAAC;QAC7B;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACAe,UAAU,CAAC,MAAM;MACfT,aAAa,CAACN,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhI,WAAW,EAAE;MACfhB,cAAc,CAACiJ,iBAAiB,CAACjI,WAAW,CAACS,EAAE,CAAC,CAACyH,KAAK,CAAC3G,OAAO,CAACrB,KAAK,CAAC;IACvE;IACA,IAAIR,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMyI,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBhD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACiD,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACEjJ,OAAA;IAAKsJ,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCvJ,OAAA;MAAKsJ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvJ,OAAA;QAAAuJ,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B3J,OAAA;QAAAuJ,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGN3J,OAAA;MAAKsJ,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BvJ,OAAA;QAAAuJ,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB3J,OAAA;QAAKsJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvJ,OAAA;UAAAuJ,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtB3J,OAAA;UAAAuJ,QAAA,EAAOP,cAAc,CAAC5I,SAAS,CAACuB,WAAW,IAAI,CAAC;QAAC;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACL1I,IAAI,iBACHjB,OAAA,CAAAE,SAAA;QAAAqJ,QAAA,gBACEvJ,OAAA;UAAKsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvJ,OAAA;YAAAuJ,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB3J,OAAA;YAAAuJ,QAAA,EAAOP,cAAc,CAAC/H,IAAI,CAAC2I,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN3J,OAAA;UAAKsJ,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvJ,OAAA;YAAAuJ,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1B3J,OAAA;YAAAuJ,QAAA,EAAOP,cAAc,CAAC/H,IAAI,CAACgI,MAAM,GAAGhI,IAAI,CAAC2I,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3J,OAAA;MAAKsJ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvJ,OAAA;QAAAuJ,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B3J,OAAA;QAAKsJ,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBlI,cAAc,CAACsD,GAAG,CAAEkF,MAAM,iBACzB7J,OAAA;UAEEsJ,SAAS,EAAE,kBAAkBnI,cAAc,KAAK0I,MAAM,CAACvI,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9EwI,OAAO,EAAEA,CAAA,KAAM1I,iBAAiB,CAACyI,MAAM,CAACvI,EAAE,CAAE;UAAAiI,QAAA,gBAE5CvJ,OAAA;YAAKsJ,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,MAAM,CAACrI;UAAI;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChD3J,OAAA;YAAKsJ,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BvJ,OAAA;cAAKsJ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEM,MAAM,CAACtI;YAAI;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD3J,OAAA;cAAKsJ,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEM,MAAM,CAACpI;YAAW;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D3J,OAAA;cAAKsJ,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,OAAK,EAACM,MAAM,CAACnI,GAAG;YAAA;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GATDE,MAAM,CAACvI,EAAE;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5I,KAAK,iBACJf,OAAA;MAAKsJ,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BvJ,OAAA;QAAMsJ,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtC3J,OAAA;QAAAuJ,QAAA,EAAOxI;MAAK;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD3J,OAAA;MAAKsJ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvJ,OAAA;QACEsJ,SAAS,EAAC,mBAAmB;QAC7BQ,OAAO,EAAEjB,YAAa;QACtBkB,QAAQ,EAAEpJ,OAAQ;QAAA4I,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3J,OAAA;QACEsJ,SAAS,EAAC,iBAAiB;QAC3BQ,OAAO,EAAEzH,uBAAwB;QACjC0H,QAAQ,EAAEpJ,OAAO,IAAI,EAACP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,CAAC;QAAA4H,QAAA,EAE5C5I,OAAO,gBACNX,OAAA,CAAAE,SAAA;UAAAqJ,QAAA,gBACEvJ,OAAA;YAAMsJ,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA,eAAE,CAAC,gBAEH3J,OAAA,CAAAE,SAAA;UAAAqJ,QAAA,gBACEvJ,OAAA;YAAMsJ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,QACpC,EAAC1I,IAAI,GAAG+H,cAAc,CAAC/H,IAAI,CAACgI,MAAM,GAAGhI,IAAI,CAAC2I,QAAQ,CAAC,GAAGZ,cAAc,CAAC5I,SAAS,CAACuB,WAAW,IAAI,CAAC,CAAC;QAAA,eACpG;MACH;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL9I,WAAW,iBACVb,OAAA;MAAKsJ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCvJ,OAAA;QAAKsJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BvJ,OAAA;UAAMsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC3J,OAAA;UAAAuJ,QAAA,EAAM;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACN3J,OAAA;QAAAuJ,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ3J,OAAA;QAAKsJ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvJ,OAAA;UAAKsJ,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvJ,OAAA;YAAAuJ,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvB3J,OAAA;YAAMsJ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAE1I,WAAW,CAACmJ;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN3J,OAAA;UAAKsJ,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvJ,OAAA;YAAAuJ,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpB3J,OAAA;YAAMsJ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE1I,WAAW,CAACoH;UAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3J,OAAA;MAAKsJ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvJ,OAAA;QAAKsJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvJ,OAAA;UAAMsJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzC3J,OAAA;UAAAuJ,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACN3J,OAAA;QAAAuJ,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnJ,EAAA,CApWIL,gBAAgB;EAAA,QACHR,WAAW,EACNC,OAAO;AAAA;AAAAqK,EAAA,GAFzB9J,gBAAgB;AAsWtB,eAAeA,gBAAgB;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}