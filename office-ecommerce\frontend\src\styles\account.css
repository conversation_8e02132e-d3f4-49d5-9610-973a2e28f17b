/* Account Management System Styles */
/* Following the existing design system with golden yellow accent (#F0B21B) */

.account-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.account-header {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.account-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, #e6a632 100%);
}

.account-header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #e6a632 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    box-shadow: 0 4px 16px rgba(240, 178, 27, 0.3);
}

.user-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.02em;
}

.user-email {
    font-size: 1.1rem;
    color: var(--text-light);
    margin: 0 0 0.5rem 0;
}

.user-role {
    display: inline-block;
    background: rgba(240, 178, 27, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: capitalize;
}

.account-content {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
    align-items: start;
}

.account-sidebar {
    background: var(--white);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    position: sticky;
    top: 2rem;
}

.account-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.account-nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.25rem;
    background: none;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-light);
    width: 100%;
}

.account-nav-item:hover {
    background: rgba(240, 178, 27, 0.08);
    color: var(--text-dark);
    transform: translateX(4px);
}

.account-nav-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #e6a632 100%);
    color: var(--white);
    box-shadow: 0 4px 16px rgba(240, 178, 27, 0.3);
}

.account-nav-item.active:hover {
    transform: translateX(0);
}

.nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.nav-label {
    font-weight: 600;
}

.account-main {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.account-section {
    padding: 2rem;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: 1rem;
    color: var(--text-light);
    margin: 0.5rem 0 0 0;
}

/* Form Styles */
.account-form {
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-input {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
    font-family: inherit;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-input:disabled {
    background: var(--background-light);
    color: var(--text-light);
    cursor: not-allowed;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #e6a632 100%);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    justify-content: center;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 178, 27, 0.4);
}

.btn-secondary {
    background: var(--white);
    color: var(--text-dark);
    border: 2px solid var(--border-color);
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    justify-content: center;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    justify-content: center;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-color);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .account-page {
        padding: 1rem 0;
    }

    .account-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .account-sidebar {
        position: static;
        order: 2;
    }

    .account-nav {
        flex-direction: row;
        overflow-x: auto;
        gap: 0.25rem;
        padding-bottom: 0.5rem;
    }

    .account-nav-item {
        flex-shrink: 0;
        min-width: auto;
        padding: 0.75rem 1rem;
    }

    .nav-label {
        display: none;
    }

    .account-header {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .account-header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .user-avatar {
        width: 60px;
        height: 60px;
    }

    .user-info h1 {
        font-size: 1.5rem;
    }

    .account-section {
        padding: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .account-header {
        padding: 1rem;
    }

    .account-section {
        padding: 1rem;
    }

    .user-info h1 {
        font-size: 1.25rem;
    }
}

/* Profile Management Specific Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Message Styles */
.message {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

/* Spinner Animation */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Order History Styles */
.order-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--white);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.orders-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.order-card {
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.order-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(240, 178, 27, 0.15);
    transform: translateY(-2px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.order-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
}

.order-date {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: capitalize;
}

.order-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-light);
    border-radius: 12px;
}

.item-image {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
}

.item-details {
    flex: 1;
}

.item-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
}

.item-quantity {
    font-size: 0.875rem;
    color: var(--text-light);
    margin: 0;
}

.item-price {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.order-total {
    font-size: 1.125rem;
    color: var(--text-dark);
}

.order-actions {
    display: flex;
    gap: 0.75rem;
}

.order-actions .btn-primary,
.order-actions .btn-secondary {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: var(--text-light);
}

.loading-state .spinner {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.empty-state svg {
    margin-bottom: 1rem;
    color: var(--text-light);
}

.empty-state h3 {
    font-size: 1.25rem;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    margin: 0;
    max-width: 400px;
}

/* Address Book Styles */
.address-form-container {
    background: var(--background-light);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.form-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.address-form {
    display: grid;
    gap: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-dark);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.addresses-list {
    margin-top: 2rem;
}

.address-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.address-card {
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.address-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(240, 178, 27, 0.15);
    transform: translateY(-2px);
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: capitalize;
}

.type-badge.shipping {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.type-badge.billing {
    background: rgba(155, 89, 182, 0.1);
    color: #9b59b6;
    border: 1px solid rgba(155, 89, 182, 0.2);
}

.default-badge {
    background: rgba(240, 178, 27, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.address-content {
    margin-bottom: 1.5rem;
}

.address-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.address-company {
    font-size: 0.875rem;
    color: var(--text-light);
    font-style: italic;
    margin: 0 0 0.5rem 0;
}

.address-text {
    color: var(--text-dark);
    line-height: 1.5;
    margin: 0 0 0.5rem 0;
}

.address-phone {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0;
}

.address-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn.edit {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.action-btn.edit:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: translateY(-1px);
}

.action-btn.default {
    background: rgba(240, 178, 27, 0.1);
    color: var(--primary-color);
}

.action-btn.default:hover {
    background: rgba(240, 178, 27, 0.2);
    transform: translateY(-1px);
}

.action-btn.delete {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.action-btn.delete:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: translateY(-1px);
}

/* Account Preferences Styles */
.preferences-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.preference-section {
    background: var(--background-light);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.preference-section:hover {
    border-color: rgba(240, 178, 27, 0.3);
}

.preference-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.preference-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.preference-description {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.preference-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.preference-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.preference-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.1);
}

.preference-info {
    flex: 1;
}

.preference-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
    display: block;
}

.preference-desc {
    font-size: 0.875rem;
    color: var(--text-light);
    margin: 0;
    line-height: 1.4;
}

/* Toggle Switch Styles */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    flex-shrink: 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.2);
}

/* Select Styles */
.preference-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--white);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    flex-shrink: 0;
}

.preference-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.preference-select:hover {
    border-color: var(--primary-color);
}

/* Mobile Responsive for Preferences */
@media (max-width: 768px) {
    .preference-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .preference-select {
        min-width: auto;
        width: 100%;
    }

    .toggle-switch {
        align-self: flex-start;
    }

    .preference-section {
        padding: 1.5rem;
    }

    .preferences-form {
        gap: 1.5rem;
    }
}

/* Security Settings Styles */
.security-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.security-card {
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.security-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(240, 178, 27, 0.15);
}

.security-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem;
    background: var(--background-light);
    border-bottom: 1px solid var(--border-color);
}

.security-info {
    flex: 1;
}

.security-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
}

.security-description {
    color: var(--text-light);
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.security-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.enabled {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.status-indicator.enabled::before {
    content: '●';
    color: #27ae60;
}

.status-indicator.disabled {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.status-indicator.disabled::before {
    content: '●';
    color: #e74c3c;
}

.security-content {
    padding: 2rem;
}

.password-form,
.verification-form {
    display: grid;
    gap: 1.5rem;
}

.password-strength {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-label {
    font-size: 0.875rem;
    font-weight: 600;
    min-width: 80px;
    text-align: right;
}

/* Two-Factor Authentication Styles */
.two-factor-setup {
    text-align: center;
}

.two-factor-setup h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.two-factor-setup p {
    color: var(--text-light);
    margin: 0 0 2rem 0;
}

.qr-code-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 200px;
    margin: 0 auto 2rem auto;
    background: var(--background-light);
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    color: var(--text-light);
}

.qr-code-placeholder p {
    margin: 0.5rem 0 0 0;
    font-size: 0.875rem;
}

/* Sessions List Styles */
.sessions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--background-light);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.session-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.1);
}

.session-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.session-device {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.device-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
}

.device-location {
    font-size: 0.875rem;
    color: var(--text-light);
    margin: 0 0 0.25rem 0;
}

.device-details {
    font-size: 0.75rem;
    color: var(--text-light);
    margin: 0;
}

.current-session {
    background: rgba(240, 178, 27, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.session-terminate {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Mobile Responsive for Security Settings */
@media (max-width: 768px) {
    .security-card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .security-content {
        padding: 1.5rem;
    }

    .session-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .session-info {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .session-device {
        flex-direction: row;
        align-items: center;
    }

    .current-session {
        align-self: flex-start;
    }

    .password-strength {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .strength-label {
        text-align: left;
        min-width: auto;
    }
}
