const Order = require('../models/Order');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class OrderService {
  constructor() {
    this.orderModel = new Order();
  }

  /**
   * Create a verified order from successful payment
   * @param {Object} params - Order creation parameters
   * @param {string} params.orderId - Order ID from payment metadata
   * @param {Object} params.paymentData - Payment data from PayMongo
   * @param {string} params.paymentStatus - Payment status ('Paid')
   * @param {string} params.orderStatus - Order status ('Confirmed' or 'Processing')
   * @returns {Promise<Object>} Order creation result
   */
  async createVerifiedOrder({ orderId, paymentData, paymentStatus, orderStatus }) {
    try {
      logger.info(`Creating verified order for payment: ${orderId}`);

      // Extract order information from payment metadata
      const metadata = paymentData.metadata || {};
      const items = metadata.items ? JSON.parse(metadata.items) : [];
      const shippingAddress = metadata.shippingAddress ? JSON.parse(metadata.shippingAddress) : {};
      const customer = metadata.customer ? JSON.parse(metadata.customer) : {};

      // Validate required data
      if (!items || items.length === 0) {
        throw new Error('No order items found in payment metadata');
      }

      if (!customer.email || !customer.name) {
        throw new Error('Customer information missing from payment metadata');
      }

      // Check if order already exists
      const existingOrder = await this.orderModel.findByOrderNumber(orderId);
      if (existingOrder) {
        logger.info(`Order ${orderId} already exists, updating payment status`);
        
        // Update existing order with payment confirmation
        const updatedOrder = await this.orderModel.updateById(existingOrder.OrderID, {
          PaymentStatus: paymentStatus,
          OrderStatus: orderStatus,
          UpdatedAt: new Date()
        });

        return {
          success: true,
          data: {
            orderId: updatedOrder.OrderID,
            orderNumber: updatedOrder.OrderNumber,
            customerName: updatedOrder.CustomerName,
            totalAmount: updatedOrder.TotalAmount,
            isUpdate: true
          }
        };
      }

      // Prepare order data for new order creation
      const orderData = {
        OrderID: uuidv4(),
        CustomerEmail: customer.email,
        CustomerName: customer.name,
        CustomerPhone: customer.phone || shippingAddress.phone || '',
        ShippingAddress: JSON.stringify(shippingAddress),
        BillingAddress: JSON.stringify(shippingAddress), // Use shipping as billing if not provided
        OrderStatus: orderStatus,
        PaymentStatus: paymentStatus,
        SubTotal: this.calculateSubTotal(items),
        TaxAmount: metadata.taxAmount || 0,
        ShippingAmount: metadata.shippingAmount || 0,
        DiscountAmount: metadata.discountAmount || 0,
        TotalAmount: paymentData.amount / 100, // Convert from centavos to PHP
        Currency: 'PHP',
        Notes: `Order created from verified PayMongo payment: ${paymentData.id}`
      };

      // Prepare order items
      const orderItems = items.map(item => ({
        VariantID: item.variantId || item.id,
        Quantity: item.quantity || 1,
        UnitPrice: item.unitPrice || item.price || 0,
        TotalPrice: (item.quantity || 1) * (item.unitPrice || item.price || 0),
        CustomConfiguration: item.customConfiguration ? JSON.stringify(item.customConfiguration) : null
      }));

      // Create order with inventory management
      const result = await this.orderModel.createOrderWithInventory(orderData, orderItems);

      logger.info(`✅ Verified order created successfully: ${result.order.OrderNumber}`);

      return {
        success: true,
        data: {
          orderId: result.order.OrderID,
          orderNumber: result.order.OrderNumber,
          customerName: result.order.CustomerName,
          totalAmount: result.order.TotalAmount,
          isUpdate: false
        }
      };

    } catch (error) {
      logger.error('Error creating verified order:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate subtotal from items
   * @param {Array} items - Order items
   * @returns {number} Subtotal amount
   */
  calculateSubTotal(items) {
    return items.reduce((total, item) => {
      const quantity = item.quantity || 1;
      const price = item.unitPrice || item.price || 0;
      return total + (quantity * price);
    }, 0);
  }

  /**
   * Update order status and emit real-time notifications
   * @param {string} orderId - Order ID
   * @param {string} newStatus - New order status
   * @param {string} notes - Optional notes
   * @returns {Promise<Object>} Update result
   */
  async updateOrderStatus(orderId, newStatus, notes = null) {
    try {
      const updatedOrder = await this.orderModel.updateOrderStatus(orderId, newStatus, notes);
      
      // Emit real-time update
      const websocketService = require('./websocketService');
      websocketService.emitOrderUpdate({
        orderId: updatedOrder.OrderID,
        orderNumber: updatedOrder.OrderNumber,
        status: updatedOrder.OrderStatus,
        paymentStatus: updatedOrder.PaymentStatus,
        timestamp: new Date().toISOString()
      });

      logger.info(`Order status updated: ${updatedOrder.OrderNumber} -> ${newStatus}`);

      return {
        success: true,
        data: updatedOrder
      };
    } catch (error) {
      logger.error('Error updating order status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get order by ID with full details
   * @param {string} orderId - Order ID
   * @returns {Promise<Object>} Order details
   */
  async getOrderById(orderId) {
    try {
      const order = await this.orderModel.getOrderWithDetails(orderId);
      return {
        success: true,
        data: order
      };
    } catch (error) {
      logger.error('Error getting order by ID:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process payment completion for existing order
   * @param {string} orderId - Order ID
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} Processing result
   */
  async processPaymentCompletion(orderId, paymentData) {
    try {
      // Update order payment status
      const order = await this.orderModel.findById(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Update payment status and order status
      const updatedOrder = await this.orderModel.updateById(orderId, {
        PaymentStatus: 'Paid',
        OrderStatus: order.OrderStatus === 'Pending' ? 'Confirmed' : order.OrderStatus,
        UpdatedAt: new Date()
      });

      // Emit real-time notification
      const websocketService = require('./websocketService');
      websocketService.emitOrderUpdate({
        orderId: updatedOrder.OrderID,
        orderNumber: updatedOrder.OrderNumber,
        status: updatedOrder.OrderStatus,
        paymentStatus: updatedOrder.PaymentStatus,
        customerName: updatedOrder.CustomerName,
        totalAmount: updatedOrder.TotalAmount,
        timestamp: new Date().toISOString()
      });

      logger.info(`Payment completed for order: ${updatedOrder.OrderNumber}`);

      return {
        success: true,
        data: updatedOrder
      };
    } catch (error) {
      logger.error('Error processing payment completion:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
const orderService = new OrderService();

module.exports = orderService;
