.checkout-page {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 2rem 0;
}

.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.checkout-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-step.active {
    color: #F0B21B;
}

.progress-step.completed {
    color: #059669;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid currentColor;
    background: white;
    transition: all 0.2s ease;
}

.progress-step.active .step-number {
    background: #F0B21B;
    color: white;
    border-color: #F0B21B;
}

.progress-step.completed .step-number {
    background: #059669;
    color: white;
    border-color: #059669;
}

.progress-line {
    width: 100px;
    height: 2px;
    background: #e5e7eb;
    margin: 0 2rem;
}

.error-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.error-icon {
    font-size: 1.25rem;
    color: #ef4444;
}

.error-text {
    color: #dc2626;
    font-weight: 500;
}

.checkout-step {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.step-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1.5rem;
}

.address-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-row:has(.form-group:nth-child(3)) {
    grid-template-columns: 1fr 1fr 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-group input:disabled {
    background: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #F0B21B;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #d4a017;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #e5e7eb;
    color: #374151;
}

.btn-secondary:hover:not(:disabled) {
    background: #d1d5db;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.order-summary {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.order-summary h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.summary-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.item-name {
    font-weight: 500;
    color: #1a1a1a;
    font-size: 0.9rem;
}

.item-quantity {
    font-size: 0.8rem;
    color: #6b7280;
}

.item-price {
    font-weight: 600;
    color: #F0B21B;
}

.summary-total {
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
}

.total-amount {
    color: #F0B21B;
    font-size: 1.25rem;
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin: 0 auto;
}

.empty-cart h2 {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .checkout-page {
        padding: 1rem 0;
    }
    
    .checkout-container {
        padding: 0 0.5rem;
    }
    
    .checkout-progress {
        margin-bottom: 2rem;
        padding: 1.5rem 1rem;
    }
    
    .progress-line {
        width: 60px;
        margin: 0 1rem;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .progress-step span {
        font-size: 0.8rem;
    }
    
    .checkout-step {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .step-content {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn {
        width: 100%;
    }
    
    .order-summary {
        position: static;
        order: -1;
        padding: 1rem;
    }
}

/* Animation for step transitions */
.step-content {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
