/* Modern Checkout Page Design */
.checkout-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.checkout-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: grid;
    grid-template-columns: 1fr 420px;
    gap: 2rem;
    align-items: start;
}

/* Main checkout form area */
.checkout-form-area {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

/* Modern Section Design */
.checkout-section {
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.3s ease;
}

.checkout-section:last-child {
    border-bottom: none;
}

.checkout-section:hover {
    background: rgba(248, 250, 252, 0.5);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.75rem 2rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    font-weight: 600;
    color: #1e293b;
    font-size: 1rem;
    letter-spacing: -0.025em;
}

.section-header svg {
    color: #64748b;
    transition: color 0.3s ease;
}

.checkout-section:hover .section-header svg {
    color: #F0B21B;
}

.section-number {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #F0B21B 0%, #e6a017 100%);
    box-shadow: 0 4px 8px rgba(240, 178, 27, 0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
}

.section-content {
    padding: 2.5rem;
    background: white;
}

/* Modern Form Design */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
    margin-bottom: 1.75rem;
}

.form-row.single {
    grid-template-columns: 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group label {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
    color: #1e293b;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 4px rgba(240, 178, 27, 0.15);
    transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #cbd5e1;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Modern Order Summary Sidebar */
.order-summary {
    background: white;
    border-radius: 16px;
    padding: 0;
    height: fit-content;
    position: sticky;
    top: 2rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
}

.order-summary-header {
    padding: 2rem 2rem 1.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.order-summary-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.order-summary-header .summary-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #F0B21B 0%, #e6a017 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 700;
    box-shadow: 0 4px 8px rgba(240, 178, 27, 0.3);
}

.order-summary-header .summary-icon svg {
    color: white;
    width: 16px;
    height: 16px;
}

.order-summary-content {
    padding: 2rem;
}

/* Cart items in order summary */
.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
    background: #f3f4f6;
}

.cart-item-details {
    flex: 1;
}

.cart-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.cart-item-quantity {
    font-size: 12px;
    color: #6b7280;
}

.cart-item-price {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

/* Order totals */
.order-totals {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 14px;
}

.total-row.final {
    font-weight: 600;
    font-size: 16px;
    color: #374151;
    border-top: 1px solid #e5e7eb;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

/* Modern Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-method {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.payment-method::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: transparent;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: #F0B21B;
    background: #fffbf0;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(240, 178, 27, 0.2);
}

.payment-method:hover::before {
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
}

.payment-method.selected {
    border-color: #F0B21B;
    background: #fffbf0;
    box-shadow: 0 8px 25px -5px rgba(240, 178, 27, 0.25);
}

.payment-method.selected::before {
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
}

.payment-method-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.payment-method-header svg {
    color: #64748b;
    transition: color 0.3s ease;
}

.payment-method:hover .payment-method-header svg,
.payment-method.selected .payment-method-header svg {
    color: #F0B21B;
}

.payment-method-radio {
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e1;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.payment-method.selected .payment-method-radio {
    border-color: #F0B21B;
    background: #F0B21B;
}

.payment-method.selected .payment-method-radio::after {
    content: '';
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.payment-method-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 16px;
    letter-spacing: -0.025em;
}

.payment-method-description {
    font-size: 14px;
    color: #64748b;
    margin-left: 2rem;
    line-height: 1.5;
}

/* Place Order Button */
.place-order-btn {
    width: 100%;
    background: #F0B21B;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 1rem 2rem;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 2rem;
}

.place-order-btn:hover {
    background: #d4a017;
}

.place-order-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        max-width: 800px;
    }

    .order-summary {
        order: -1;
        position: static;
    }
}

@media (max-width: 768px) {
    .checkout-container {
        padding: 0 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .section-header {
        padding: 1rem 1.5rem;
    }

    .section-content {
        padding: 1.5rem;
    }

    .order-summary-header {
        padding: 1rem 1.5rem;
    }

    .order-summary-content {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .checkout-page {
        padding: 1rem 0;
    }

    .section-header {
        padding: 0.75rem 1rem;
        font-size: 14px;
    }

    .section-content {
        padding: 1rem;
    }

    .order-summary-header {
        padding: 0.75rem 1rem;
    }

    .order-summary-content {
        padding: 1rem;
    }

    .cart-item {
        padding: 0.75rem 0;
    }

    .cart-item-image {
        width: 50px;
        height: 50px;
    }
}

.error-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.error-icon {
    font-size: 1.25rem;
    color: #ef4444;
}

.error-text {
    color: #dc2626;
    font-weight: 500;
}

/* Removed old step-based styles - now using section-based layout */

.address-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-row:has(.form-group:nth-child(3)) {
    grid-template-columns: 1fr 1fr 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-group input:disabled {
    background: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #F0B21B;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #d4a017;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #e5e7eb;
    color: #374151;
}

.btn-secondary:hover:not(:disabled) {
    background: #d1d5db;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.order-summary {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.order-summary h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.summary-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.item-name {
    font-weight: 500;
    color: #1a1a1a;
    font-size: 0.9rem;
}

.item-quantity {
    font-size: 0.8rem;
    color: #6b7280;
}

.item-price {
    font-weight: 600;
    color: #F0B21B;
}

.summary-total {
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
}

.total-amount {
    color: #F0B21B;
    font-size: 1.25rem;
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin: 0 auto;
}

.empty-cart h2 {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Modern Mobile Responsiveness */
@media (max-width: 1024px) {
    .checkout-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 800px;
    }

    .order-summary {
        order: -1;
        position: static;
    }
}

@media (max-width: 768px) {
    .checkout-page {
        padding: 1rem 0;
    }

    .checkout-container {
        padding: 0 1rem;
        gap: 1rem;
    }

    .checkout-form-area {
        border-radius: 12px;
    }

    .section-header {
        padding: 1.25rem 1.5rem;
        font-size: 15px;
    }

    .section-number {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .section-content {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.25rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.875rem 1rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .payment-method {
        padding: 1.25rem;
    }

    .payment-method-header {
        gap: 0.75rem;
    }

    .payment-method-name {
        font-size: 15px;
    }

    .order-summary {
        border-radius: 12px;
        margin-bottom: 1rem;
    }

    .order-summary-header {
        padding: 1.5rem;
    }

    .order-summary-content {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .checkout-container {
        padding: 0 0.75rem;
    }

    .section-content {
        padding: 1.25rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.75rem;
    }

    .payment-method {
        padding: 1rem;
    }
}

/* Animation for step transitions */
.step-content {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
