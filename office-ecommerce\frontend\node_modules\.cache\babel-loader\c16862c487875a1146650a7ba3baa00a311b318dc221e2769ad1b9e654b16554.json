{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport { usePermissions } from '../hooks/usePermissions';\nimport '../styles/pages.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    firstName: '',\n    lastName: '',\n    phone: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login,\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Get the intended destination from location state\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      if (isLogin) {\n        const result = await login(formData.email, formData.password);\n        if (result.success) {\n          // Determine redirect destination based on role and intended destination\n          let redirectTo = from;\n\n          // If user was trying to access admin area, check permissions\n          if (from.startsWith('/admin')) {\n            if (result.user.role === 'Admin' || result.user.role === 'Employee') {\n              redirectTo = from;\n            } else {\n              redirectTo = '/'; // Redirect customers away from admin\n            }\n          } else if (from === '/') {\n            // Default redirect based on role\n            if (result.user.role === 'Admin' || result.user.role === 'Employee') {\n              redirectTo = '/admin';\n            } else {\n              redirectTo = '/';\n            }\n          }\n          navigate(redirectTo, {\n            replace: true\n          });\n        } else {\n          setError(result.error);\n        }\n      } else {\n        const result = await register(formData);\n        if (result.success) {\n          // New users are customers by default, redirect to home\n          navigate('/', {\n            replace: true\n          });\n        } else {\n          setError(result.error);\n        }\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-page-modern\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-hero-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"brand-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"48\",\n                height: \"48\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 21h18\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M5 21V7l8-4v18\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19 21V11l-6-4\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 9v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 12v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 15v.01\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"DesignXcel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Premium Office Furniture Solutions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-layout\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-form-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-header-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"auth-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"32\",\n                    height: \"32\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                      stroke: \"#808080\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 115,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\",\n                      stroke: \"#808080\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 116,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: isLogin ? 'Welcome Back' : 'Create Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"auth-subtitle\",\n                  children: isLogin ? 'Sign in to access your account and continue shopping' : 'Join us for a personalized shopping experience'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-underline\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"auth-form-modern\",\n                children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-message-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"#e74c3c\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M15 9l-6 6M9 9l6 6\",\n                      stroke: \"#e74c3c\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 41\n                }, this), !isLogin && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        children: \"First Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 144,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"18\",\n                          height: \"18\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 147,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                            cx: \"12\",\n                            cy: \"7\",\n                            r: \"4\",\n                            stroke: \"#808080\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 148,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 146,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          name: \"firstName\",\n                          placeholder: \"Enter your first name\",\n                          value: formData.firstName,\n                          onChange: handleChange,\n                          required: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 150,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-group-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        children: \"Last Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 161,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"18\",\n                          height: \"18\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\",\n                            stroke: \"#7f8c8d\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 164,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                            cx: \"12\",\n                            cy: \"7\",\n                            r: \"4\",\n                            stroke: \"#7f8c8d\",\n                            strokeWidth: \"1.5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 165,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 163,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          name: \"lastName\",\n                          placeholder: \"Enter your last name\",\n                          value: formData.lastName,\n                          onChange: handleChange,\n                          required: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 162,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: \"Phone Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"input-wrapper\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n                          stroke: \"#7f8c8d\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 182,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        name: \"phone\",\n                        placeholder: \"Enter your phone number\",\n                        value: formData.phone,\n                        onChange: handleChange\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-wrapper\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\",\n                        stroke: \"#7f8c8d\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                        points: \"22,6 12,13 2,6\",\n                        stroke: \"#7f8c8d\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      name: \"email\",\n                      placeholder: \"Enter your email address\",\n                      value: formData.email,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-wrapper\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                        x: \"3\",\n                        y: \"11\",\n                        width: \"18\",\n                        height: \"11\",\n                        rx: \"2\",\n                        ry: \"2\",\n                        stroke: \"#7f8c8d\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"16\",\n                        r: \"1\",\n                        fill: \"#7f8c8d\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M7 11V7a5 5 0 0 1 10 0v4\",\n                        stroke: \"#7f8c8d\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      name: \"password\",\n                      placeholder: \"Enter your password\",\n                      value: formData.password,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this), isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-options-modern\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/forgot-password\",\n                    className: \"forgot-link-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 237,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 6v6l4 2\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 238,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 49\n                    }, this), \"Forgot Password?\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"auth-submit-btn-modern\",\n                  disabled: loading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-content\",\n                    children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"loading-spinner\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Please wait...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: isLogin ? 'Sign In' : 'Create Account'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5 12h14M12 5l7 7-7 7\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 256,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 255,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 33\n              }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"terms-notice-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"terms-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"By creating an account, you agree to our\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    children: \"Terms of Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 45\n                  }, this), \" and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/privacy\",\n                    children: \"Privacy Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-switch-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-switch-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"switch-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"64\",\n                  height: \"64\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: isLogin ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 8 0 4 4 0 0 0-8 0M22 11l-3-3m0 0l-3 3m3-3h-6\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 45\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: isLogin ? 'New to DesignXcel?' : 'Already have an account?'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: isLogin ? 'Join thousands of satisfied customers who trust us for their office furniture needs. Create an account to unlock exclusive benefits and personalized recommendations.' : 'Welcome back! Sign in to access your saved items, order history, and continue your seamless shopping experience with us.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"switch-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Personalized recommendations' : 'Quick checkout process'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Exclusive member discounts' : 'Order tracking & history'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"1.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isLogin ? 'Priority customer support' : 'Saved favorites & wishlist'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"switch-btn-modern\",\n                onClick: () => {\n                  setIsLogin(!isLogin);\n                  setError('');\n                  setFormData({\n                    email: '',\n                    password: '',\n                    firstName: '',\n                    lastName: '',\n                    phone: ''\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: isLogin ? 'Create Account' : 'Sign In'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M5 12h14M12 5l7 7-7 7\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"FcV206aU/wdt35eYJ2M+1KHvxw8=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "useAuth", "usePermissions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "email", "password", "firstName", "lastName", "phone", "loading", "setLoading", "error", "setError", "login", "register", "navigate", "location", "from", "state", "pathname", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "redirectTo", "startsWith", "user", "role", "replace", "err", "message", "className", "children", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "onSubmit", "type", "placeholder", "onChange", "required", "points", "x", "y", "rx", "ry", "to", "disabled", "strokeLinecap", "strokeLinejoin", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport { useAuth } from '../hooks/useAuth';\r\nimport { usePermissions } from '../hooks/usePermissions';\r\nimport '../styles/pages.css';\r\n\r\nconst Login = () => {\r\n    const [isLogin, setIsLogin] = useState(true);\r\n    const [formData, setFormData] = useState({\r\n        email: '',\r\n        password: '',\r\n        firstName: '',\r\n        lastName: '',\r\n        phone: ''\r\n    });\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState('');\r\n    \r\n    const { login, register } = useAuth();\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n\r\n    // Get the intended destination from location state\r\n    const from = location.state?.from?.pathname || '/';\r\n\r\n    const handleChange = (e) => {\r\n        setFormData({\r\n            ...formData,\r\n            [e.target.name]: e.target.value\r\n        });\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setLoading(true);\r\n        setError('');\r\n\r\n        try {\r\n            if (isLogin) {\r\n                const result = await login(formData.email, formData.password);\r\n                if (result.success) {\r\n                    // Determine redirect destination based on role and intended destination\r\n                    let redirectTo = from;\r\n\r\n                    // If user was trying to access admin area, check permissions\r\n                    if (from.startsWith('/admin')) {\r\n                        if (result.user.role === 'Admin' || result.user.role === 'Employee') {\r\n                            redirectTo = from;\r\n                        } else {\r\n                            redirectTo = '/'; // Redirect customers away from admin\r\n                        }\r\n                    } else if (from === '/') {\r\n                        // Default redirect based on role\r\n                        if (result.user.role === 'Admin' || result.user.role === 'Employee') {\r\n                            redirectTo = '/admin';\r\n                        } else {\r\n                            redirectTo = '/';\r\n                        }\r\n                    }\r\n\r\n                    navigate(redirectTo, { replace: true });\r\n                } else {\r\n                    setError(result.error);\r\n                }\r\n            } else {\r\n                const result = await register(formData);\r\n                if (result.success) {\r\n                    // New users are customers by default, redirect to home\r\n                    navigate('/', { replace: true });\r\n                } else {\r\n                    setError(result.error);\r\n                }\r\n            }\r\n        } catch (err) {\r\n            setError(err.message || 'An error occurred');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"auth-page-modern\">\r\n            {/* Hero Section */}\r\n            <div className=\"auth-hero\">\r\n                <div className=\"container\">\r\n                    <div className=\"auth-hero-content\">\r\n                        <div className=\"auth-brand\">\r\n                            <div className=\"brand-icon\">\r\n                                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                    <path d=\"M3 21h18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M5 21V7l8-4v18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M19 21V11l-6-4\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 9v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 12v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                    <path d=\"M9 15v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                </svg>\r\n                            </div>\r\n                            <h1>DesignXcel</h1>\r\n                            <p>Premium Office Furniture Solutions</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"auth-main\">\r\n                <div className=\"container\">\r\n                    <div className=\"auth-layout\">\r\n                        {/* Left Side - Form */}\r\n                        <div className=\"auth-form-container\">\r\n                            <div className=\"auth-card\">\r\n                                <div className=\"auth-header-modern\">\r\n                                    <div className=\"auth-icon\">\r\n                                        <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#808080\" strokeWidth=\"2\"/>\r\n                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#808080\" strokeWidth=\"2\"/>\r\n                                        </svg>\r\n                                    </div>\r\n                                    <h2>{isLogin ? 'Welcome Back' : 'Create Account'}</h2>\r\n                                    <p className=\"auth-subtitle\">\r\n                                        {isLogin\r\n                                            ? 'Sign in to access your account and continue shopping'\r\n                                            : 'Join us for a personalized shopping experience'\r\n                                        }\r\n                                    </p>\r\n                                    <div className=\"header-underline\"></div>\r\n                                </div>\r\n\r\n                                <form onSubmit={handleSubmit} className=\"auth-form-modern\">\r\n                                    {error && (\r\n                                        <div className=\"error-message-modern\">\r\n                                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#e74c3c\" strokeWidth=\"2\"/>\r\n                                                <path d=\"M15 9l-6 6M9 9l6 6\" stroke=\"#e74c3c\" strokeWidth=\"2\"/>\r\n                                            </svg>\r\n                                            <span>{error}</span>\r\n                                        </div>\r\n                                    )}\r\n\r\n                                    {!isLogin && (\r\n                                        <>\r\n                                            <div className=\"form-row-modern\">\r\n                                                <div className=\"form-group-modern\">\r\n                                                    <label>First Name</label>\r\n                                                    <div className=\"input-wrapper\">\r\n                                                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#808080\" strokeWidth=\"1.5\"/>\r\n                                                        </svg>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            name=\"firstName\"\r\n                                                            placeholder=\"Enter your first name\"\r\n                                                            value={formData.firstName}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"form-group-modern\">\r\n                                                    <label>Last Name</label>\r\n                                                    <div className=\"input-wrapper\">\r\n                                                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                                            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                                        </svg>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            name=\"lastName\"\r\n                                                            placeholder=\"Enter your last name\"\r\n                                                            value={formData.lastName}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"form-group-modern\">\r\n                                                <label>Phone Number</label>\r\n                                                <div className=\"input-wrapper\">\r\n                                                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                        <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                                    </svg>\r\n                                                    <input\r\n                                                        type=\"tel\"\r\n                                                        name=\"phone\"\r\n                                                        placeholder=\"Enter your phone number\"\r\n                                                        value={formData.phone}\r\n                                                        onChange={handleChange}\r\n                                                    />\r\n                                                </div>\r\n                                            </div>\r\n                                        </>\r\n                                    )}\r\n\r\n                                    <div className=\"form-group-modern\">\r\n                                        <label>Email Address</label>\r\n                                        <div className=\"input-wrapper\">\r\n                                            <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                                <polyline points=\"22,6 12,13 2,6\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                            <input\r\n                                                type=\"email\"\r\n                                                name=\"email\"\r\n                                                placeholder=\"Enter your email address\"\r\n                                                value={formData.email}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"form-group-modern\">\r\n                                        <label>Password</label>\r\n                                        <div className=\"input-wrapper\">\r\n                                            <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                                <circle cx=\"12\" cy=\"16\" r=\"1\" fill=\"#7f8c8d\"/>\r\n                                                <path d=\"M7 11V7a5 5 0 0 1 10 0v4\" stroke=\"#7f8c8d\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                            <input\r\n                                                type=\"password\"\r\n                                                name=\"password\"\r\n                                                placeholder=\"Enter your password\"\r\n                                                value={formData.password}\r\n                                                onChange={handleChange}\r\n                                                required\r\n                                            />\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {isLogin && (\r\n                                        <div className=\"form-options-modern\">\r\n                                            <Link to=\"/forgot-password\" className=\"forgot-link-modern\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                    <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                                    <path d=\"M12 6v6l4 2\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                                </svg>\r\n                                                Forgot Password?\r\n                                            </Link>\r\n                                        </div>\r\n                                    )}\r\n\r\n                                    <button type=\"submit\" className=\"auth-submit-btn-modern\" disabled={loading}>\r\n                                        <div className=\"btn-content\">\r\n                                            {loading ? (\r\n                                                <>\r\n                                                    <div className=\"loading-spinner\"></div>\r\n                                                    <span>Please wait...</span>\r\n                                                </>\r\n                                            ) : (\r\n                                                <>\r\n                                                    <span>{isLogin ? 'Sign In' : 'Create Account'}</span>\r\n                                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                        <path d=\"M5 12h14M12 5l7 7-7 7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                                    </svg>\r\n                                                </>\r\n                                            )}\r\n                                        </div>\r\n                                    </button>\r\n                                </form>\r\n\r\n                                {!isLogin && (\r\n                                    <div className=\"terms-notice-modern\">\r\n                                        <div className=\"terms-icon\">\r\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                                <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                                <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                            </svg>\r\n                                        </div>\r\n                                        <p>\r\n                                            By creating an account, you agree to our{' '}\r\n                                            <Link to=\"/terms\">Terms of Service</Link> and{' '}\r\n                                            <Link to=\"/privacy\">Privacy Policy</Link>\r\n                                        </p>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Right Side - Switch */}\r\n                        <div className=\"auth-switch-container\">\r\n                            <div className=\"auth-switch-card\">\r\n                                <div className=\"switch-icon\">\r\n                                    <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                        {isLogin ? (\r\n                                            <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 8 0 4 4 0 0 0-8 0M22 11l-3-3m0 0l-3 3m3-3h-6\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                        ) : (\r\n                                            <path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\r\n                                        )}\r\n                                    </svg>\r\n                                </div>\r\n                                <h3>{isLogin ? 'New to DesignXcel?' : 'Already have an account?'}</h3>\r\n                                <p>\r\n                                    {isLogin\r\n                                        ? 'Join thousands of satisfied customers who trust us for their office furniture needs. Create an account to unlock exclusive benefits and personalized recommendations.'\r\n                                        : 'Welcome back! Sign in to access your saved items, order history, and continue your seamless shopping experience with us.'\r\n                                    }\r\n                                </p>\r\n                                <div className=\"switch-features\">\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Personalized recommendations' : 'Quick checkout process'}</span>\r\n                                    </div>\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Exclusive member discounts' : 'Order tracking & history'}</span>\r\n                                    </div>\r\n                                    <div className=\"feature-item\">\r\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                            <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\r\n                                        </svg>\r\n                                        <span>{isLogin ? 'Priority customer support' : 'Saved favorites & wishlist'}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <button\r\n                                    className=\"switch-btn-modern\"\r\n                                    onClick={() => {\r\n                                        setIsLogin(!isLogin);\r\n                                        setError('');\r\n                                        setFormData({\r\n                                            email: '',\r\n                                            password: '',\r\n                                            firstName: '',\r\n                                            lastName: '',\r\n                                            phone: ''\r\n                                        });\r\n                                    }}\r\n                                >\r\n                                    <span>{isLogin ? 'Create Account' : 'Sign In'}</span>\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                                        <path d=\"M5 12h14M12 5l7 7-7 7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                    </svg>\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAE2B,KAAK;IAAEC;EAAS,CAAC,GAAGxB,OAAO,CAAC,CAAC;EACrC,MAAMyB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4B,IAAI,GAAG,EAAAnB,eAAA,GAAAkB,QAAQ,CAACE,KAAK,cAAApB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBmB,IAAI,cAAAlB,oBAAA,uBAApBA,oBAAA,CAAsBoB,QAAQ,KAAI,GAAG;EAElD,MAAMC,YAAY,GAAIC,CAAC,IAAK;IACxBlB,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAC9BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBhB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACA,IAAIZ,OAAO,EAAE;QACT,MAAM2B,MAAM,GAAG,MAAMd,KAAK,CAACX,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;QAC7D,IAAIsB,MAAM,CAACC,OAAO,EAAE;UAChB;UACA,IAAIC,UAAU,GAAGZ,IAAI;;UAErB;UACA,IAAIA,IAAI,CAACa,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAIH,MAAM,CAACI,IAAI,CAACC,IAAI,KAAK,OAAO,IAAIL,MAAM,CAACI,IAAI,CAACC,IAAI,KAAK,UAAU,EAAE;cACjEH,UAAU,GAAGZ,IAAI;YACrB,CAAC,MAAM;cACHY,UAAU,GAAG,GAAG,CAAC,CAAC;YACtB;UACJ,CAAC,MAAM,IAAIZ,IAAI,KAAK,GAAG,EAAE;YACrB;YACA,IAAIU,MAAM,CAACI,IAAI,CAACC,IAAI,KAAK,OAAO,IAAIL,MAAM,CAACI,IAAI,CAACC,IAAI,KAAK,UAAU,EAAE;cACjEH,UAAU,GAAG,QAAQ;YACzB,CAAC,MAAM;cACHA,UAAU,GAAG,GAAG;YACpB;UACJ;UAEAd,QAAQ,CAACc,UAAU,EAAE;YAAEI,OAAO,EAAE;UAAK,CAAC,CAAC;QAC3C,CAAC,MAAM;UACHrB,QAAQ,CAACe,MAAM,CAAChB,KAAK,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH,MAAMgB,MAAM,GAAG,MAAMb,QAAQ,CAACZ,QAAQ,CAAC;QACvC,IAAIyB,MAAM,CAACC,OAAO,EAAE;UAChB;UACAb,QAAQ,CAAC,GAAG,EAAE;YAAEkB,OAAO,EAAE;UAAK,CAAC,CAAC;QACpC,CAAC,MAAM;UACHrB,QAAQ,CAACe,MAAM,CAAChB,KAAK,CAAC;QAC1B;MACJ;IACJ,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACVtB,QAAQ,CAACsB,GAAG,CAACC,OAAO,IAAI,mBAAmB,CAAC;IAChD,CAAC,SAAS;MACNzB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,oBACIjB,OAAA;IAAK2C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE7B5C,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtB5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB5C,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9B5C,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB5C,OAAA;cAAK2C,SAAS,EAAC,YAAY;cAAAC,QAAA,eACvB5C,OAAA;gBAAK6C,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAJ,QAAA,gBACvD5C,OAAA;kBAAMiD,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrDvD,OAAA;kBAAMiD,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC3DvD,OAAA;kBAAMiD,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC3DvD,OAAA;kBAAMiD,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrDvD,OAAA;kBAAMiD,CAAC,EAAC,WAAW;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACtDvD,OAAA;kBAAMiD,CAAC,EAAC,WAAW;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvD,OAAA;cAAA4C,QAAA,EAAI;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBvD,OAAA;cAAA4C,QAAA,EAAG;YAAkC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvD,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtB5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtB5C,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAExB5C,OAAA;YAAK2C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChC5C,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5C,OAAA;gBAAK2C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/B5C,OAAA;kBAAK2C,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACtB5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAMiD,CAAC,EAAC,2CAA2C;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtFvD,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNvD,OAAA;kBAAA4C,QAAA,EAAKrC,OAAO,GAAG,cAAc,GAAG;gBAAgB;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtDvD,OAAA;kBAAG2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACvBrC,OAAO,GACF,sDAAsD,GACtD;gBAAgD;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvD,CAAC,eACJvD,OAAA;kBAAK2C,SAAS,EAAC;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAENvD,OAAA;gBAAM2D,QAAQ,EAAE3B,YAAa;gBAACW,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GACrD1B,KAAK,iBACFlB,OAAA;kBAAK2C,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACjC5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjEvD,OAAA;sBAAMiD,CAAC,EAAC,oBAAoB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACNvD,OAAA;oBAAA4C,QAAA,EAAO1B;kBAAK;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CACR,EAEA,CAAChD,OAAO,iBACLP,OAAA,CAAAE,SAAA;kBAAA0C,QAAA,gBACI5C,OAAA;oBAAK2C,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5B5C,OAAA;sBAAK2C,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAC9B5C,OAAA;wBAAA4C,QAAA,EAAO;sBAAU;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzBvD,OAAA;wBAAK2C,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC1B5C,OAAA;0BAAK6C,KAAK,EAAC,IAAI;0BAACC,MAAM,EAAC,IAAI;0BAACC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAJ,QAAA,gBACvD5C,OAAA;4BAAMiD,CAAC,EAAC,2CAA2C;4BAACC,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC,eACxFvD,OAAA;4BAAQwD,EAAE,EAAC,IAAI;4BAACC,EAAE,EAAC,GAAG;4BAACC,CAAC,EAAC,GAAG;4BAACR,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE,CAAC,eACNvD,OAAA;0BACI4D,IAAI,EAAC,MAAM;0BACX9B,IAAI,EAAC,WAAW;0BAChB+B,WAAW,EAAC,uBAAuB;0BACnC9B,KAAK,EAAEtB,QAAQ,CAACI,SAAU;0BAC1BiD,QAAQ,EAAEnC,YAAa;0BACvBoC,QAAQ;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNvD,OAAA;sBAAK2C,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAC9B5C,OAAA;wBAAA4C,QAAA,EAAO;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxBvD,OAAA;wBAAK2C,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC1B5C,OAAA;0BAAK6C,KAAK,EAAC,IAAI;0BAACC,MAAM,EAAC,IAAI;0BAACC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAJ,QAAA,gBACvD5C,OAAA;4BAAMiD,CAAC,EAAC,2CAA2C;4BAACC,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC,eACxFvD,OAAA;4BAAQwD,EAAE,EAAC,IAAI;4BAACC,EAAE,EAAC,GAAG;4BAACC,CAAC,EAAC,GAAG;4BAACR,MAAM,EAAC,SAAS;4BAACC,WAAW,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE,CAAC,eACNvD,OAAA;0BACI4D,IAAI,EAAC,MAAM;0BACX9B,IAAI,EAAC,UAAU;0BACf+B,WAAW,EAAC,sBAAsB;0BAClC9B,KAAK,EAAEtB,QAAQ,CAACK,QAAS;0BACzBgD,QAAQ,EAAEnC,YAAa;0BACvBoC,QAAQ;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNvD,OAAA;oBAAK2C,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9B5C,OAAA;sBAAA4C,QAAA,EAAO;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3BvD,OAAA;sBAAK2C,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC1B5C,OAAA;wBAAK6C,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACC,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAJ,QAAA,eACvD5C,OAAA;0BAAMiD,CAAC,EAAC,+RAA+R;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3U,CAAC,eACNvD,OAAA;wBACI4D,IAAI,EAAC,KAAK;wBACV9B,IAAI,EAAC,OAAO;wBACZ+B,WAAW,EAAC,yBAAyB;wBACrC9B,KAAK,EAAEtB,QAAQ,CAACM,KAAM;wBACtB+C,QAAQ,EAAEnC;sBAAa;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,eACR,CACL,eAEDvD,OAAA;kBAAK2C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B5C,OAAA;oBAAA4C,QAAA,EAAO;kBAAa;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BvD,OAAA;oBAAK2C,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC1B5C,OAAA;sBAAK6C,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvD5C,OAAA;wBAAMiD,CAAC,EAAC,6EAA6E;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC1HvD,OAAA;wBAAUgE,MAAM,EAAC,gBAAgB;wBAACd,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC,eACNvD,OAAA;sBACI4D,IAAI,EAAC,OAAO;sBACZ9B,IAAI,EAAC,OAAO;sBACZ+B,WAAW,EAAC,0BAA0B;sBACtC9B,KAAK,EAAEtB,QAAQ,CAACE,KAAM;sBACtBmD,QAAQ,EAAEnC,YAAa;sBACvBoC,QAAQ;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENvD,OAAA;kBAAK2C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B5C,OAAA;oBAAA4C,QAAA,EAAO;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvBvD,OAAA;oBAAK2C,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC1B5C,OAAA;sBAAK6C,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvD5C,OAAA;wBAAMiE,CAAC,EAAC,GAAG;wBAACC,CAAC,EAAC,IAAI;wBAACrB,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACqB,EAAE,EAAC,GAAG;wBAACC,EAAE,EAAC,GAAG;wBAAClB,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC5FvD,OAAA;wBAAQwD,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACV,IAAI,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC9CvD,OAAA;wBAAMiD,CAAC,EAAC,0BAA0B;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACNvD,OAAA;sBACI4D,IAAI,EAAC,UAAU;sBACf9B,IAAI,EAAC,UAAU;sBACf+B,WAAW,EAAC,qBAAqB;sBACjC9B,KAAK,EAAEtB,QAAQ,CAACG,QAAS;sBACzBkD,QAAQ,EAAEnC,YAAa;sBACvBoC,QAAQ;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EAELhD,OAAO,iBACJP,OAAA;kBAAK2C,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAChC5C,OAAA,CAACN,IAAI;oBAAC2E,EAAE,EAAC,kBAAkB;oBAAC1B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACtD5C,OAAA;sBAAK6C,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAJ,QAAA,gBACvD5C,OAAA;wBAAQwD,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,IAAI;wBAACR,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACnEvD,OAAA;wBAAMiD,CAAC,EAAC,aAAa;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,oBAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACR,eAEDvD,OAAA;kBAAQ4D,IAAI,EAAC,QAAQ;kBAACjB,SAAS,EAAC,wBAAwB;kBAAC2B,QAAQ,EAAEtD,OAAQ;kBAAA4B,QAAA,eACvE5C,OAAA;oBAAK2C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EACvB5B,OAAO,gBACJhB,OAAA,CAAAE,SAAA;sBAAA0C,QAAA,gBACI5C,OAAA;wBAAK2C,SAAS,EAAC;sBAAiB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvCvD,OAAA;wBAAA4C,QAAA,EAAM;sBAAc;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eAC7B,CAAC,gBAEHvD,OAAA,CAAAE,SAAA;sBAAA0C,QAAA,gBACI5C,OAAA;wBAAA4C,QAAA,EAAOrC,OAAO,GAAG,SAAS,GAAG;sBAAgB;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACrDvD,OAAA;wBAAK6C,KAAK,EAAC,IAAI;wBAACC,MAAM,EAAC,IAAI;wBAACC,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAJ,QAAA,eACvD5C,OAAA;0BAAMiD,CAAC,EAAC,uBAAuB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACoB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC;wBAAO;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnH,CAAC;oBAAA,eACR;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EAEN,CAAChD,OAAO,iBACLP,OAAA;gBAAK2C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChC5C,OAAA;kBAAK2C,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACvB5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAMiD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGvD,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNvD,OAAA;kBAAA4C,QAAA,GAAG,0CACyC,EAAC,GAAG,eAC5C5C,OAAA,CAACN,IAAI;oBAAC2E,EAAE,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAgB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,QAAI,EAAC,GAAG,eACjDvD,OAAA,CAACN,IAAI;oBAAC2E,EAAE,EAAC,UAAU;oBAAAzB,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNvD,OAAA;YAAK2C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClC5C,OAAA;cAAK2C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7B5C,OAAA;gBAAK2C,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACxB5C,OAAA;kBAAK6C,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAJ,QAAA,EACtDrC,OAAO,gBACJP,OAAA;oBAAMiD,CAAC,EAAC,sGAAsG;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAEjJvD,OAAA;oBAAMiD,CAAC,EAAC,iEAAiE;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC9G;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,EAAKrC,OAAO,GAAG,oBAAoB,GAAG;cAA0B;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtEvD,OAAA;gBAAA4C,QAAA,EACKrC,OAAO,GACF,uKAAuK,GACvK;cAA0H;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjI,CAAC,eACJvD,OAAA;gBAAK2C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5B5C,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAMiD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGvD,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNvD,OAAA;oBAAA4C,QAAA,EAAOrC,OAAO,GAAG,8BAA8B,GAAG;kBAAwB;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNvD,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAMiD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGvD,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNvD,OAAA;oBAAA4C,QAAA,EAAOrC,OAAO,GAAG,4BAA4B,GAAG;kBAA0B;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNvD,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB5C,OAAA;oBAAK6C,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAJ,QAAA,gBACvD5C,OAAA;sBAAMiD,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACoB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGvD,OAAA;sBAAQwD,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACR,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNvD,OAAA;oBAAA4C,QAAA,EAAOrC,OAAO,GAAG,2BAA2B,GAAG;kBAA4B;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNvD,OAAA;gBACI2C,SAAS,EAAC,mBAAmB;gBAC7B8B,OAAO,EAAEA,CAAA,KAAM;kBACXjE,UAAU,CAAC,CAACD,OAAO,CAAC;kBACpBY,QAAQ,CAAC,EAAE,CAAC;kBACZT,WAAW,CAAC;oBACRC,KAAK,EAAE,EAAE;oBACTC,QAAQ,EAAE,EAAE;oBACZC,SAAS,EAAE,EAAE;oBACbC,QAAQ,EAAE,EAAE;oBACZC,KAAK,EAAE;kBACX,CAAC,CAAC;gBACN,CAAE;gBAAA6B,QAAA,gBAEF5C,OAAA;kBAAA4C,QAAA,EAAOrC,OAAO,GAAG,gBAAgB,GAAG;gBAAS;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDvD,OAAA;kBAAK6C,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAJ,QAAA,eACvD5C,OAAA;oBAAMiD,CAAC,EAAC,uBAAuB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACoB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnD,EAAA,CAvVID,KAAK;EAAA,QAYqBN,OAAO,EAClBF,WAAW,EACXC,WAAW;AAAA;AAAA8E,EAAA,GAd1BvE,KAAK;AAyVX,eAAeA,KAAK;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}