import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import paymentService from '../../services/paymentService';
import apiClient from '../../services/apiClient';
import './PayMongoCheckout.css';

const PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {
  const navigate = useNavigate();
  const { clearCart } = useCart();
  const [loading, setLoading] = useState(false);
  const [paymentLink, setPaymentLink] = useState(null);
  const [error, setError] = useState('');
  const [fees, setFees] = useState(null);
  const [selectedMethod, setSelectedMethod] = useState('card');

  // Payment method options
  const paymentMethods = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      icon: '💳',
      description: 'Visa, Mastercard, JCB, American Express',
      fee: '3.5% + ₱15'
    },
    {
      id: 'gcash',
      name: 'GCash',
      icon: '📱',
      description: 'Pay using your GCash wallet',
      fee: '2.5%'
    },
    {
      id: 'grabpay',
      name: 'GrabPay',
      icon: '🚗',
      description: 'Pay using your GrabPay wallet',
      fee: '2.5%'
    },
    {
      id: 'bank',
      name: 'Online Banking',
      icon: '🏦',
      description: 'Direct bank transfer',
      fee: '1.5%'
    }
  ];

  // Calculate fees when component mounts or method changes
  useEffect(() => {
    if (orderData?.totalAmount) {
      calculatePaymentFees();
    }
  }, [orderData?.totalAmount, selectedMethod]);

  const calculatePaymentFees = async () => {
    try {
      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos
      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);
      if (result.success) {
        setFees(result.data);
      }
    } catch (error) {
      console.error('Fee calculation error:', error);
    }
  };

  const handleCreatePaymentLink = async () => {
    setLoading(true);
    setError('');

    try {
      // First, create a pending order in the backend
      const orderCreationData = {
        customerEmail: orderData.shippingAddress?.email,
        customerName: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,
        customerPhone: orderData.shippingAddress?.phone || '',
        shippingAddress: JSON.stringify(orderData.shippingAddress),
        billingAddress: JSON.stringify(orderData.shippingAddress),
        items: orderData.items.map(item => ({
          variantId: item.id || item.variantId,
          quantity: item.quantity,
          unitPrice: item.price || item.unitPrice,
          totalPrice: (item.quantity || 1) * (item.price || item.unitPrice || 0),
          customConfiguration: item.customConfiguration
        })),
        subTotal: orderData.subtotal,
        taxAmount: orderData.tax || 0,
        shippingAmount: orderData.shipping || 0,
        discountAmount: 0,
        totalAmount: orderData.totalAmount,
        currency: 'PHP',
        notes: 'Order created for PayMongo payment processing'
      };

      // Create order in backend
      const orderResult = await apiClient.post('/api/orders', orderCreationData);

      if (!orderResult.success) {
        throw new Error(orderResult.message || 'Failed to create order');
      }

      const createdOrder = orderResult.data.order;
      console.log('Order created in backend:', createdOrder.OrderNumber);

      // Prepare payment data with the created order ID
      const paymentData = {
        orderId: createdOrder.OrderNumber,
        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos
        items: orderData.items || [],
        customer: {
          name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,
          email: orderData.shippingAddress?.email,
          phone: orderData.shippingAddress?.phone
        },
        shippingAddress: orderData.shippingAddress || {},
        metadata: {
          paymentMethod: selectedMethod,
          source: 'designxcel-checkout',
          backendOrderId: createdOrder.OrderID,
          items: JSON.stringify(orderData.items),
          shippingAddress: JSON.stringify(orderData.shippingAddress),
          customer: JSON.stringify({
            name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,
            email: orderData.shippingAddress?.email,
            phone: orderData.shippingAddress?.phone
          }),
          taxAmount: orderData.tax || 0,
          shippingAmount: orderData.shipping || 0,
          discountAmount: 0,
          ...orderData.metadata
        }
      };

      const result = await paymentService.createPaymentLink(paymentData);

      // Check if result has paymentLink (successful response structure)
      if (result && result.paymentLink && result.paymentLink.url) {
        setPaymentLink(result.paymentLink);

        // Redirect to PayMongo checkout
        window.open(result.paymentLink.url, '_blank');

        // Start polling for payment status
        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);

        if (onSuccess) {
          onSuccess({
            ...result,
            backendOrder: createdOrder
          });
        }
      } else {
        throw new Error('Invalid payment link response structure');
      }
    } catch (error) {
      console.error('Payment link creation error:', error);
      setError(error.message || 'Failed to create payment link');
      if (onError) {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  const startPaymentStatusPolling = (orderId, paymentLinkId) => {
    const pollInterval = setInterval(async () => {
      try {
        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);
        
        // Handle different response structures
        if (statusResult && (statusResult.success || statusResult.status)) {
          const status = statusResult.data?.status || statusResult.status;
          
          if (status === 'paid') {
            clearInterval(pollInterval);

            // Clear cart on successful payment
            clearCart();

            // Navigate to success page - the backend order will be updated via webhook
            navigate('/order-success', {
              state: {
                order: {
                  id: orderId,
                  order_number: orderId,
                  total_amount: orderData.totalAmount,
                  currency: 'PHP',
                  status: 'paid',
                  created_at: new Date().toISOString(),
                  shipping_address: orderData.shippingAddress,
                  items: orderData.items
                },
                message: 'Your payment has been processed successfully! Your order is being processed.',
                paymentStatus: 'completed',
                paymentMethod: 'PayMongo'
              }
            });
          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {
            clearInterval(pollInterval);
            setError(`Payment ${status}. Please try again.`);
          }
        }
      } catch (error) {
        console.error('Payment status polling error:', error);
        // If we get rate limited, stop polling to avoid further issues
        if (error.message && error.message.includes('Too many requests')) {
          console.log('Rate limited - stopping payment status polling');
          clearInterval(pollInterval);
        }
      }
    }, 15000); // Poll every 15 seconds to reduce rate limiting

    // Stop polling after 15 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 900000);
  };

  const handleCancel = () => {
    if (paymentLink) {
      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);
    }
    if (onCancel) {
      onCancel();
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <div className="paymongo-checkout">
      <div className="checkout-header">
        <h2>Complete Your Payment</h2>
        <p>Secure payment powered by PayMongo</p>
      </div>

      {/* Order Summary */}
      <div className="order-summary">
        <h3>Order Summary</h3>
        <div className="summary-row">
          <span>Subtotal:</span>
          <span>{formatCurrency(orderData.totalAmount || 0)}</span>
        </div>
        {fees && (
          <>
            <div className="summary-row">
              <span>Payment Fee:</span>
              <span>{formatCurrency(fees.totalFee)}</span>
            </div>
            <div className="summary-row total">
              <span>Total Amount:</span>
              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>
            </div>
          </>
        )}
      </div>

      {/* Payment Method Selection */}
      <div className="payment-methods">
        <h3>Select Payment Method</h3>
        <div className="method-grid">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}
              onClick={() => setSelectedMethod(method.id)}
            >
              <div className="method-icon">{method.icon}</div>
              <div className="method-info">
                <div className="method-name">{method.name}</div>
                <div className="method-description">{method.description}</div>
                <div className="method-fee">Fee: {method.fee}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          <span>{error}</span>
        </div>
      )}

      {/* Payment Actions */}
      <div className="payment-actions">
        <button
          className="btn btn-secondary"
          onClick={handleCancel}
          disabled={loading}
        >
          Cancel
        </button>
        <button
          className="btn btn-primary"
          onClick={handleCreatePaymentLink}
          disabled={loading || !orderData?.totalAmount}
        >
          {loading ? (
            <>
              <span className="loading-spinner"></span>
              Creating Payment Link...
            </>
          ) : (
            <>
              <span className="payment-icon">🔒</span>
              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}
            </>
          )}
        </button>
      </div>

      {/* Payment Link Status */}
      {paymentLink && (
        <div className="payment-link-status">
          <div className="status-header">
            <span className="status-icon">✅</span>
            <span>Payment link created successfully!</span>
          </div>
          <p>
            A new tab has opened with your secure payment page. 
            Complete your payment there and return here to see the confirmation.
          </p>
          <div className="payment-link-info">
            <div className="info-row">
              <span>Reference:</span>
              <span className="reference-number">{paymentLink.reference}</span>
            </div>
            <div className="info-row">
              <span>Status:</span>
              <span className="status-badge">{paymentLink.status}</span>
            </div>
          </div>
        </div>
      )}

      {/* Security Notice */}
      <div className="security-notice">
        <div className="security-header">
          <span className="security-icon">🔐</span>
          <span>Secure Payment</span>
        </div>
        <p>
          Your payment information is encrypted and secure. 
          PayMongo is PCI DSS compliant and follows international security standards.
        </p>
      </div>
    </div>
  );
};

export default PayMongoCheckout;
