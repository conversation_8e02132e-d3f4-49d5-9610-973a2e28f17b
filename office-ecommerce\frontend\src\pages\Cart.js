import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import CartItem from '../components/cart/CartItem';
import ConfirmationModal from '../components/modals/ConfirmationModal';

const Cart = () => {
    const navigate = useNavigate();
    const { items, getSubtotal, getTax, getShipping, getTotal, clearCart } = useCart();
    const [showClearConfirmation, setShowClearConfirmation] = useState(false);

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const subtotal = getSubtotal();
    const tax = getTax(subtotal);
    const shipping = getShipping(subtotal);
    const total = getTotal();

    if (items.length === 0) {
        return (
            <div className="cart-page">
                <div className="container">
                    <div className="cart-empty-page">
                        <div className="empty-cart-content">
                            <div className="empty-cart-icon">
                                🛒
                            </div>
                            <h1>Your Cart is Empty</h1>
                            <p>Looks like you haven't added any items to your cart yet.</p>
                            <div className="empty-cart-actions">
                                <Link to="/products" className="btn btn-primary btn-large">
                                    Continue Shopping
                                </Link>
                                <Link to="/" className="btn btn-secondary">
                                    Back to Home
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="cart-page">
            <div className="container">
                {/* Breadcrumb Navigation */}
                <nav className="cart-breadcrumb" aria-label="Breadcrumb">
                    <ol className="breadcrumb-list">
                        <li className="breadcrumb-item">
                            <Link to="/" className="breadcrumb-link">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                                Home
                            </Link>
                        </li>
                        <li className="breadcrumb-separator" aria-hidden="true">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </li>
                        <li className="breadcrumb-item breadcrumb-current" aria-current="page">
                            Shopping Cart
                        </li>
                    </ol>
                </nav>

                {/* Page Header */}
                <header className="cart-page-header">
                    <div className="cart-header-content">
                        <h1 className="cart-title">Shopping Cart</h1>
                        <p className="cart-subtitle">
                            {items.length} {items.length === 1 ? 'item' : 'items'} in your cart
                        </p>
                    </div>
                </header>

                <div className="cart-layout">
                    <div className="cart-main">
                        <div className="cart-items-header">
                            <h2>Items in Your Cart</h2>
                            <button
                                className="clear-cart-btn"
                                onClick={() => setShowClearConfirmation(true)}
                            >
                                Clear All
                            </button>
                        </div>

                        <div className="cart-items-list">
                            {items.map(item => (
                                <div key={item.id} className="cart-item-wrapper">
                                    <CartItem item={item} />
                                </div>
                            ))}
                        </div>

                        <div className="cart-actions-bottom">
                            <Link to="/products" className="btn btn-secondary">
                                ← Continue Shopping
                            </Link>
                        </div>
                    </div>

                    <div className="cart-sidebar-summary">
                        <div className="cart-summary-card">
                            <h3>Order Summary</h3>
                            
                            <div className="summary-details">
                                <div className="summary-row">
                                    <span>Subtotal ({items.length} items):</span>
                                    <span>{formatPrice(subtotal)}</span>
                                </div>
                                
                                <div className="summary-row">
                                    <span>Tax (8%):</span>
                                    <span>{formatPrice(tax)}</span>
                                </div>
                                
                                <div className="summary-row">
                                    <span>Shipping:</span>
                                    <span>
                                        {shipping === 0 ? (
                                            <span className="free-shipping">FREE</span>
                                        ) : (
                                            formatPrice(shipping)
                                        )}
                                    </span>
                                </div>

                                {shipping === 0 && (
                                    <div className="shipping-notice">
                                        🎉 You qualify for free shipping!
                                    </div>
                                )}

                                {subtotal < 1000 && (
                                    <div className="shipping-notice">
                                        💡 Add {formatPrice(1000 - subtotal)} more for free shipping
                                    </div>
                                )}
                                
                                <hr />
                                
                                <div className="summary-row total">
                                    <span>Total:</span>
                                    <span>{formatPrice(total)}</span>
                                </div>
                            </div>

                            <div className="checkout-actions">
                                <Link
                                    to="/checkout"
                                    className="btn btn-primary btn-full btn-large"
                                    onClick={(e) => {
                                        console.log('🔍 Checkout button clicked!');
                                        console.log('🔍 Navigating to /checkout');
                                    }}
                                >
                                    Proceed to Checkout
                                </Link>

                                {/* Debug button for testing */}
                                <button
                                    onClick={() => {
                                        console.log('🔍 Debug button clicked!');
                                        navigate('/checkout');
                                    }}
                                    style={{
                                        marginTop: '10px',
                                        padding: '10px',
                                        backgroundColor: '#ff6b6b',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                    }}
                                >
                                    🐛 Debug: Navigate to Checkout
                                </button>

                                <div className="payment-methods">
                                    <p>We accept:</p>
                                    <div className="payment-icons">
                                        <span>💳</span>
                                        <span>🏦</span>
                                        <span>📱</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Recommended Products */}
                        <div className="recommended-products">
                            <h4>You might also like</h4>
                            <div className="recommended-item">
                                <img 
                                    src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=100" 
                                    alt="Recommended product"
                                />
                                <div className="recommended-info">
                                    <p>Executive Desk Lamp</p>
                                    <span>$149.99</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Clear Cart Confirmation Modal */}
            <ConfirmationModal
                isOpen={showClearConfirmation}
                onClose={() => setShowClearConfirmation(false)}
                onConfirm={() => {
                    clearCart();
                    setShowClearConfirmation(false);
                }}
                title="Clear Shopping Cart"
                message="Are you sure you want to remove all items from your cart? This action cannot be undone."
                confirmText="Clear Cart"
                cancelText="Keep Items"
                type="warning"
            />
        </div>
    );
};

export default Cart;
