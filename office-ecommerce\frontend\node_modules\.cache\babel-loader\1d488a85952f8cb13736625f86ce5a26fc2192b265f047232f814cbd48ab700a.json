{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\payment\\\\PayMongoCheckout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PayMongoCheckout = ({\n  orderData,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    clearCart\n  } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [{\n    id: 'card',\n    name: 'Credit/Debit Card',\n    icon: '💳',\n    description: 'Visa, Mastercard, JCB, American Express',\n    fee: '3.5% + ₱15'\n  }, {\n    id: 'gcash',\n    name: 'GCash',\n    icon: '📱',\n    description: 'Pay using your GCash wallet',\n    fee: '2.5%'\n  }, {\n    id: 'grabpay',\n    name: 'GrabPay',\n    icon: '🚗',\n    description: 'Pay using your GrabPay wallet',\n    fee: '2.5%'\n  }, {\n    id: 'bank',\n    name: 'Online Banking',\n    icon: '🏦',\n    description: 'Direct bank transfer',\n    fee: '1.5%'\n  }];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData !== null && orderData !== void 0 && orderData.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount, selectedMethod]);\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      var _orderData$shippingAd, _orderData$shippingAd2, _orderData$shippingAd3, _orderData$shippingAd4, _orderData$shippingAd5, _orderData$shippingAd6, _orderData$shippingAd7, _orderData$shippingAd8, _orderData$shippingAd9, _orderData$shippingAd0, _orderData$shippingAd1, _orderData$shippingAd10;\n      // First, create a pending order in the backend\n      const orderCreationData = {\n        customerEmail: (_orderData$shippingAd = orderData.shippingAddress) === null || _orderData$shippingAd === void 0 ? void 0 : _orderData$shippingAd.email,\n        customerName: ((_orderData$shippingAd2 = orderData.shippingAddress) === null || _orderData$shippingAd2 === void 0 ? void 0 : _orderData$shippingAd2.firstName) + ' ' + ((_orderData$shippingAd3 = orderData.shippingAddress) === null || _orderData$shippingAd3 === void 0 ? void 0 : _orderData$shippingAd3.lastName),\n        customerPhone: ((_orderData$shippingAd4 = orderData.shippingAddress) === null || _orderData$shippingAd4 === void 0 ? void 0 : _orderData$shippingAd4.phone) || '',\n        shippingAddress: JSON.stringify(orderData.shippingAddress),\n        billingAddress: JSON.stringify(orderData.shippingAddress),\n        items: orderData.items.map(item => ({\n          variantId: item.id || item.variantId,\n          quantity: item.quantity,\n          unitPrice: item.price || item.unitPrice,\n          totalPrice: (item.quantity || 1) * (item.price || item.unitPrice || 0),\n          customConfiguration: item.customConfiguration\n        })),\n        subTotal: orderData.subtotal,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100),\n        // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: ((_orderData$shippingAd5 = orderData.shippingAddress) === null || _orderData$shippingAd5 === void 0 ? void 0 : _orderData$shippingAd5.firstName) + ' ' + ((_orderData$shippingAd6 = orderData.shippingAddress) === null || _orderData$shippingAd6 === void 0 ? void 0 : _orderData$shippingAd6.lastName),\n          email: (_orderData$shippingAd7 = orderData.shippingAddress) === null || _orderData$shippingAd7 === void 0 ? void 0 : _orderData$shippingAd7.email,\n          phone: (_orderData$shippingAd8 = orderData.shippingAddress) === null || _orderData$shippingAd8 === void 0 ? void 0 : _orderData$shippingAd8.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: ((_orderData$shippingAd9 = orderData.shippingAddress) === null || _orderData$shippingAd9 === void 0 ? void 0 : _orderData$shippingAd9.firstName) + ' ' + ((_orderData$shippingAd0 = orderData.shippingAddress) === null || _orderData$shippingAd0 === void 0 ? void 0 : _orderData$shippingAd0.lastName),\n            email: (_orderData$shippingAd1 = orderData.shippingAddress) === null || _orderData$shippingAd1 === void 0 ? void 0 : _orderData$shippingAd1.email,\n            phone: (_orderData$shippingAd10 = orderData.shippingAddress) === null || _orderData$shippingAd10 === void 0 ? void 0 : _orderData$shippingAd10.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n\n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          var _statusResult$data;\n          const status = ((_statusResult$data = statusResult.data) === null || _statusResult$data === void 0 ? void 0 : _statusResult$data.status) || statusResult.status;\n          if (status === 'paid') {\n            var _orderData$metadata;\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: ((_orderData$metadata = orderData.metadata) === null || _orderData$metadata === void 0 ? void 0 : _orderData$metadata.currency) || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paymongo-checkout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Complete Your Payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Secure payment powered by PayMongo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Order Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subtotal:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatCurrency(orderData.totalAmount || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), fees && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Payment Fee:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row total\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Amount:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.amount + fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-methods\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Select Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"method-grid\",\n        children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `payment-method ${selectedMethod === method.id ? 'selected' : ''}`,\n          onClick: () => setSelectedMethod(method.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-icon\",\n            children: method.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: method.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-description\",\n              children: method.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-fee\",\n              children: [\"Fee: \", method.fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, method.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: handleCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleCreatePaymentLink,\n        disabled: loading || !(orderData !== null && orderData !== void 0 && orderData.totalAmount),\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), \"Creating Payment Link...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"payment-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), \"Pay \", fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), paymentLink && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-link-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Payment link created successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-link-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Reference:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reference-number\",\n            children: paymentLink.reference\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            children: paymentLink.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"security-icon\",\n          children: \"\\uD83D\\uDD10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Secure Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(PayMongoCheckout, \"86+uGYKNFyWUeaqIj86xKtQyg4w=\", false, function () {\n  return [useNavigate, useCart];\n});\n_c = PayMongoCheckout;\nexport default PayMongoCheckout;\nvar _c;\n$RefreshReg$(_c, \"PayMongoCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCart", "paymentService", "apiClient", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PayMongoCheckout", "orderData", "onSuccess", "onError", "onCancel", "_s", "navigate", "clearCart", "loading", "setLoading", "paymentLink", "setPaymentLink", "error", "setError", "fees", "setFees", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "paymentMethods", "id", "name", "icon", "description", "fee", "totalAmount", "calculatePaymentFees", "amountInCentavos", "Math", "round", "result", "calculateFees", "success", "data", "console", "handleCreatePaymentLink", "_orderData$shippingAd", "_orderData$shippingAd2", "_orderData$shippingAd3", "_orderData$shippingAd4", "_orderData$shippingAd5", "_orderData$shippingAd6", "_orderData$shippingAd7", "_orderData$shippingAd8", "_orderData$shippingAd9", "_orderData$shippingAd0", "_orderData$shippingAd1", "_orderData$shippingAd10", "orderCreationData", "customerEmail", "shippingAddress", "email", "customerName", "firstName", "lastName", "customerPhone", "phone", "JSON", "stringify", "billing<PERSON><PERSON>ress", "items", "map", "item", "variantId", "quantity", "unitPrice", "price", "totalPrice", "customConfiguration", "subTotal", "subtotal", "taxAmount", "tax", "shippingAmount", "shipping", "discountAmount", "currency", "notes", "orderResult", "post", "Error", "message", "createdOrder", "order", "log", "OrderNumber", "paymentData", "orderId", "customer", "metadata", "paymentMethod", "source", "backendOrderId", "OrderID", "createPaymentLink", "url", "window", "open", "startPaymentStatusPolling", "backendOrder", "paymentLinkId", "pollInterval", "setInterval", "statusResult", "getPaymentStatus", "status", "_statusResult$data", "_orderData$metadata", "clearInterval", "state", "order_number", "total_amount", "created_at", "Date", "toISOString", "shipping_address", "paymentStatus", "includes", "setTimeout", "handleCancel", "cancelPaymentLink", "catch", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalFee", "method", "onClick", "disabled", "reference", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/payment/PayMongoCheckout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\n\nconst PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const { clearCart } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      icon: '💳',\n      description: 'Visa, Mastercard, JCB, American Express',\n      fee: '3.5% + ₱15'\n    },\n    {\n      id: 'gcash',\n      name: 'GCash',\n      icon: '📱',\n      description: 'Pay using your GCash wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'grabpay',\n      name: 'GrabPay',\n      icon: '🚗',\n      description: 'Pay using your GrabPay wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'bank',\n      name: 'Online Banking',\n      icon: '🏦',\n      description: 'Direct bank transfer',\n      fee: '1.5%'\n    }\n  ];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData?.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData?.totalAmount, selectedMethod]);\n\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // First, create a pending order in the backend\n      const orderCreationData = {\n        customerEmail: orderData.shippingAddress?.email,\n        customerName: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n        customerPhone: orderData.shippingAddress?.phone || '',\n        shippingAddress: JSON.stringify(orderData.shippingAddress),\n        billingAddress: JSON.stringify(orderData.shippingAddress),\n        items: orderData.items.map(item => ({\n          variantId: item.id || item.variantId,\n          quantity: item.quantity,\n          unitPrice: item.price || item.unitPrice,\n          totalPrice: (item.quantity || 1) * (item.price || item.unitPrice || 0),\n          customConfiguration: item.customConfiguration\n        })),\n        subTotal: orderData.subtotal,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n          email: orderData.shippingAddress?.email,\n          phone: orderData.shippingAddress?.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n            email: orderData.shippingAddress?.email,\n            phone: orderData.shippingAddress?.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        \n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          const status = statusResult.data?.status || statusResult.status;\n          \n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page with complete order data\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: `ORD-${orderId}`,\n                  total_amount: orderData.totalAmount,\n                  currency: orderData.metadata?.currency || 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully!',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paymongo-checkout\">\n      <div className=\"checkout-header\">\n        <h2>Complete Your Payment</h2>\n        <p>Secure payment powered by PayMongo</p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div className=\"summary-row\">\n          <span>Subtotal:</span>\n          <span>{formatCurrency(orderData.totalAmount || 0)}</span>\n        </div>\n        {fees && (\n          <>\n            <div className=\"summary-row\">\n              <span>Payment Fee:</span>\n              <span>{formatCurrency(fees.totalFee)}</span>\n            </div>\n            <div className=\"summary-row total\">\n              <span>Total Amount:</span>\n              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"payment-methods\">\n        <h3>Select Payment Method</h3>\n        <div className=\"method-grid\">\n          {paymentMethods.map((method) => (\n            <div\n              key={method.id}\n              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}\n              onClick={() => setSelectedMethod(method.id)}\n            >\n              <div className=\"method-icon\">{method.icon}</div>\n              <div className=\"method-info\">\n                <div className=\"method-name\">{method.name}</div>\n                <div className=\"method-description\">{method.description}</div>\n                <div className=\"method-fee\">Fee: {method.fee}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* Payment Actions */}\n      <div className=\"payment-actions\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={handleCancel}\n          disabled={loading}\n        >\n          Cancel\n        </button>\n        <button\n          className=\"btn btn-primary\"\n          onClick={handleCreatePaymentLink}\n          disabled={loading || !orderData?.totalAmount}\n        >\n          {loading ? (\n            <>\n              <span className=\"loading-spinner\"></span>\n              Creating Payment Link...\n            </>\n          ) : (\n            <>\n              <span className=\"payment-icon\">🔒</span>\n              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Payment Link Status */}\n      {paymentLink && (\n        <div className=\"payment-link-status\">\n          <div className=\"status-header\">\n            <span className=\"status-icon\">✅</span>\n            <span>Payment link created successfully!</span>\n          </div>\n          <p>\n            A new tab has opened with your secure payment page. \n            Complete your payment there and return here to see the confirmation.\n          </p>\n          <div className=\"payment-link-info\">\n            <div className=\"info-row\">\n              <span>Reference:</span>\n              <span className=\"reference-number\">{paymentLink.reference}</span>\n            </div>\n            <div className=\"info-row\">\n              <span>Status:</span>\n              <span className=\"status-badge\">{paymentLink.status}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Security Notice */}\n      <div className=\"security-notice\">\n        <div className=\"security-header\">\n          <span className=\"security-icon\">🔐</span>\n          <span>Secure Payment</span>\n        </div>\n        <p>\n          Your payment information is encrypted and secure. \n          PayMongo is PCI DSS compliant and follows international security standards.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default PayMongoCheckout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAU,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC;;EAE5D;EACA,MAAM4B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,yCAAyC;IACtDC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,6BAA6B;IAC1CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sBAAsB;IACnCC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,EAAE;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,EAAER,cAAc,CAAC,CAAC;EAE5C,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAClE,MAAMK,MAAM,GAAG,MAAMnC,cAAc,CAACoC,aAAa,CAACJ,gBAAgB,EAAEV,cAAc,CAAC;MACnF,IAAIa,MAAM,CAACE,OAAO,EAAE;QAClBhB,OAAO,CAACc,MAAM,CAACG,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMsB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CzB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAsB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;MACF;MACA,MAAMC,iBAAiB,GAAG;QACxBC,aAAa,GAAAb,qBAAA,GAAElC,SAAS,CAACgD,eAAe,cAAAd,qBAAA,uBAAzBA,qBAAA,CAA2Be,KAAK;QAC/CC,YAAY,EAAE,EAAAf,sBAAA,GAAAnC,SAAS,CAACgD,eAAe,cAAAb,sBAAA,uBAAzBA,sBAAA,CAA2BgB,SAAS,IAAG,GAAG,KAAAf,sBAAA,GAAGpC,SAAS,CAACgD,eAAe,cAAAZ,sBAAA,uBAAzBA,sBAAA,CAA2BgB,QAAQ;QAC9FC,aAAa,EAAE,EAAAhB,sBAAA,GAAArC,SAAS,CAACgD,eAAe,cAAAX,sBAAA,uBAAzBA,sBAAA,CAA2BiB,KAAK,KAAI,EAAE;QACrDN,eAAe,EAAEO,IAAI,CAACC,SAAS,CAACxD,SAAS,CAACgD,eAAe,CAAC;QAC1DS,cAAc,EAAEF,IAAI,CAACC,SAAS,CAACxD,SAAS,CAACgD,eAAe,CAAC;QACzDU,KAAK,EAAE1D,SAAS,CAAC0D,KAAK,CAACC,GAAG,CAACC,IAAI,KAAK;UAClCC,SAAS,EAAED,IAAI,CAAC1C,EAAE,IAAI0C,IAAI,CAACC,SAAS;UACpCC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;UACvBC,SAAS,EAAEH,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACG,SAAS;UACvCE,UAAU,EAAE,CAACL,IAAI,CAACE,QAAQ,IAAI,CAAC,KAAKF,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACG,SAAS,IAAI,CAAC,CAAC;UACtEG,mBAAmB,EAAEN,IAAI,CAACM;QAC5B,CAAC,CAAC,CAAC;QACHC,QAAQ,EAAEnE,SAAS,CAACoE,QAAQ;QAC5BC,SAAS,EAAErE,SAAS,CAACsE,GAAG,IAAI,CAAC;QAC7BC,cAAc,EAAEvE,SAAS,CAACwE,QAAQ,IAAI,CAAC;QACvCC,cAAc,EAAE,CAAC;QACjBlD,WAAW,EAAEvB,SAAS,CAACuB,WAAW;QAClCmD,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE;MACT,CAAC;;MAED;MACA,MAAMC,WAAW,GAAG,MAAMlF,SAAS,CAACmF,IAAI,CAAC,aAAa,EAAE/B,iBAAiB,CAAC;MAE1E,IAAI,CAAC8B,WAAW,CAAC9C,OAAO,EAAE;QACxB,MAAM,IAAIgD,KAAK,CAACF,WAAW,CAACG,OAAO,IAAI,wBAAwB,CAAC;MAClE;MAEA,MAAMC,YAAY,GAAGJ,WAAW,CAAC7C,IAAI,CAACkD,KAAK;MAC3CjD,OAAO,CAACkD,GAAG,CAAC,2BAA2B,EAAEF,YAAY,CAACG,WAAW,CAAC;;MAElE;MACA,MAAMC,WAAW,GAAG;QAClBC,OAAO,EAAEL,YAAY,CAACG,WAAW;QACjC5D,WAAW,EAAEG,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC;QAAE;QACtDmC,KAAK,EAAE1D,SAAS,CAAC0D,KAAK,IAAI,EAAE;QAC5B4B,QAAQ,EAAE;UACRnE,IAAI,EAAE,EAAAmB,sBAAA,GAAAtC,SAAS,CAACgD,eAAe,cAAAV,sBAAA,uBAAzBA,sBAAA,CAA2Ba,SAAS,IAAG,GAAG,KAAAZ,sBAAA,GAAGvC,SAAS,CAACgD,eAAe,cAAAT,sBAAA,uBAAzBA,sBAAA,CAA2Ba,QAAQ;UACtFH,KAAK,GAAAT,sBAAA,GAAExC,SAAS,CAACgD,eAAe,cAAAR,sBAAA,uBAAzBA,sBAAA,CAA2BS,KAAK;UACvCK,KAAK,GAAAb,sBAAA,GAAEzC,SAAS,CAACgD,eAAe,cAAAP,sBAAA,uBAAzBA,sBAAA,CAA2Ba;QACpC,CAAC;QACDN,eAAe,EAAEhD,SAAS,CAACgD,eAAe,IAAI,CAAC,CAAC;QAChDuC,QAAQ,EAAE;UACRC,aAAa,EAAEzE,cAAc;UAC7B0E,MAAM,EAAE,qBAAqB;UAC7BC,cAAc,EAAEV,YAAY,CAACW,OAAO;UACpCjC,KAAK,EAAEH,IAAI,CAACC,SAAS,CAACxD,SAAS,CAAC0D,KAAK,CAAC;UACtCV,eAAe,EAAEO,IAAI,CAACC,SAAS,CAACxD,SAAS,CAACgD,eAAe,CAAC;UAC1DsC,QAAQ,EAAE/B,IAAI,CAACC,SAAS,CAAC;YACvBrC,IAAI,EAAE,EAAAuB,sBAAA,GAAA1C,SAAS,CAACgD,eAAe,cAAAN,sBAAA,uBAAzBA,sBAAA,CAA2BS,SAAS,IAAG,GAAG,KAAAR,sBAAA,GAAG3C,SAAS,CAACgD,eAAe,cAAAL,sBAAA,uBAAzBA,sBAAA,CAA2BS,QAAQ;YACtFH,KAAK,GAAAL,sBAAA,GAAE5C,SAAS,CAACgD,eAAe,cAAAJ,sBAAA,uBAAzBA,sBAAA,CAA2BK,KAAK;YACvCK,KAAK,GAAAT,uBAAA,GAAE7C,SAAS,CAACgD,eAAe,cAAAH,uBAAA,uBAAzBA,uBAAA,CAA2BS;UACpC,CAAC,CAAC;UACFe,SAAS,EAAErE,SAAS,CAACsE,GAAG,IAAI,CAAC;UAC7BC,cAAc,EAAEvE,SAAS,CAACwE,QAAQ,IAAI,CAAC;UACvCC,cAAc,EAAE,CAAC;UACjB,GAAGzE,SAAS,CAACuF;QACf;MACF,CAAC;MAED,MAAM3D,MAAM,GAAG,MAAMnC,cAAc,CAACmG,iBAAiB,CAACR,WAAW,CAAC;;MAElE;MACA,IAAIxD,MAAM,IAAIA,MAAM,CAACnB,WAAW,IAAImB,MAAM,CAACnB,WAAW,CAACoF,GAAG,EAAE;QAC1DnF,cAAc,CAACkB,MAAM,CAACnB,WAAW,CAAC;;QAElC;QACAqF,MAAM,CAACC,IAAI,CAACnE,MAAM,CAACnB,WAAW,CAACoF,GAAG,EAAE,QAAQ,CAAC;;QAE7C;QACAG,yBAAyB,CAAChB,YAAY,CAACG,WAAW,EAAEvD,MAAM,CAACnB,WAAW,CAACS,EAAE,CAAC;QAE1E,IAAIjB,SAAS,EAAE;UACbA,SAAS,CAAC;YACR,GAAG2B,MAAM;YACTqE,YAAY,EAAEjB;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAACD,KAAK,CAACoE,OAAO,IAAI,+BAA+B,CAAC;MAC1D,IAAI7E,OAAO,EAAE;QACXA,OAAO,CAACS,KAAK,CAAC;MAChB;IACF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,yBAAyB,GAAGA,CAACX,OAAO,EAAEa,aAAa,KAAK;IAC5D,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,YAAY,GAAG,MAAM5G,cAAc,CAAC6G,gBAAgB,CAACjB,OAAO,EAAEa,aAAa,CAAC;;QAElF;QACA,IAAIG,YAAY,KAAKA,YAAY,CAACvE,OAAO,IAAIuE,YAAY,CAACE,MAAM,CAAC,EAAE;UAAA,IAAAC,kBAAA;UACjE,MAAMD,MAAM,GAAG,EAAAC,kBAAA,GAAAH,YAAY,CAACtE,IAAI,cAAAyE,kBAAA,uBAAjBA,kBAAA,CAAmBD,MAAM,KAAIF,YAAY,CAACE,MAAM;UAE/D,IAAIA,MAAM,KAAK,MAAM,EAAE;YAAA,IAAAE,mBAAA;YACrBC,aAAa,CAACP,YAAY,CAAC;;YAE3B;YACA7F,SAAS,CAAC,CAAC;;YAEX;YACAD,QAAQ,CAAC,gBAAgB,EAAE;cACzBsG,KAAK,EAAE;gBACL1B,KAAK,EAAE;kBACL/D,EAAE,EAAEmE,OAAO;kBACXuB,YAAY,EAAE,OAAOvB,OAAO,EAAE;kBAC9BwB,YAAY,EAAE7G,SAAS,CAACuB,WAAW;kBACnCmD,QAAQ,EAAE,EAAA+B,mBAAA,GAAAzG,SAAS,CAACuF,QAAQ,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoB/B,QAAQ,KAAI,KAAK;kBAC/C6B,MAAM,EAAE,MAAM;kBACdO,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACpCC,gBAAgB,EAAEjH,SAAS,CAACgD,eAAe;kBAC3CU,KAAK,EAAE1D,SAAS,CAAC0D;gBACnB,CAAC;gBACDqB,OAAO,EAAE,+CAA+C;gBACxDmC,aAAa,EAAE,WAAW;gBAC1B1B,aAAa,EAAE;cACjB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIe,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,SAAS,EAAE;YAChFG,aAAa,CAACP,YAAY,CAAC;YAC3BvF,QAAQ,CAAC,WAAW2F,MAAM,qBAAqB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAIA,KAAK,CAACoE,OAAO,IAAIpE,KAAK,CAACoE,OAAO,CAACoC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAChEnF,OAAO,CAACkD,GAAG,CAAC,gDAAgD,CAAC;UAC7DwB,aAAa,CAACP,YAAY,CAAC;QAC7B;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACAiB,UAAU,CAAC,MAAM;MACfV,aAAa,CAACP,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5G,WAAW,EAAE;MACfhB,cAAc,CAAC6H,iBAAiB,CAAC7G,WAAW,CAACS,EAAE,CAAC,CAACqG,KAAK,CAACvF,OAAO,CAACrB,KAAK,CAAC;IACvE;IACA,IAAIR,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMqH,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBlD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACmD,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACE7H,OAAA;IAAKkI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCnI,OAAA;MAAKkI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnI,OAAA;QAAAmI,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BvI,OAAA;QAAAmI,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGNvI,OAAA;MAAKkI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BnI,OAAA;QAAAmI,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBvI,OAAA;QAAKkI,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnI,OAAA;UAAAmI,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtBvI,OAAA;UAAAmI,QAAA,EAAOP,cAAc,CAACxH,SAAS,CAACuB,WAAW,IAAI,CAAC;QAAC;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACLtH,IAAI,iBACHjB,OAAA,CAAAE,SAAA;QAAAiI,QAAA,gBACEnI,OAAA;UAAKkI,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnI,OAAA;YAAAmI,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvI,OAAA;YAAAmI,QAAA,EAAOP,cAAc,CAAC3G,IAAI,CAACuH,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNvI,OAAA;UAAKkI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnI,OAAA;YAAAmI,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BvI,OAAA;YAAAmI,QAAA,EAAOP,cAAc,CAAC3G,IAAI,CAAC4G,MAAM,GAAG5G,IAAI,CAACuH,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvI,OAAA;MAAKkI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnI,OAAA;QAAAmI,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BvI,OAAA;QAAKkI,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB9G,cAAc,CAAC0C,GAAG,CAAE0E,MAAM,iBACzBzI,OAAA;UAEEkI,SAAS,EAAE,kBAAkB/G,cAAc,KAAKsH,MAAM,CAACnH,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9EoH,OAAO,EAAEA,CAAA,KAAMtH,iBAAiB,CAACqH,MAAM,CAACnH,EAAE,CAAE;UAAA6G,QAAA,gBAE5CnI,OAAA;YAAKkI,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,MAAM,CAACjH;UAAI;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDvI,OAAA;YAAKkI,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnI,OAAA;cAAKkI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEM,MAAM,CAAClH;YAAI;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvI,OAAA;cAAKkI,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEM,MAAM,CAAChH;YAAW;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DvI,OAAA;cAAKkI,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,OAAK,EAACM,MAAM,CAAC/G,GAAG;YAAA;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GATDE,MAAM,CAACnH,EAAE;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxH,KAAK,iBACJf,OAAA;MAAKkI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BnI,OAAA;QAAMkI,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCvI,OAAA;QAAAmI,QAAA,EAAOpH;MAAK;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDvI,OAAA;MAAKkI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnI,OAAA;QACEkI,SAAS,EAAC,mBAAmB;QAC7BQ,OAAO,EAAEjB,YAAa;QACtBkB,QAAQ,EAAEhI,OAAQ;QAAAwH,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvI,OAAA;QACEkI,SAAS,EAAC,iBAAiB;QAC3BQ,OAAO,EAAErG,uBAAwB;QACjCsG,QAAQ,EAAEhI,OAAO,IAAI,EAACP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,CAAC;QAAAwG,QAAA,EAE5CxH,OAAO,gBACNX,OAAA,CAAAE,SAAA;UAAAiI,QAAA,gBACEnI,OAAA;YAAMkI,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA,eAAE,CAAC,gBAEHvI,OAAA,CAAAE,SAAA;UAAAiI,QAAA,gBACEnI,OAAA;YAAMkI,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,QACpC,EAACtH,IAAI,GAAG2G,cAAc,CAAC3G,IAAI,CAAC4G,MAAM,GAAG5G,IAAI,CAACuH,QAAQ,CAAC,GAAGZ,cAAc,CAACxH,SAAS,CAACuB,WAAW,IAAI,CAAC,CAAC;QAAA,eACpG;MACH;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL1H,WAAW,iBACVb,OAAA;MAAKkI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCnI,OAAA;QAAKkI,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnI,OAAA;UAAMkI,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtCvI,OAAA;UAAAmI,QAAA,EAAM;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNvI,OAAA;QAAAmI,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJvI,OAAA;QAAKkI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnI,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnI,OAAA;YAAAmI,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvBvI,OAAA;YAAMkI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEtH,WAAW,CAAC+H;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNvI,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBnI,OAAA;YAAAmI,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpBvI,OAAA;YAAMkI,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEtH,WAAW,CAAC8F;UAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDvI,OAAA;MAAKkI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnI,OAAA;QAAKkI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnI,OAAA;UAAMkI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCvI,OAAA;UAAAmI,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNvI,OAAA;QAAAmI,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/H,EAAA,CA/VIL,gBAAgB;EAAA,QACHR,WAAW,EACNC,OAAO;AAAA;AAAAiJ,EAAA,GAFzB1I,gBAAgB;AAiWtB,eAAeA,gBAAgB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}