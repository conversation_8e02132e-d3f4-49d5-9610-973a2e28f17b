{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\account\\\\SecuritySettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SecuritySettings = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [activeSection, setActiveSection] = useState(null);\n\n  // Password Change State\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  // Two-Factor Authentication State\n  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);\n  const [showQRCode, setShowQRCode] = useState(false);\n  const [verificationCode, setVerificationCode] = useState('');\n\n  // Login Sessions State\n  const [sessions] = useState([{\n    id: 1,\n    device: 'Chrome on Windows',\n    location: 'Manila, Philippines',\n    lastActive: '2024-01-15 14:30',\n    current: true,\n    ipAddress: '*************'\n  }, {\n    id: 2,\n    device: 'Safari on iPhone',\n    location: 'Quezon City, Philippines',\n    lastActive: '2024-01-14 09:15',\n    current: false,\n    ipAddress: '*************'\n  }, {\n    id: 3,\n    device: 'Firefox on MacOS',\n    location: 'Makati, Philippines',\n    lastActive: '2024-01-13 16:45',\n    current: false,\n    ipAddress: '*************'\n  }]);\n  const handlePasswordChange = e => {\n    setPasswordData({\n      ...passwordData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handlePasswordSubmit = async e => {\n    e.preventDefault();\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setMessage('New passwords do not match');\n      return;\n    }\n    if (passwordData.newPassword.length < 8) {\n      setMessage('Password must be at least 8 characters long');\n      return;\n    }\n    setLoading(true);\n    setMessage('');\n    try {\n      // Mock API call - replace with actual password change API\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setMessage('Password changed successfully!');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n      setActiveSection(null);\n    } catch (error) {\n      setMessage('Failed to change password. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnable2FA = async () => {\n    setLoading(true);\n    try {\n      // Mock API call - replace with actual 2FA setup API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setShowQRCode(true);\n    } catch (error) {\n      setMessage('Failed to setup 2FA. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleVerify2FA = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Mock API call - replace with actual 2FA verification API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setTwoFactorEnabled(true);\n      setShowQRCode(false);\n      setVerificationCode('');\n      setMessage('Two-factor authentication enabled successfully!');\n    } catch (error) {\n      setMessage('Invalid verification code. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDisable2FA = async () => {\n    if (window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {\n      setLoading(true);\n      try {\n        // Mock API call - replace with actual 2FA disable API\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setTwoFactorEnabled(false);\n        setMessage('Two-factor authentication disabled.');\n      } catch (error) {\n        setMessage('Failed to disable 2FA. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleTerminateSession = async sessionId => {\n    if (window.confirm('Are you sure you want to terminate this session?')) {\n      try {\n        // Mock API call - replace with actual session termination API\n        await new Promise(resolve => setTimeout(resolve, 500));\n        setMessage('Session terminated successfully.');\n      } catch (error) {\n        setMessage('Failed to terminate session. Please try again.');\n      }\n    }\n  };\n  const getPasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n  const getStrengthLabel = strength => {\n    switch (strength) {\n      case 0:\n      case 1:\n        return {\n          label: 'Very Weak',\n          color: '#e74c3c'\n        };\n      case 2:\n        return {\n          label: 'Weak',\n          color: '#f39c12'\n        };\n      case 3:\n        return {\n          label: 'Fair',\n          color: '#f1c40f'\n        };\n      case 4:\n        return {\n          label: 'Good',\n          color: '#27ae60'\n        };\n      case 5:\n        return {\n          label: 'Strong',\n          color: '#2ecc71'\n        };\n      default:\n        return {\n          label: 'Unknown',\n          color: '#95a5a6'\n        };\n    }\n  };\n  const passwordStrength = getPasswordStrength(passwordData.newPassword);\n  const strengthInfo = getStrengthLabel(passwordStrength);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"security-settings\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Manage your account security and authentication settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 13\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('success') ? 'success' : 'error'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-sections\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"security-title\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"security-description\",\n              children: \"Change your account password to keep your account secure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            onClick: () => setActiveSection(activeSection === 'password' ? null : 'password'),\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                x: \"3\",\n                y: \"11\",\n                width: \"18\",\n                height: \"11\",\n                rx: \"2\",\n                ry: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"16\",\n                r: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this), \"Change Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 21\n        }, this), activeSection === 'password' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-content\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handlePasswordSubmit,\n            className: \"password-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Current Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"currentPassword\",\n                className: \"form-input\",\n                value: passwordData.currentPassword,\n                onChange: handlePasswordChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"newPassword\",\n                className: \"form-input\",\n                value: passwordData.newPassword,\n                onChange: handlePasswordChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 37\n              }, this), passwordData.newPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"password-strength\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"strength-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"strength-fill\",\n                    style: {\n                      width: `${passwordStrength / 5 * 100}%`,\n                      backgroundColor: strengthInfo.color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"strength-label\",\n                  style: {\n                    color: strengthInfo.color\n                  },\n                  children: strengthInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Confirm New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"confirmPassword\",\n                className: \"form-input\",\n                value: passwordData.confirmPassword,\n                onChange: handlePasswordChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn-primary\",\n                disabled: loading,\n                children: loading ? 'Changing Password...' : 'Change Password'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn-secondary\",\n                onClick: () => setActiveSection(null),\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"security-title\",\n              children: \"Two-Factor Authentication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"security-description\",\n              children: \"Add an extra layer of security to your account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"security-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-indicator ${twoFactorEnabled ? 'enabled' : 'disabled'}`,\n                children: twoFactorEnabled ? 'Enabled' : 'Disabled'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 25\n          }, this), !twoFactorEnabled ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-primary\",\n            onClick: handleEnable2FA,\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 33\n            }, this), \"Enable 2FA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-danger\",\n            onClick: handleDisable2FA,\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"9\",\n                y1: \"9\",\n                x2: \"15\",\n                y2: \"15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"15\",\n                y1: \"9\",\n                x2: \"9\",\n                y2: \"15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 33\n            }, this), \"Disable 2FA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 21\n        }, this), showQRCode && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"two-factor-setup\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Setup Two-Factor Authentication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Scan this QR code with your authenticator app:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-code-placeholder\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"150\",\n                height: \"150\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"QR Code Placeholder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleVerify2FA,\n              className: \"verification-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Verification Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-input\",\n                  value: verificationCode,\n                  onChange: e => setVerificationCode(e.target.value),\n                  placeholder: \"Enter 6-digit code\",\n                  maxLength: \"6\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn-primary\",\n                  disabled: loading,\n                  children: loading ? 'Verifying...' : 'Verify & Enable'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-secondary\",\n                  onClick: () => setShowQRCode(false),\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"security-title\",\n              children: \"Active Sessions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"security-description\",\n              children: \"Monitor and manage your active login sessions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sessions-list\",\n            children: sessions.map(session => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"session-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"session-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"session-device\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                      x: \"2\",\n                      y: \"3\",\n                      width: \"20\",\n                      height: \"14\",\n                      rx: \"2\",\n                      ry: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"8\",\n                      y1: \"21\",\n                      x2: \"16\",\n                      y2: \"21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"12\",\n                      y1: \"17\",\n                      x2: \"12\",\n                      y2: \"21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"device-name\",\n                      children: session.device\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"device-location\",\n                      children: session.location\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"device-details\",\n                      children: [\"Last active: \", session.lastActive, \" \\u2022 IP: \", session.ipAddress]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 41\n                }, this), session.current && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"current-session\",\n                  children: \"Current Session\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 37\n              }, this), !session.current && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-danger session-terminate\",\n                onClick: () => handleTerminateSession(session.id),\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"18\",\n                    y1: \"6\",\n                    x2: \"6\",\n                    y2: \"18\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"6\",\n                    y1: \"6\",\n                    x2: \"18\",\n                    y2: \"18\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 45\n                }, this), \"Terminate\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 41\n              }, this)]\n            }, session.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 9\n  }, this);\n};\n_s(SecuritySettings, \"NKjzBTw87kDj1YWCeP9wKJd/rpQ=\", false, function () {\n  return [useAuth];\n});\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "SecuritySettings", "_s", "user", "loading", "setLoading", "message", "setMessage", "activeSection", "setActiveSection", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "twoFactorEnabled", "setTwoFactorEnabled", "showQRCode", "setShowQRCode", "verificationCode", "setVerificationCode", "sessions", "id", "device", "location", "lastActive", "current", "ip<PERSON><PERSON><PERSON>", "handlePasswordChange", "e", "target", "name", "value", "handlePasswordSubmit", "preventDefault", "length", "Promise", "resolve", "setTimeout", "error", "handleEnable2FA", "handleVerify2FA", "handleDisable2FA", "window", "confirm", "handleTerminateSession", "sessionId", "getPasswordStrength", "password", "strength", "test", "getStrengthLabel", "label", "color", "passwordStrength", "strengthInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x", "y", "rx", "ry", "cx", "cy", "r", "d", "onSubmit", "type", "onChange", "required", "style", "backgroundColor", "disabled", "x1", "y1", "x2", "y2", "placeholder", "max<PERSON><PERSON><PERSON>", "map", "session", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/account/SecuritySettings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst SecuritySettings = () => {\n    const { user } = useAuth();\n    const [loading, setLoading] = useState(false);\n    const [message, setMessage] = useState('');\n    const [activeSection, setActiveSection] = useState(null);\n    \n    // Password Change State\n    const [passwordData, setPasswordData] = useState({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n\n    // Two-Factor Authentication State\n    const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);\n    const [showQRCode, setShowQRCode] = useState(false);\n    const [verificationCode, setVerificationCode] = useState('');\n\n    // Login Sessions State\n    const [sessions] = useState([\n        {\n            id: 1,\n            device: 'Chrome on Windows',\n            location: 'Manila, Philippines',\n            lastActive: '2024-01-15 14:30',\n            current: true,\n            ipAddress: '*************'\n        },\n        {\n            id: 2,\n            device: 'Safari on iPhone',\n            location: 'Quezon City, Philippines',\n            lastActive: '2024-01-14 09:15',\n            current: false,\n            ipAddress: '*************'\n        },\n        {\n            id: 3,\n            device: 'Firefox on MacOS',\n            location: 'Makati, Philippines',\n            lastActive: '2024-01-13 16:45',\n            current: false,\n            ipAddress: '*************'\n        }\n    ]);\n\n    const handlePasswordChange = (e) => {\n        setPasswordData({\n            ...passwordData,\n            [e.target.name]: e.target.value\n        });\n    };\n\n    const handlePasswordSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            setMessage('New passwords do not match');\n            return;\n        }\n\n        if (passwordData.newPassword.length < 8) {\n            setMessage('Password must be at least 8 characters long');\n            return;\n        }\n\n        setLoading(true);\n        setMessage('');\n\n        try {\n            // Mock API call - replace with actual password change API\n            await new Promise(resolve => setTimeout(resolve, 1500));\n            setMessage('Password changed successfully!');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n            setActiveSection(null);\n        } catch (error) {\n            setMessage('Failed to change password. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleEnable2FA = async () => {\n        setLoading(true);\n        try {\n            // Mock API call - replace with actual 2FA setup API\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            setShowQRCode(true);\n        } catch (error) {\n            setMessage('Failed to setup 2FA. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleVerify2FA = async (e) => {\n        e.preventDefault();\n        setLoading(true);\n        \n        try {\n            // Mock API call - replace with actual 2FA verification API\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            setTwoFactorEnabled(true);\n            setShowQRCode(false);\n            setVerificationCode('');\n            setMessage('Two-factor authentication enabled successfully!');\n        } catch (error) {\n            setMessage('Invalid verification code. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleDisable2FA = async () => {\n        if (window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {\n            setLoading(true);\n            try {\n                // Mock API call - replace with actual 2FA disable API\n                await new Promise(resolve => setTimeout(resolve, 1000));\n                setTwoFactorEnabled(false);\n                setMessage('Two-factor authentication disabled.');\n            } catch (error) {\n                setMessage('Failed to disable 2FA. Please try again.');\n            } finally {\n                setLoading(false);\n            }\n        }\n    };\n\n    const handleTerminateSession = async (sessionId) => {\n        if (window.confirm('Are you sure you want to terminate this session?')) {\n            try {\n                // Mock API call - replace with actual session termination API\n                await new Promise(resolve => setTimeout(resolve, 500));\n                setMessage('Session terminated successfully.');\n            } catch (error) {\n                setMessage('Failed to terminate session. Please try again.');\n            }\n        }\n    };\n\n    const getPasswordStrength = (password) => {\n        let strength = 0;\n        if (password.length >= 8) strength++;\n        if (/[a-z]/.test(password)) strength++;\n        if (/[A-Z]/.test(password)) strength++;\n        if (/[0-9]/.test(password)) strength++;\n        if (/[^A-Za-z0-9]/.test(password)) strength++;\n        return strength;\n    };\n\n    const getStrengthLabel = (strength) => {\n        switch (strength) {\n            case 0:\n            case 1: return { label: 'Very Weak', color: '#e74c3c' };\n            case 2: return { label: 'Weak', color: '#f39c12' };\n            case 3: return { label: 'Fair', color: '#f1c40f' };\n            case 4: return { label: 'Good', color: '#27ae60' };\n            case 5: return { label: 'Strong', color: '#2ecc71' };\n            default: return { label: 'Unknown', color: '#95a5a6' };\n        }\n    };\n\n    const passwordStrength = getPasswordStrength(passwordData.newPassword);\n    const strengthInfo = getStrengthLabel(passwordStrength);\n\n    return (\n        <div className=\"security-settings\">\n            <div className=\"section-header\">\n                <div>\n                    <h2 className=\"section-title\">Security Settings</h2>\n                    <p className=\"section-subtitle\">\n                        Manage your account security and authentication settings\n                    </p>\n                </div>\n            </div>\n\n            {message && (\n                <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>\n                    {message}\n                </div>\n            )}\n\n            <div className=\"security-sections\">\n                {/* Password Change Section */}\n                <div className=\"security-card\">\n                    <div className=\"security-card-header\">\n                        <div className=\"security-info\">\n                            <h3 className=\"security-title\">Password</h3>\n                            <p className=\"security-description\">\n                                Change your account password to keep your account secure\n                            </p>\n                        </div>\n                        <button\n                            className=\"btn-secondary\"\n                            onClick={() => setActiveSection(activeSection === 'password' ? null : 'password')}\n                        >\n                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"/>\n                                <circle cx=\"12\" cy=\"16\" r=\"1\"/>\n                                <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"/>\n                            </svg>\n                            Change Password\n                        </button>\n                    </div>\n\n                    {activeSection === 'password' && (\n                        <div className=\"security-content\">\n                            <form onSubmit={handlePasswordSubmit} className=\"password-form\">\n                                <div className=\"form-group\">\n                                    <label className=\"form-label\">Current Password</label>\n                                    <input\n                                        type=\"password\"\n                                        name=\"currentPassword\"\n                                        className=\"form-input\"\n                                        value={passwordData.currentPassword}\n                                        onChange={handlePasswordChange}\n                                        required\n                                    />\n                                </div>\n\n                                <div className=\"form-group\">\n                                    <label className=\"form-label\">New Password</label>\n                                    <input\n                                        type=\"password\"\n                                        name=\"newPassword\"\n                                        className=\"form-input\"\n                                        value={passwordData.newPassword}\n                                        onChange={handlePasswordChange}\n                                        required\n                                    />\n                                    {passwordData.newPassword && (\n                                        <div className=\"password-strength\">\n                                            <div className=\"strength-bar\">\n                                                <div \n                                                    className=\"strength-fill\"\n                                                    style={{ \n                                                        width: `${(passwordStrength / 5) * 100}%`,\n                                                        backgroundColor: strengthInfo.color\n                                                    }}\n                                                ></div>\n                                            </div>\n                                            <span \n                                                className=\"strength-label\"\n                                                style={{ color: strengthInfo.color }}\n                                            >\n                                                {strengthInfo.label}\n                                            </span>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"form-group\">\n                                    <label className=\"form-label\">Confirm New Password</label>\n                                    <input\n                                        type=\"password\"\n                                        name=\"confirmPassword\"\n                                        className=\"form-input\"\n                                        value={passwordData.confirmPassword}\n                                        onChange={handlePasswordChange}\n                                        required\n                                    />\n                                </div>\n\n                                <div className=\"form-actions\">\n                                    <button\n                                        type=\"submit\"\n                                        className=\"btn-primary\"\n                                        disabled={loading}\n                                    >\n                                        {loading ? 'Changing Password...' : 'Change Password'}\n                                    </button>\n                                    <button\n                                        type=\"button\"\n                                        className=\"btn-secondary\"\n                                        onClick={() => setActiveSection(null)}\n                                    >\n                                        Cancel\n                                    </button>\n                                </div>\n                            </form>\n                        </div>\n                    )}\n                </div>\n\n                {/* Two-Factor Authentication Section */}\n                <div className=\"security-card\">\n                    <div className=\"security-card-header\">\n                        <div className=\"security-info\">\n                            <h3 className=\"security-title\">Two-Factor Authentication</h3>\n                            <p className=\"security-description\">\n                                Add an extra layer of security to your account\n                            </p>\n                            <div className=\"security-status\">\n                                <span className={`status-indicator ${twoFactorEnabled ? 'enabled' : 'disabled'}`}>\n                                    {twoFactorEnabled ? 'Enabled' : 'Disabled'}\n                                </span>\n                            </div>\n                        </div>\n                        {!twoFactorEnabled ? (\n                            <button\n                                className=\"btn-primary\"\n                                onClick={handleEnable2FA}\n                                disabled={loading}\n                            >\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                    <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\n                                </svg>\n                                Enable 2FA\n                            </button>\n                        ) : (\n                            <button\n                                className=\"btn-danger\"\n                                onClick={handleDisable2FA}\n                                disabled={loading}\n                            >\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                    <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\n                                    <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>\n                                    <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/>\n                                </svg>\n                                Disable 2FA\n                            </button>\n                        )}\n                    </div>\n\n                    {showQRCode && (\n                        <div className=\"security-content\">\n                            <div className=\"two-factor-setup\">\n                                <h4>Setup Two-Factor Authentication</h4>\n                                <p>Scan this QR code with your authenticator app:</p>\n                                \n                                <div className=\"qr-code-placeholder\">\n                                    <svg width=\"150\" height=\"150\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                                        <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"/>\n                                        <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"/>\n                                        <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"/>\n                                        <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"/>\n                                    </svg>\n                                    <p>QR Code Placeholder</p>\n                                </div>\n\n                                <form onSubmit={handleVerify2FA} className=\"verification-form\">\n                                    <div className=\"form-group\">\n                                        <label className=\"form-label\">Verification Code</label>\n                                        <input\n                                            type=\"text\"\n                                            className=\"form-input\"\n                                            value={verificationCode}\n                                            onChange={(e) => setVerificationCode(e.target.value)}\n                                            placeholder=\"Enter 6-digit code\"\n                                            maxLength=\"6\"\n                                            required\n                                        />\n                                    </div>\n                                    <div className=\"form-actions\">\n                                        <button\n                                            type=\"submit\"\n                                            className=\"btn-primary\"\n                                            disabled={loading}\n                                        >\n                                            {loading ? 'Verifying...' : 'Verify & Enable'}\n                                        </button>\n                                        <button\n                                            type=\"button\"\n                                            className=\"btn-secondary\"\n                                            onClick={() => setShowQRCode(false)}\n                                        >\n                                            Cancel\n                                        </button>\n                                    </div>\n                                </form>\n                            </div>\n                        </div>\n                    )}\n                </div>\n\n                {/* Active Sessions Section */}\n                <div className=\"security-card\">\n                    <div className=\"security-card-header\">\n                        <div className=\"security-info\">\n                            <h3 className=\"security-title\">Active Sessions</h3>\n                            <p className=\"security-description\">\n                                Monitor and manage your active login sessions\n                            </p>\n                        </div>\n                    </div>\n\n                    <div className=\"security-content\">\n                        <div className=\"sessions-list\">\n                            {sessions.map(session => (\n                                <div key={session.id} className=\"session-item\">\n                                    <div className=\"session-info\">\n                                        <div className=\"session-device\">\n                                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"/>\n                                                <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"/>\n                                                <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"/>\n                                            </svg>\n                                            <div>\n                                                <h4 className=\"device-name\">{session.device}</h4>\n                                                <p className=\"device-location\">{session.location}</p>\n                                                <p className=\"device-details\">\n                                                    Last active: {session.lastActive} • IP: {session.ipAddress}\n                                                </p>\n                                            </div>\n                                        </div>\n                                        {session.current && (\n                                            <span className=\"current-session\">Current Session</span>\n                                        )}\n                                    </div>\n                                    {!session.current && (\n                                        <button\n                                            className=\"btn-danger session-terminate\"\n                                            onClick={() => handleTerminateSession(session.id)}\n                                        >\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n                                                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n                                            </svg>\n                                            Terminate\n                                        </button>\n                                    )}\n                                </div>\n                            ))}\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC;IAC7Ce,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACwB,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,CACxB;IACIyB,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,mBAAmB;IAC3BC,QAAQ,EAAE,qBAAqB;IAC/BC,UAAU,EAAE,kBAAkB;IAC9BC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE;EACf,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,kBAAkB;IAC1BC,QAAQ,EAAE,0BAA0B;IACpCC,UAAU,EAAE,kBAAkB;IAC9BC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;EACf,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,kBAAkB;IAC1BC,QAAQ,EAAE,qBAAqB;IAC/BC,UAAU,EAAE,kBAAkB;IAC9BC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;EACf,CAAC,CACJ,CAAC;EAEF,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAChClB,eAAe,CAAC;MACZ,GAAGD,YAAY;MACf,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,oBAAoB,GAAG,MAAOJ,CAAC,IAAK;IACtCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAIxB,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC3DP,UAAU,CAAC,4BAA4B,CAAC;MACxC;IACJ;IAEA,IAAIG,YAAY,CAACG,WAAW,CAACsB,MAAM,GAAG,CAAC,EAAE;MACrC5B,UAAU,CAAC,6CAA6C,CAAC;MACzD;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA;MACA,MAAM,IAAI6B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD9B,UAAU,CAAC,gCAAgC,CAAC;MAC5CI,eAAe,CAAC;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACrB,CAAC,CAAC;MACFL,gBAAgB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACZhC,UAAU,CAAC,8CAA8C,CAAC;IAC9D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMmC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChCnC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA;MACA,MAAM,IAAI+B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDnB,aAAa,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACZhC,UAAU,CAAC,wCAAwC,CAAC;IACxD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMoC,eAAe,GAAG,MAAOZ,CAAC,IAAK;IACjCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB7B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA;MACA,MAAM,IAAI+B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDrB,mBAAmB,CAAC,IAAI,CAAC;MACzBE,aAAa,CAAC,KAAK,CAAC;MACpBE,mBAAmB,CAAC,EAAE,CAAC;MACvBb,UAAU,CAAC,iDAAiD,CAAC;IACjE,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACZhC,UAAU,CAAC,8CAA8C,CAAC;IAC9D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIC,MAAM,CAACC,OAAO,CAAC,sGAAsG,CAAC,EAAE;MACxHvC,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACA;QACA,MAAM,IAAI+B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QACvDrB,mBAAmB,CAAC,KAAK,CAAC;QAC1BT,UAAU,CAAC,qCAAqC,CAAC;MACrD,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACZhC,UAAU,CAAC,0CAA0C,CAAC;MAC1D,CAAC,SAAS;QACNF,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ;EACJ,CAAC;EAED,MAAMwC,sBAAsB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAIH,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MACpE,IAAI;QACA;QACA,MAAM,IAAIR,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD9B,UAAU,CAAC,kCAAkC,CAAC;MAClD,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACZhC,UAAU,CAAC,gDAAgD,CAAC;MAChE;IACJ;EACJ,CAAC;EAED,MAAMwC,mBAAmB,GAAIC,QAAQ,IAAK;IACtC,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAID,QAAQ,CAACb,MAAM,IAAI,CAAC,EAAEc,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAI,cAAc,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IAC7C,OAAOA,QAAQ;EACnB,CAAC;EAED,MAAME,gBAAgB,GAAIF,QAAQ,IAAK;IACnC,QAAQA,QAAQ;MACZ,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO;UAAEG,KAAK,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC;MACvD,KAAK,CAAC;QAAE,OAAO;UAAED,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC;MAClD,KAAK,CAAC;QAAE,OAAO;UAAED,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC;MAClD,KAAK,CAAC;QAAE,OAAO;UAAED,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC;MAClD,KAAK,CAAC;QAAE,OAAO;UAAED,KAAK,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAC;MACpD;QAAS,OAAO;UAAED,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU,CAAC;IAC1D;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGP,mBAAmB,CAACrC,YAAY,CAACG,WAAW,CAAC;EACtE,MAAM0C,YAAY,GAAGJ,gBAAgB,CAACG,gBAAgB,CAAC;EAEvD,oBACItD,OAAA;IAAKwD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9BzD,OAAA;MAAKwD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BzD,OAAA;QAAAyD,QAAA,gBACIzD,OAAA;UAAIwD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD7D,OAAA;UAAGwD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELvD,OAAO,iBACJN,OAAA;MAAKwD,SAAS,EAAE,WAAWlD,OAAO,CAACwD,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,OAAO,EAAG;MAAAL,QAAA,EAC1EnD;IAAO;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAED7D,OAAA;MAAKwD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAE9BzD,OAAA;QAAKwD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCzD,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BzD,OAAA;cAAIwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C7D,OAAA;cAAGwD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YACIwD,SAAS,EAAC,eAAe;YACzBO,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAACD,aAAa,KAAK,UAAU,GAAG,IAAI,GAAG,UAAU,CAAE;YAAAiD,QAAA,gBAElFzD,OAAA;cAAKgE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAZ,QAAA,gBAC7FzD,OAAA;gBAAMsE,CAAC,EAAC,GAAG;gBAACC,CAAC,EAAC,IAAI;gBAACP,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACO,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACzD7D,OAAA;gBAAQ0E,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC/B7D,OAAA;gBAAM6E,CAAC,EAAC;cAA0B;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,mBAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAELrD,aAAa,KAAK,UAAU,iBACzBR,OAAA;UAAKwD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BzD,OAAA;YAAM8E,QAAQ,EAAE7C,oBAAqB;YAACuB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC3DzD,OAAA;cAAKwD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBzD,OAAA;gBAAOwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtD7D,OAAA;gBACI+E,IAAI,EAAC,UAAU;gBACfhD,IAAI,EAAC,iBAAiB;gBACtByB,SAAS,EAAC,YAAY;gBACtBxB,KAAK,EAAEtB,YAAY,CAACE,eAAgB;gBACpCoE,QAAQ,EAAEpD,oBAAqB;gBAC/BqD,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBzD,OAAA;gBAAOwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD7D,OAAA;gBACI+E,IAAI,EAAC,UAAU;gBACfhD,IAAI,EAAC,aAAa;gBAClByB,SAAS,EAAC,YAAY;gBACtBxB,KAAK,EAAEtB,YAAY,CAACG,WAAY;gBAChCmE,QAAQ,EAAEpD,oBAAqB;gBAC/BqD,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACDnD,YAAY,CAACG,WAAW,iBACrBb,OAAA;gBAAKwD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BzD,OAAA;kBAAKwD,SAAS,EAAC,cAAc;kBAAAC,QAAA,eACzBzD,OAAA;oBACIwD,SAAS,EAAC,eAAe;oBACzB0B,KAAK,EAAE;sBACHlB,KAAK,EAAE,GAAIV,gBAAgB,GAAG,CAAC,GAAI,GAAG,GAAG;sBACzC6B,eAAe,EAAE5B,YAAY,CAACF;oBAClC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN7D,OAAA;kBACIwD,SAAS,EAAC,gBAAgB;kBAC1B0B,KAAK,EAAE;oBAAE7B,KAAK,EAAEE,YAAY,CAACF;kBAAM,CAAE;kBAAAI,QAAA,EAEpCF,YAAY,CAACH;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBzD,OAAA;gBAAOwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1D7D,OAAA;gBACI+E,IAAI,EAAC,UAAU;gBACfhD,IAAI,EAAC,iBAAiB;gBACtByB,SAAS,EAAC,YAAY;gBACtBxB,KAAK,EAAEtB,YAAY,CAACI,eAAgB;gBACpCkE,QAAQ,EAAEpD,oBAAqB;gBAC/BqD,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBzD,OAAA;gBACI+E,IAAI,EAAC,QAAQ;gBACbvB,SAAS,EAAC,aAAa;gBACvB4B,QAAQ,EAAEhF,OAAQ;gBAAAqD,QAAA,EAEjBrD,OAAO,GAAG,sBAAsB,GAAG;cAAiB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACT7D,OAAA;gBACI+E,IAAI,EAAC,QAAQ;gBACbvB,SAAS,EAAC,eAAe;gBACzBO,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,IAAI,CAAE;gBAAAgD,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCzD,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BzD,OAAA;cAAIwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D7D,OAAA;cAAGwD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7D,OAAA;cAAKwD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BzD,OAAA;gBAAMwD,SAAS,EAAE,oBAAoBzC,gBAAgB,GAAG,SAAS,GAAG,UAAU,EAAG;gBAAA0C,QAAA,EAC5E1C,gBAAgB,GAAG,SAAS,GAAG;cAAU;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACL,CAAC9C,gBAAgB,gBACdf,OAAA;YACIwD,SAAS,EAAC,aAAa;YACvBO,OAAO,EAAEvB,eAAgB;YACzB4C,QAAQ,EAAEhF,OAAQ;YAAAqD,QAAA,gBAElBzD,OAAA;cAAKgE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAZ,QAAA,eAC7FzD,OAAA;gBAAM6E,CAAC,EAAC;cAA6C;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,cAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET7D,OAAA;YACIwD,SAAS,EAAC,YAAY;YACtBO,OAAO,EAAErB,gBAAiB;YAC1B0C,QAAQ,EAAEhF,OAAQ;YAAAqD,QAAA,gBAElBzD,OAAA;cAAKgE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAZ,QAAA,gBAC7FzD,OAAA;gBAAM6E,CAAC,EAAC;cAA6C;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACvD7D,OAAA;gBAAMqF,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC;cAAI;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACrC7D,OAAA;gBAAMqF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAEL5C,UAAU,iBACPjB,OAAA;UAAKwD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BzD,OAAA;YAAKwD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7BzD,OAAA;cAAAyD,QAAA,EAAI;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC7D,OAAA;cAAAyD,QAAA,EAAG;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAErD7D,OAAA;cAAKwD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCzD,OAAA;gBAAKgE,KAAK,EAAC,KAAK;gBAACC,MAAM,EAAC,KAAK;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAAAZ,QAAA,gBAC/FzD,OAAA;kBAAMsE,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACP,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACxC7D,OAAA;kBAAMsE,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACP,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACzC7D,OAAA;kBAAMsE,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACP,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACzC7D,OAAA;kBAAMsE,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACP,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN7D,OAAA;gBAAAyD,QAAA,EAAG;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAEN7D,OAAA;cAAM8E,QAAQ,EAAErC,eAAgB;cAACe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC1DzD,OAAA;gBAAKwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBzD,OAAA;kBAAOwD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvD7D,OAAA;kBACI+E,IAAI,EAAC,MAAM;kBACXvB,SAAS,EAAC,YAAY;kBACtBxB,KAAK,EAAEb,gBAAiB;kBACxB6D,QAAQ,EAAGnD,CAAC,IAAKT,mBAAmB,CAACS,CAAC,CAACC,MAAM,CAACE,KAAK,CAAE;kBACrDyD,WAAW,EAAC,oBAAoB;kBAChCC,SAAS,EAAC,GAAG;kBACbT,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN7D,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBzD,OAAA;kBACI+E,IAAI,EAAC,QAAQ;kBACbvB,SAAS,EAAC,aAAa;kBACvB4B,QAAQ,EAAEhF,OAAQ;kBAAAqD,QAAA,EAEjBrD,OAAO,GAAG,cAAc,GAAG;gBAAiB;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACT7D,OAAA;kBACI+E,IAAI,EAAC,QAAQ;kBACbvB,SAAS,EAAC,eAAe;kBACzBO,OAAO,EAAEA,CAAA,KAAM7C,aAAa,CAAC,KAAK,CAAE;kBAAAuC,QAAA,EACvC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN7D,OAAA;QAAKwD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACjCzD,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BzD,OAAA;cAAIwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD7D,OAAA;cAAGwD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7D,OAAA;UAAKwD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BzD,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzBpC,QAAQ,CAACsE,GAAG,CAACC,OAAO,iBACjB5F,OAAA;cAAsBwD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1CzD,OAAA;gBAAKwD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBzD,OAAA;kBAAKwD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3BzD,OAAA;oBAAKgE,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAZ,QAAA,gBAC7FzD,OAAA;sBAAMsE,CAAC,EAAC,GAAG;sBAACC,CAAC,EAAC,GAAG;sBAACP,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACO,EAAE,EAAC,GAAG;sBAACC,EAAE,EAAC;oBAAG;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACxD7D,OAAA;sBAAMqF,EAAE,EAAC,GAAG;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC;oBAAI;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtC7D,OAAA;sBAAMqF,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC;oBAAI;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACN7D,OAAA;oBAAAyD,QAAA,gBACIzD,OAAA;sBAAIwD,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEmC,OAAO,CAACrE;oBAAM;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjD7D,OAAA;sBAAGwD,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAEmC,OAAO,CAACpE;oBAAQ;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrD7D,OAAA;sBAAGwD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAAC,eACb,EAACmC,OAAO,CAACnE,UAAU,EAAC,cAAO,EAACmE,OAAO,CAACjE,SAAS;oBAAA;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACL+B,OAAO,CAAClE,OAAO,iBACZ1B,OAAA;kBAAMwD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EACL,CAAC+B,OAAO,CAAClE,OAAO,iBACb1B,OAAA;gBACIwD,SAAS,EAAC,8BAA8B;gBACxCO,OAAO,EAAEA,CAAA,KAAMlB,sBAAsB,CAAC+C,OAAO,CAACtE,EAAE,CAAE;gBAAAmC,QAAA,gBAElDzD,OAAA;kBAAKgE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,gBAC7FzD,OAAA;oBAAMqF,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC;kBAAI;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACrC7D,OAAA;oBAAMqF,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC;kBAAI;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,aAEV;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA,GA/BK+B,OAAO,CAACtE,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCf,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC3D,EAAA,CAnbID,gBAAgB;EAAA,QACDH,OAAO;AAAA;AAAA+F,EAAA,GADtB5F,gBAAgB;AAqbtB,eAAeA,gBAAgB;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}