{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport websocketService from '../services/websocketService';\nimport { useAuth } from './useAuth';\nexport const useWebSocket = () => {\n  _s();\n  const {\n    token,\n    user\n  } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [notifications, setNotifications] = useState([]);\n\n  // Connect to WebSocket when user is authenticated\n  useEffect(() => {\n    if (token && user && (user.role === 'Admin' || user.role === 'Employee')) {\n      connectWebSocket();\n    } else {\n      disconnectWebSocket();\n    }\n    return () => {\n      disconnectWebSocket();\n    };\n  }, [token, user]);\n  const connectWebSocket = async () => {\n    try {\n      setConnectionStatus('connecting');\n      await websocketService.connect(token);\n      setIsConnected(true);\n      setConnectionStatus('connected');\n\n      // Subscribe to relevant updates based on user role\n      if (user.role === 'Admin' || user.role === 'Employee') {\n        websocketService.subscribeToInventory();\n        websocketService.subscribeToOrders();\n        websocketService.subscribeToDashboard();\n      }\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      setIsConnected(false);\n      setConnectionStatus('error');\n    }\n  };\n  const disconnectWebSocket = () => {\n    websocketService.disconnect();\n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  };\n\n  // Add notification to the list\n  const addNotification = useCallback(notification => {\n    const newNotification = {\n      id: notification.id || Date.now(),\n      title: notification.title,\n      message: notification.message,\n      type: notification.type || 'info',\n      timestamp: notification.timestamp || new Date().toISOString(),\n      read: false\n    };\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  }, []);\n\n  // Remove notification by ID\n  const removeNotification = useCallback(id => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  // Clear all notifications\n  const clearAllNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Set up event listeners\n  useEffect(() => {\n    const handleNotification = data => {\n      addNotification(data);\n    };\n    const handleInventoryUpdate = data => {\n      addNotification({\n        title: 'Inventory Updated',\n        message: `${data.item.name} stock updated to ${data.item.currentStock}`,\n        type: 'info'\n      });\n    };\n    const handleLowStockAlert = data => {\n      addNotification({\n        title: 'Low Stock Alert',\n        message: `${data.item.name} is running low (${data.item.currentStock} remaining)`,\n        type: 'warning'\n      });\n    };\n    const handleOrderUpdate = data => {\n      var _data$data, _data$data2;\n      addNotification({\n        title: 'Order Update',\n        message: `Order #${((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.orderNumber) || data.orderId} status: ${((_data$data2 = data.data) === null || _data$data2 === void 0 ? void 0 : _data$data2.status) || data.status}`,\n        type: 'info'\n      });\n    };\n    const handleNewOrder = data => {\n      var _data$data3, _data$data4, _data$data5;\n      addNotification({\n        title: 'New Order Received',\n        message: `New paid order #${((_data$data3 = data.data) === null || _data$data3 === void 0 ? void 0 : _data$data3.orderNumber) || data.orderNumber} from ${((_data$data4 = data.data) === null || _data$data4 === void 0 ? void 0 : _data$data4.customerName) || data.customerName} - ₱${((_data$data5 = data.data) === null || _data$data5 === void 0 ? void 0 : _data$data5.totalAmount) || data.totalAmount}`,\n        type: 'success'\n      });\n    };\n    const handleDashboardUpdate = data => {\n      // Dashboard updates are usually silent, just trigger re-renders\n      console.log('Dashboard updated:', data);\n    };\n\n    // Register event listeners\n    websocketService.on('notification', handleNotification);\n    websocketService.on('inventoryUpdated', handleInventoryUpdate);\n    websocketService.on('lowStockAlert', handleLowStockAlert);\n    websocketService.on('orderUpdated', handleOrderUpdate);\n    websocketService.on('dashboardUpdated', handleDashboardUpdate);\n    return () => {\n      // Cleanup event listeners\n      websocketService.off('notification', handleNotification);\n      websocketService.off('inventoryUpdated', handleInventoryUpdate);\n      websocketService.off('lowStockAlert', handleLowStockAlert);\n      websocketService.off('orderUpdated', handleOrderUpdate);\n      websocketService.off('dashboardUpdated', handleDashboardUpdate);\n    };\n  }, [addNotification]);\n\n  // Monitor connection status\n  useEffect(() => {\n    const checkConnectionStatus = () => {\n      const status = websocketService.getConnectionStatus();\n      setConnectionStatus(status);\n      setIsConnected(status === 'connected');\n    };\n    const interval = setInterval(checkConnectionStatus, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Send event to server\n  const sendEvent = useCallback((event, data) => {\n    websocketService.sendEvent(event, data);\n  }, []);\n\n  // Show notification helper\n  const showNotification = useCallback((title, message, type = 'info') => {\n    websocketService.showNotification(title, message, type);\n  }, []);\n  return {\n    isConnected,\n    connectionStatus,\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    sendEvent,\n    showNotification,\n    connect: connectWebSocket,\n    disconnect: disconnectWebSocket\n  };\n};\n_s(useWebSocket, \"Jr1xA65EDrX9cHGo0byXqMRr0d4=\", false, function () {\n  return [useAuth];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "websocketService", "useAuth", "useWebSocket", "_s", "token", "user", "isConnected", "setIsConnected", "connectionStatus", "setConnectionStatus", "notifications", "setNotifications", "role", "connectWebSocket", "disconnectWebSocket", "connect", "subscribeToInventory", "subscribeToOrders", "subscribeToDashboard", "error", "console", "disconnect", "addNotification", "notification", "newNotification", "id", "Date", "now", "title", "message", "type", "timestamp", "toISOString", "read", "prev", "setTimeout", "removeNotification", "filter", "clearAllNotifications", "handleNotification", "data", "handleInventoryUpdate", "item", "name", "currentStock", "handleLowStockAlert", "handleOrderUpdate", "_data$data", "_data$data2", "orderNumber", "orderId", "status", "handleNewOrder", "_data$data3", "_data$data4", "_data$data5", "customerName", "totalAmount", "handleDashboardUpdate", "log", "on", "off", "checkConnectionStatus", "getConnectionStatus", "interval", "setInterval", "clearInterval", "sendEvent", "event", "showNotification"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/hooks/useWebSocket.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport websocketService from '../services/websocketService';\nimport { useAuth } from './useAuth';\n\nexport const useWebSocket = () => {\n  const { token, user } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [notifications, setNotifications] = useState([]);\n\n  // Connect to WebSocket when user is authenticated\n  useEffect(() => {\n    if (token && user && (user.role === 'Admin' || user.role === 'Employee')) {\n      connectWebSocket();\n    } else {\n      disconnectWebSocket();\n    }\n\n    return () => {\n      disconnectWebSocket();\n    };\n  }, [token, user]);\n\n  const connectWebSocket = async () => {\n    try {\n      setConnectionStatus('connecting');\n      await websocketService.connect(token);\n      setIsConnected(true);\n      setConnectionStatus('connected');\n      \n      // Subscribe to relevant updates based on user role\n      if (user.role === 'Admin' || user.role === 'Employee') {\n        websocketService.subscribeToInventory();\n        websocketService.subscribeToOrders();\n        websocketService.subscribeToDashboard();\n      }\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      setIsConnected(false);\n      setConnectionStatus('error');\n    }\n  };\n\n  const disconnectWebSocket = () => {\n    websocketService.disconnect();\n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  };\n\n  // Add notification to the list\n  const addNotification = useCallback((notification) => {\n    const newNotification = {\n      id: notification.id || Date.now(),\n      title: notification.title,\n      message: notification.message,\n      type: notification.type || 'info',\n      timestamp: notification.timestamp || new Date().toISOString(),\n      read: false\n    };\n\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  }, []);\n\n  // Remove notification by ID\n  const removeNotification = useCallback((id) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  // Clear all notifications\n  const clearAllNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Set up event listeners\n  useEffect(() => {\n    const handleNotification = (data) => {\n      addNotification(data);\n    };\n\n    const handleInventoryUpdate = (data) => {\n      addNotification({\n        title: 'Inventory Updated',\n        message: `${data.item.name} stock updated to ${data.item.currentStock}`,\n        type: 'info'\n      });\n    };\n\n    const handleLowStockAlert = (data) => {\n      addNotification({\n        title: 'Low Stock Alert',\n        message: `${data.item.name} is running low (${data.item.currentStock} remaining)`,\n        type: 'warning'\n      });\n    };\n\n    const handleOrderUpdate = (data) => {\n      addNotification({\n        title: 'Order Update',\n        message: `Order #${data.data?.orderNumber || data.orderId} status: ${data.data?.status || data.status}`,\n        type: 'info'\n      });\n    };\n\n    const handleNewOrder = (data) => {\n      addNotification({\n        title: 'New Order Received',\n        message: `New paid order #${data.data?.orderNumber || data.orderNumber} from ${data.data?.customerName || data.customerName} - ₱${data.data?.totalAmount || data.totalAmount}`,\n        type: 'success'\n      });\n    };\n\n    const handleDashboardUpdate = (data) => {\n      // Dashboard updates are usually silent, just trigger re-renders\n      console.log('Dashboard updated:', data);\n    };\n\n    // Register event listeners\n    websocketService.on('notification', handleNotification);\n    websocketService.on('inventoryUpdated', handleInventoryUpdate);\n    websocketService.on('lowStockAlert', handleLowStockAlert);\n    websocketService.on('orderUpdated', handleOrderUpdate);\n    websocketService.on('dashboardUpdated', handleDashboardUpdate);\n\n    return () => {\n      // Cleanup event listeners\n      websocketService.off('notification', handleNotification);\n      websocketService.off('inventoryUpdated', handleInventoryUpdate);\n      websocketService.off('lowStockAlert', handleLowStockAlert);\n      websocketService.off('orderUpdated', handleOrderUpdate);\n      websocketService.off('dashboardUpdated', handleDashboardUpdate);\n    };\n  }, [addNotification]);\n\n  // Monitor connection status\n  useEffect(() => {\n    const checkConnectionStatus = () => {\n      const status = websocketService.getConnectionStatus();\n      setConnectionStatus(status);\n      setIsConnected(status === 'connected');\n    };\n\n    const interval = setInterval(checkConnectionStatus, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Send event to server\n  const sendEvent = useCallback((event, data) => {\n    websocketService.sendEvent(event, data);\n  }, []);\n\n  // Show notification helper\n  const showNotification = useCallback((title, message, type = 'info') => {\n    websocketService.showNotification(title, message, type);\n  }, []);\n\n  return {\n    isConnected,\n    connectionStatus,\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    sendEvent,\n    showNotification,\n    connect: connectWebSocket,\n    disconnect: disconnectWebSocket\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,OAAO,QAAQ,WAAW;AAEnC,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGJ,OAAO,CAAC,CAAC;EACjC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,cAAc,CAAC;EACxE,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIM,KAAK,IAAIC,IAAI,KAAKA,IAAI,CAACO,IAAI,KAAK,OAAO,IAAIP,IAAI,CAACO,IAAI,KAAK,UAAU,CAAC,EAAE;MACxEC,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLC,mBAAmB,CAAC,CAAC;IACvB;IAEA,OAAO,MAAM;MACXA,mBAAmB,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACV,KAAK,EAAEC,IAAI,CAAC,CAAC;EAEjB,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFJ,mBAAmB,CAAC,YAAY,CAAC;MACjC,MAAMT,gBAAgB,CAACe,OAAO,CAACX,KAAK,CAAC;MACrCG,cAAc,CAAC,IAAI,CAAC;MACpBE,mBAAmB,CAAC,WAAW,CAAC;;MAEhC;MACA,IAAIJ,IAAI,CAACO,IAAI,KAAK,OAAO,IAAIP,IAAI,CAACO,IAAI,KAAK,UAAU,EAAE;QACrDZ,gBAAgB,CAACgB,oBAAoB,CAAC,CAAC;QACvChB,gBAAgB,CAACiB,iBAAiB,CAAC,CAAC;QACpCjB,gBAAgB,CAACkB,oBAAoB,CAAC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDZ,cAAc,CAAC,KAAK,CAAC;MACrBE,mBAAmB,CAAC,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChCd,gBAAgB,CAACqB,UAAU,CAAC,CAAC;IAC7Bd,cAAc,CAAC,KAAK,CAAC;IACrBE,mBAAmB,CAAC,cAAc,CAAC;EACrC,CAAC;;EAED;EACA,MAAMa,eAAe,GAAGvB,WAAW,CAAEwB,YAAY,IAAK;IACpD,MAAMC,eAAe,GAAG;MACtBC,EAAE,EAAEF,YAAY,CAACE,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;MACjCC,KAAK,EAAEL,YAAY,CAACK,KAAK;MACzBC,OAAO,EAAEN,YAAY,CAACM,OAAO;MAC7BC,IAAI,EAAEP,YAAY,CAACO,IAAI,IAAI,MAAM;MACjCC,SAAS,EAAER,YAAY,CAACQ,SAAS,IAAI,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MAC7DC,IAAI,EAAE;IACR,CAAC;IAEDtB,gBAAgB,CAACuB,IAAI,IAAI,CAACV,eAAe,EAAE,GAAGU,IAAI,CAAC,CAAC;;IAEpD;IACA,IAAIX,YAAY,CAACO,IAAI,KAAK,OAAO,EAAE;MACjCK,UAAU,CAAC,MAAM;QACfC,kBAAkB,CAACZ,eAAe,CAACC,EAAE,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,kBAAkB,GAAGrC,WAAW,CAAE0B,EAAE,IAAK;IAC7Cd,gBAAgB,CAACuB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACd,YAAY,IAAIA,YAAY,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC/E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,qBAAqB,GAAGvC,WAAW,CAAC,MAAM;IAC9CY,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAb,SAAS,CAAC,MAAM;IACd,MAAMyC,kBAAkB,GAAIC,IAAI,IAAK;MACnClB,eAAe,CAACkB,IAAI,CAAC;IACvB,CAAC;IAED,MAAMC,qBAAqB,GAAID,IAAI,IAAK;MACtClB,eAAe,CAAC;QACdM,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE,GAAGW,IAAI,CAACE,IAAI,CAACC,IAAI,qBAAqBH,IAAI,CAACE,IAAI,CAACE,YAAY,EAAE;QACvEd,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMe,mBAAmB,GAAIL,IAAI,IAAK;MACpClB,eAAe,CAAC;QACdM,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,GAAGW,IAAI,CAACE,IAAI,CAACC,IAAI,oBAAoBH,IAAI,CAACE,IAAI,CAACE,YAAY,aAAa;QACjFd,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMgB,iBAAiB,GAAIN,IAAI,IAAK;MAAA,IAAAO,UAAA,EAAAC,WAAA;MAClC1B,eAAe,CAAC;QACdM,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE,UAAU,EAAAkB,UAAA,GAAAP,IAAI,CAACA,IAAI,cAAAO,UAAA,uBAATA,UAAA,CAAWE,WAAW,KAAIT,IAAI,CAACU,OAAO,YAAY,EAAAF,WAAA,GAAAR,IAAI,CAACA,IAAI,cAAAQ,WAAA,uBAATA,WAAA,CAAWG,MAAM,KAAIX,IAAI,CAACW,MAAM,EAAE;QACvGrB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMsB,cAAc,GAAIZ,IAAI,IAAK;MAAA,IAAAa,WAAA,EAAAC,WAAA,EAAAC,WAAA;MAC/BjC,eAAe,CAAC;QACdM,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE,mBAAmB,EAAAwB,WAAA,GAAAb,IAAI,CAACA,IAAI,cAAAa,WAAA,uBAATA,WAAA,CAAWJ,WAAW,KAAIT,IAAI,CAACS,WAAW,SAAS,EAAAK,WAAA,GAAAd,IAAI,CAACA,IAAI,cAAAc,WAAA,uBAATA,WAAA,CAAWE,YAAY,KAAIhB,IAAI,CAACgB,YAAY,OAAO,EAAAD,WAAA,GAAAf,IAAI,CAACA,IAAI,cAAAe,WAAA,uBAATA,WAAA,CAAWE,WAAW,KAAIjB,IAAI,CAACiB,WAAW,EAAE;QAC9K3B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAM4B,qBAAqB,GAAIlB,IAAI,IAAK;MACtC;MACApB,OAAO,CAACuC,GAAG,CAAC,oBAAoB,EAAEnB,IAAI,CAAC;IACzC,CAAC;;IAED;IACAxC,gBAAgB,CAAC4D,EAAE,CAAC,cAAc,EAAErB,kBAAkB,CAAC;IACvDvC,gBAAgB,CAAC4D,EAAE,CAAC,kBAAkB,EAAEnB,qBAAqB,CAAC;IAC9DzC,gBAAgB,CAAC4D,EAAE,CAAC,eAAe,EAAEf,mBAAmB,CAAC;IACzD7C,gBAAgB,CAAC4D,EAAE,CAAC,cAAc,EAAEd,iBAAiB,CAAC;IACtD9C,gBAAgB,CAAC4D,EAAE,CAAC,kBAAkB,EAAEF,qBAAqB,CAAC;IAE9D,OAAO,MAAM;MACX;MACA1D,gBAAgB,CAAC6D,GAAG,CAAC,cAAc,EAAEtB,kBAAkB,CAAC;MACxDvC,gBAAgB,CAAC6D,GAAG,CAAC,kBAAkB,EAAEpB,qBAAqB,CAAC;MAC/DzC,gBAAgB,CAAC6D,GAAG,CAAC,eAAe,EAAEhB,mBAAmB,CAAC;MAC1D7C,gBAAgB,CAAC6D,GAAG,CAAC,cAAc,EAAEf,iBAAiB,CAAC;MACvD9C,gBAAgB,CAAC6D,GAAG,CAAC,kBAAkB,EAAEH,qBAAqB,CAAC;IACjE,CAAC;EACH,CAAC,EAAE,CAACpC,eAAe,CAAC,CAAC;;EAErB;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMgE,qBAAqB,GAAGA,CAAA,KAAM;MAClC,MAAMX,MAAM,GAAGnD,gBAAgB,CAAC+D,mBAAmB,CAAC,CAAC;MACrDtD,mBAAmB,CAAC0C,MAAM,CAAC;MAC3B5C,cAAc,CAAC4C,MAAM,KAAK,WAAW,CAAC;IACxC,CAAC;IAED,MAAMa,QAAQ,GAAGC,WAAW,CAACH,qBAAqB,EAAE,IAAI,CAAC;IACzD,OAAO,MAAMI,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,SAAS,GAAGpE,WAAW,CAAC,CAACqE,KAAK,EAAE5B,IAAI,KAAK;IAC7CxC,gBAAgB,CAACmE,SAAS,CAACC,KAAK,EAAE5B,IAAI,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6B,gBAAgB,GAAGtE,WAAW,CAAC,CAAC6B,KAAK,EAAEC,OAAO,EAAEC,IAAI,GAAG,MAAM,KAAK;IACtE9B,gBAAgB,CAACqE,gBAAgB,CAACzC,KAAK,EAAEC,OAAO,EAAEC,IAAI,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLxB,WAAW;IACXE,gBAAgB;IAChBE,aAAa;IACbY,eAAe;IACfc,kBAAkB;IAClBE,qBAAqB;IACrB6B,SAAS;IACTE,gBAAgB;IAChBtD,OAAO,EAAEF,gBAAgB;IACzBQ,UAAU,EAAEP;EACd,CAAC;AACH,CAAC;AAACX,EAAA,CA1KWD,YAAY;EAAA,QACCD,OAAO;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}