const { connectDB, getPool } = require('./config/database');

async function testDatabase() {
  try {
    console.log('Connecting to database...');
    await connectDB();
    const pool = getPool();
    console.log('Database connection established');

    // Check current database context
    const dbContext = await pool.request().query('SELECT DB_NAME() as CurrentDatabase');
    console.log('Current database:', dbContext.recordset[0].CurrentDatabase);
    
    // Check if Orders table exists
    const result = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'Orders'
    `);
    
    console.log('Orders table exists:', result.recordset.length > 0);
    
    if (result.recordset.length > 0) {
      // Check if there are any orders
      const orderCount = await pool.request().query('SELECT COUNT(*) as count FROM Orders');
      console.log('Number of orders:', orderCount.recordset[0].count);

      // Test the exact query from Order model
      try {
        const testQuery = `
          SELECT o.*,
                 COUNT(oi.OrderItemID) as ItemCount,
                 COALESCE(SUM(oi.TotalPrice), 0) as CalculatedTotal
          FROM Orders o
          LEFT JOIN OrderItems oi ON o.OrderID = oi.OrderID
          WHERE 1=1
          GROUP BY o.OrderID, o.OrderNumber, o.CustomerID, o.CustomerEmail, o.CustomerName,
                   o.CustomerPhone, o.OrderStatus, o.PaymentStatus, o.PaymentMethod,
                   o.SubTotal, o.TaxAmount, o.ShippingAmount, o.DiscountAmount, o.TotalAmount,
                   o.ShippingAddress, o.BillingAddress, o.Notes, o.CreatedAt, o.UpdatedAt
          ORDER BY o.CreatedAt DESC
          OFFSET 0 ROWS FETCH NEXT 10 ROWS ONLY
        `;
        const testResult = await pool.request().query(testQuery);
        console.log('Test query successful, returned', testResult.recordset.length, 'orders');
      } catch (queryError) {
        console.error('Test query failed:', queryError.message);
      }
    }
    
    // List all tables
    const tables = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    console.log('Available tables:');
    tables.recordset.forEach(table => {
      console.log('  -', table.TABLE_NAME);
    });
    
  } catch (error) {
    console.error('Database test error:', error.message);
  } finally {
    process.exit(0);
  }
}

testDatabase();
