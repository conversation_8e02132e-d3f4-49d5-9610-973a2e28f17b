const express = require('express');
const { body, validationResult, query } = require('express-validator');
const router = express.Router();

// Import models and middleware
const Order = require('../models/Order');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const logger = require('../utils/logger');
const websocketService = require('../services/websocketService');

// Initialize Order model
const orderModel = new Order();

// @route   GET /api/orders
// @desc    Get all orders with pagination and filters
// @access  Private (Admin/Employee)
router.get('/', [
  authenticateToken,
  requirePermission('view_all_orders'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled']).withMessage('Invalid status'),
  query('paymentStatus').optional().isIn(['Pending', 'Paid', 'Failed', 'Refunded']).withMessage('Invalid payment status'),
  query('search').optional().isLength({ min: 1, max: 100 }).withMessage('Search term must be 1-100 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      paymentStatus,
      search,
      customerId,
      startDate,
      endDate
    } = req.query;

    // Build filters object
    const filters = {
      limit: parseInt(limit)
    };

    if (status) filters.status = status;
    if (paymentStatus) filters.paymentStatus = paymentStatus;
    if (search) filters.search = search;
    if (customerId) filters.customerId = customerId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    // Get orders with filters
    const orders = await orderModel.getOrdersWithFilters(filters);

    // Calculate pagination
    const totalItems = orders.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedOrders = orders.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    logger.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});



// @route   POST /api/orders
// @desc    Create new order
// @access  Private (Authenticated users)
router.post('/', [
  authenticateToken,
  body('customerEmail').isEmail().withMessage('Valid email is required'),
  body('customerName').isLength({ min: 2, max: 100 }).withMessage('Customer name must be 2-100 characters'),
  body('customerPhone').optional().isMobilePhone().withMessage('Invalid phone number'),
  body('shippingAddress').isLength({ min: 10, max: 500 }).withMessage('Shipping address must be 10-500 characters'),
  body('billingAddress').optional().isLength({ min: 10, max: 500 }).withMessage('Billing address must be 10-500 characters'),
  body('items').isArray({ min: 1 }).withMessage('At least one item is required'),
  body('items.*.variantId').custom((value) => {
    // Allow UUIDs or temporary mock IDs (for frontend transition period)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const mockIdRegex = /^[a-zA-Z0-9\-_]+$/;
    if (!uuidRegex.test(value) && !mockIdRegex.test(value)) {
      throw new Error('Valid variant ID is required');
    }
    return true;
  }),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('items.*.unitPrice').isFloat({ min: 0 }).withMessage('Unit price must be positive'),
  body('items.*.totalPrice').isFloat({ min: 0 }).withMessage('Total price must be positive'),
  body('items.*.customConfiguration').optional().isJSON().withMessage('Custom configuration must be valid JSON'),
  body('subTotal').isFloat({ min: 0 }).withMessage('Subtotal must be positive'),
  body('taxAmount').optional().isFloat({ min: 0 }).withMessage('Tax amount must be positive'),
  body('shippingAmount').optional().isFloat({ min: 0 }).withMessage('Shipping amount must be positive'),
  body('discountAmount').optional().isFloat({ min: 0 }).withMessage('Discount amount must be positive'),
  body('totalAmount').isFloat({ min: 0 }).withMessage('Total amount must be positive'),
  body('currency').optional().isLength({ min: 3, max: 3 }).withMessage('Currency must be 3 characters'),
  body('notes').optional().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      customerEmail,
      customerName,
      customerPhone,
      shippingAddress,
      billingAddress,
      items,
      subTotal,
      taxAmount = 0,
      shippingAmount = 0,
      discountAmount = 0,
      totalAmount,
      currency = 'PHP',
      notes
    } = req.body;

    // Prepare order data
    const orderData = {
      CustomerID: req.user.userId,
      CustomerEmail: customerEmail,
      CustomerName: customerName,
      CustomerPhone: customerPhone,
      ShippingAddress: shippingAddress,
      BillingAddress: billingAddress || shippingAddress,
      OrderStatus: 'Pending',
      PaymentStatus: 'Pending',
      SubTotal: subTotal,
      TaxAmount: taxAmount,
      ShippingAmount: shippingAmount,
      DiscountAmount: discountAmount,
      TotalAmount: totalAmount,
      Currency: currency,
      Notes: notes
    };

    // Prepare order items
    const orderItems = items.map(item => ({
      VariantID: item.variantId,
      Quantity: item.quantity,
      UnitPrice: item.unitPrice,
      TotalPrice: item.totalPrice,
      CustomConfiguration: item.customConfiguration ? JSON.stringify(item.customConfiguration) : null
    }));

    // Create order with inventory management
    const result = await orderModel.createOrderWithInventory(orderData, orderItems);

    // Emit real-time update
    websocketService.emitToAdmins('orderCreated', {
      orderId: result.order.OrderID,
      orderNumber: result.order.OrderNumber,
      customerName: customerName,
      totalAmount: totalAmount,
      timestamp: new Date().toISOString()
    });

    logger.info(`Order created: ${result.order.OrderNumber} by user ${req.user.userId}`);

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order: result.order,
        items: result.items
      }
    });

  } catch (error) {
    logger.error('Create order error:', error);

    // Handle specific inventory errors
    if (error.message.includes('Insufficient inventory')) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient inventory',
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/orders/:id/status
// @desc    Update order status
// @access  Private (Admin/Employee)
router.patch('/:id/status', [
  authenticateToken,
  requirePermission('manage_orders'),
  body('status').isIn(['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled']).withMessage('Invalid status'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes must be less than 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { status, notes } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID format'
      });
    }

    const updatedOrder = await orderModel.updateOrderStatus(id, status, notes);

    if (!updatedOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Emit real-time update
    websocketService.emitToAdmins('orderStatusUpdated', {
      orderId: id,
      newStatus: status,
      updatedBy: req.user.email,
      timestamp: new Date().toISOString()
    });

    logger.info(`Order ${id} status updated to ${status} by user ${req.user.userId}`);

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: updatedOrder
    });

  } catch (error) {
    logger.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/orders/:id
// @desc    Update order details
// @access  Private (Admin/Employee)
router.put('/:id', [
  authenticateToken,
  requirePermission('manage_orders'),
  body('customerEmail').optional().isEmail().withMessage('Valid email is required'),
  body('customerName').optional().isLength({ min: 2, max: 100 }).withMessage('Customer name must be 2-100 characters'),
  body('customerPhone').optional().isMobilePhone().withMessage('Invalid phone number'),
  body('shippingAddress').optional().isLength({ min: 10, max: 500 }).withMessage('Shipping address must be 10-500 characters'),
  body('billingAddress').optional().isLength({ min: 10, max: 500 }).withMessage('Billing address must be 10-500 characters'),
  body('notes').optional().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID format'
      });
    }

    // Add update timestamp
    updateData.UpdatedAt = new Date();

    const updatedOrder = await orderModel.updateById(id, updateData);

    if (!updatedOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    logger.info(`Order ${id} updated by user ${req.user.userId}`);

    res.json({
      success: true,
      message: 'Order updated successfully',
      data: updatedOrder
    });

  } catch (error) {
    logger.error('Update order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   DELETE /api/orders/:id
// @desc    Cancel/Delete order
// @access  Private (Admin/Employee)
router.delete('/:id', [
  authenticateToken,
  requirePermission('manage_orders'),
  body('reason').optional().isLength({ min: 5, max: 500 }).withMessage('Cancellation reason must be 5-500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { reason = 'Order cancelled by administrator' } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID format'
      });
    }

    const cancelledOrder = await orderModel.cancelOrder(id, reason, req.user.userId);

    if (!cancelledOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Emit real-time update
    websocketService.emitToAdmins('orderCancelled', {
      orderId: id,
      reason: reason,
      cancelledBy: req.user.email,
      timestamp: new Date().toISOString()
    });

    logger.info(`Order ${id} cancelled by user ${req.user.userId}. Reason: ${reason}`);

    res.json({
      success: true,
      message: 'Order cancelled successfully',
      data: cancelledOrder
    });

  } catch (error) {
    logger.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/orders/user/:userId
// @desc    Get orders for specific user
// @access  Private (Admin/Employee or order owner)
router.get('/user/:userId', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('status').optional().isIn(['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID format'
      });
    }

    // Check if user can access these orders
    if (req.user.role === 'Customer' && userId !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied: You can only view your own orders'
      });
    }

    // Build filters
    const filters = {
      customerId: userId,
      limit: parseInt(limit)
    };

    if (status) filters.status = status;

    const orders = await orderModel.getOrdersWithFilters(filters);

    // Calculate pagination
    const totalItems = orders.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedOrders = orders.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    logger.error('Get user orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/orders/analytics
// @desc    Get order analytics
// @access  Private (Admin/Employee)
router.get('/analytics', [
  authenticateToken,
  requirePermission('view_analytics'),
  query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be valid ISO date')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { startDate, endDate } = req.query;

    // Default to last 30 days if no dates provided
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const analyticsStartDate = startDate ? new Date(startDate) : defaultStartDate;
    const analyticsEndDate = endDate ? new Date(endDate) : defaultEndDate;

    const analytics = await orderModel.getOrderAnalytics(analyticsStartDate, analyticsEndDate);

    res.json({
      success: true,
      data: {
        analytics,
        dateRange: {
          startDate: analyticsStartDate.toISOString(),
          endDate: analyticsEndDate.toISOString()
        }
      }
    });

  } catch (error) {
    logger.error('Get order analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve order analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/orders/:id
// @desc    Get order by ID with full details
// @access  Private (Admin/Employee or order owner)
router.get('/:id', [
  authenticateToken,
  requirePermission('view_all_orders')
], async (req, res) => {
  try {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order ID format'
      });
    }

    const order = await orderModel.getOrderWithDetails(id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user can access this order (customers can only see their own orders)
    if (req.user.role === 'Customer' && order.CustomerID !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied: You can only view your own orders'
      });
    }

    res.json({
      success: true,
      data: order
    });

  } catch (error) {
    logger.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
