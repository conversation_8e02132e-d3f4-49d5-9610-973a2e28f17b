{"ast": null, "code": "import apiClient from './apiClient';\nexport const authService = {\n  async login(email, password) {\n    const response = await apiClient.post('/api/auth/login', {\n      email,\n      password\n    });\n    return response;\n  },\n  async register(userData) {\n    const response = await apiClient.post('/api/auth/register', userData);\n    return response;\n  },\n  async getProfile() {\n    // Mock profile data\n    return {\n      success: true,\n      data: {\n        id: 1,\n        name: 'Demo User',\n        email: '<EMAIL>',\n        phone: '(*************',\n        address: '123 Demo Street, Demo City, DC 12345'\n      }\n    };\n  },\n  async updateProfile(profileData) {\n    // Mock profile update (always succeeds)\n    return {\n      success: true,\n      data: {\n        ...profileData,\n        id: 1\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authService", "login", "email", "password", "response", "post", "register", "userData", "getProfile", "success", "data", "id", "name", "phone", "address", "updateProfile", "profileData"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/auth.js"], "sourcesContent": ["import apiClient from './apiClient';\n\nexport const authService = {\n    async login(email, password) {\n        const response = await apiClient.post('/api/auth/login', { email, password });\n        return response;\n    },\n\n    async register(userData) {\n        const response = await apiClient.post('/api/auth/register', userData);\n        return response;\n    },\n\n    async getProfile() {\n        // Mock profile data\n        return {\n            success: true,\n            data: {\n                id: 1,\n                name: 'Demo User',\n                email: '<EMAIL>',\n                phone: '(*************',\n                address: '123 Demo Street, Demo City, DC 12345'\n            }\n        };\n    },\n\n    async updateProfile(profileData) {\n        // Mock profile update (always succeeds)\n        return {\n            success: true,\n            data: {\n                ...profileData,\n                id: 1\n            }\n        };\n    }\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AAEnC,OAAO,MAAMC,WAAW,GAAG;EACvB,MAAMC,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACzB,MAAMC,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,iBAAiB,EAAE;MAAEH,KAAK;MAAEC;IAAS,CAAC,CAAC;IAC7E,OAAOC,QAAQ;EACnB,CAAC;EAED,MAAME,QAAQA,CAACC,QAAQ,EAAE;IACrB,MAAMH,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;IACrE,OAAOH,QAAQ;EACnB,CAAC;EAED,MAAMI,UAAUA,CAAA,EAAG;IACf;IACA,OAAO;MACHC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACFC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,WAAW;QACjBV,KAAK,EAAE,kBAAkB;QACzBW,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACb;IACJ,CAAC;EACL,CAAC;EAED,MAAMC,aAAaA,CAACC,WAAW,EAAE;IAC7B;IACA,OAAO;MACHP,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACF,GAAGM,WAAW;QACdL,EAAE,EAAE;MACR;IACJ,CAAC;EACL;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}