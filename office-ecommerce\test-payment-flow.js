/**
 * PayMongo Payment Flow Test Script
 * Tests the complete payment integration from frontend to backend
 */

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:5000';

// Test data
const testOrder = {
  orderId: `TEST-${Date.now()}`,
  totalAmount: 2499.99,
  items: [
    {
      name: 'Executive Office Chair',
      quantity: 1,
      price: 1999.99
    },
    {
      name: 'Office Desk Lamp',
      quantity: 1,
      price: 500.00
    }
  ],
  customer: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+639123456789'
  },
  shippingAddress: {
    address: '123 Main Street, Barangay Sample',
    city: 'Manila',
    province: 'Metro Manila',
    postalCode: '1000',
    country: 'Philippines'
  }
};

async function testBackendHealth() {
  console.log('\n🔍 Testing Backend Health...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`);
    console.log('✅ Backend Health:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Backend Health Error:', error.message);
    return null;
  }
}

async function testPayMongoConfig() {
  console.log('\n🔍 Testing PayMongo Configuration...');
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`);
    const paymongoStatus = response.data.paymongo;
    
    console.log('✅ PayMongo Configuration:');
    console.log(`   - Configured: ${paymongoStatus.configured}`);
    console.log(`   - Webhook Configured: ${paymongoStatus.webhook_configured}`);
    
    return paymongoStatus;
  } catch (error) {
    console.error('❌ PayMongo Config Error:', error.message);
    return null;
  }
}

async function testCreatePaymentLink() {
  console.log('\n🔍 Testing Payment Link Creation...');
  console.log('Note: This requires authentication, testing endpoint availability...');
  
  try {
    const response = await axios.post(`${BACKEND_URL}/api/payments/create-link`, testOrder, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Payment Link Created:', response.data);
    return response.data;
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️  Payment Link Endpoint: Requires authentication (expected)');
      console.log('   - Endpoint is accessible');
      console.log('   - Authentication middleware is working');
      return { requiresAuth: true };
    } else {
      console.error('❌ Payment Link Error:', error.response?.data || error.message);
      return null;
    }
  }
}

async function testWebhookEndpoint() {
  console.log('\n🔍 Testing Webhook Endpoint...');
  
  const webhookPayload = {
    data: {
      id: 'evt_test_webhook',
      type: 'link.payment.paid',
      attributes: {
        type: 'link.payment.paid',
        livemode: false,
        data: {
          id: 'link_test123456',
          type: 'link',
          attributes: {
            amount: Math.round(testOrder.totalAmount * 100), // Convert to centavos
            currency: 'PHP',
            description: 'Test Payment',
            status: 'paid',
            metadata: {
              orderId: testOrder.orderId
            }
          }
        }
      }
    }
  };
  
  try {
    const response = await axios.post(`${BACKEND_URL}/api/payments/webhook`, webhookPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Webhook Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Webhook Error:', error.response?.data || error.message);
    return null;
  }
}

async function testFrontendAccess() {
  console.log('\n🔍 Testing Frontend Access...');
  
  try {
    const response = await axios.get(FRONTEND_URL, {
      timeout: 5000
    });
    
    console.log('✅ Frontend is accessible');
    console.log(`   - Status: ${response.status}`);
    console.log(`   - Content-Type: ${response.headers['content-type']}`);
    
    return true;
  } catch (error) {
    console.error('❌ Frontend Access Error:', error.message);
    return false;
  }
}

async function testCheckoutRoute() {
  console.log('\n🔍 Testing Checkout Route...');
  console.log('Note: React Router handles client-side routing, testing main app...');

  try {
    const response = await axios.get(FRONTEND_URL, {
      timeout: 5000
    });

    if (response.data.includes('root') || response.data.includes('React')) {
      console.log('✅ React app is loaded (checkout route available via client-side routing)');
      console.log(`   - Status: ${response.status}`);
      return true;
    } else {
      console.log('⚠️  Frontend loaded but React app not detected');
      return false;
    }
  } catch (error) {
    console.error('❌ Checkout Route Error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting PayMongo Payment Flow Tests...');
  console.log('='.repeat(50));
  
  const results = {
    backendHealth: await testBackendHealth(),
    paymongoConfig: await testPayMongoConfig(),
    paymentLink: await testCreatePaymentLink(),
    webhook: await testWebhookEndpoint(),
    frontendAccess: await testFrontendAccess(),
    checkoutRoute: await testCheckoutRoute()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('='.repeat(50));
  
  console.log(`Backend Health: ${results.backendHealth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`PayMongo Config: ${results.paymongoConfig?.configured ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Payment Endpoint: ${results.paymentLink ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Webhook Endpoint: ${results.webhook ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend Access: ${results.frontendAccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Checkout Route: ${results.checkoutRoute ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
  
  if (passCount === totalTests) {
    console.log('🎉 All payment flow components are working correctly!');
    console.log('\n📝 Next Steps:');
    console.log('1. Add items to cart at http://localhost:3000');
    console.log('2. Go to cart and click "Proceed to Checkout"');
    console.log('3. Fill out shipping form and click "Review Order"');
    console.log('4. Click "Pay with PayMongo" to test payment flow');
  } else {
    console.log('⚠️  Some components need attention before testing payment flow');
  }
  
  return results;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testOrder };
