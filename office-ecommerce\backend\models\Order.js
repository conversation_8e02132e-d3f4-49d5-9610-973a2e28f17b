const BaseModel = require('./BaseModel');
const Inventory = require('./Inventory');
const logger = require('../utils/logger');

class Order extends BaseModel {
  constructor() {
    super('Orders', 'OrderID');
    this.inventory = new Inventory();
  }

  // Create order with inventory management
  async createOrderWithInventory(orderData, orderItems) {
    const transaction = await this.beginTransaction();
    
    try {
      const request = transaction.request();
      
      // Generate order number
      const orderNumber = await this.generateOrderNumber();
      orderData.OrderNumber = orderNumber;

      // Create order
      const orderColumns = Object.keys(orderData);
      const orderValues = Object.values(orderData);
      const orderPlaceholders = orderColumns.map((_, index) => `@orderParam${index}`);

      orderValues.forEach((value, index) => {
        request.input(`orderParam${index}`, value);
      });

      const orderQuery = `
        INSERT INTO Orders (${orderColumns.join(', ')})
        OUTPUT INSERTED.*
        VALUES (${orderPlaceholders.join(', ')})
      `;

      const orderResult = await request.query(orderQuery);
      const order = orderResult.recordset[0];

      // Check inventory availability for all items first
      for (const item of orderItems) {
        const availability = await this.inventory.checkAvailability(item.VariantID, item.Quantity);
        if (!availability.available) {
          throw new Error(`Insufficient inventory for variant ${item.VariantID}: ${availability.reason}`);
        }
      }

      // Create order items
      const createdItems = [];
      for (let i = 0; i < orderItems.length; i++) {
        const itemData = { ...orderItems[i], OrderID: order.OrderID };
        const itemRequest = transaction.request();
        
        const itemColumns = Object.keys(itemData);
        const itemValues = Object.values(itemData);
        const itemPlaceholders = itemColumns.map((_, index) => `@itemParam${i}_${index}`);

        itemValues.forEach((value, index) => {
          itemRequest.input(`itemParam${i}_${index}`, value);
        });

        const itemQuery = `
          INSERT INTO OrderItems (${itemColumns.join(', ')})
          OUTPUT INSERTED.*
          VALUES (${itemPlaceholders.join(', ')})
        `;

        const itemResult = await itemRequest.query(itemQuery);
        createdItems.push(itemResult.recordset[0]);
      }

      // Reserve inventory
      await this.inventory.reserveInventory(order.OrderID);

      await transaction.commit();
      
      return {
        order,
        items: createdItems
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating order with inventory:', error);
      throw error;
    }
  }

  // Generate unique order number
  async generateOrderNumber() {
    try {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      
      const prefix = `ORD${year}${month}${day}`;
      
      // Get the count of orders created today
      const query = `
        SELECT COUNT(*) as count 
        FROM Orders 
        WHERE OrderNumber LIKE @prefix
      `;
      
      const result = await this.executeQuery(query, { prefix: `${prefix}%` });
      const count = result[0].count + 1;
      
      return `${prefix}${String(count).padStart(4, '0')}`;
    } catch (error) {
      logger.error('Error generating order number:', error);
      throw error;
    }
  }

  // Get order with items and product details
  async getOrderWithDetails(orderId) {
    try {
      const query = `
        SELECT 
          o.*,
          oi.OrderItemID,
          oi.VariantID,
          oi.Quantity,
          oi.UnitPrice,
          oi.TotalPrice,
          oi.CustomConfiguration,
          pv.VariantCode,
          pv.VariantName,
          pv.SKU,
          p.ProductID,
          p.ProductCode,
          p.ProductName,
          p.ImageURL,
          c.CategoryName
        FROM Orders o
        LEFT JOIN OrderItems oi ON o.OrderID = oi.OrderID
        LEFT JOIN ProductVariants pv ON oi.VariantID = pv.VariantID
        LEFT JOIN Products p ON pv.ProductID = p.ProductID
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        WHERE o.OrderID = @orderId
        ORDER BY oi.OrderItemID
      `;
      
      const results = await this.executeQuery(query, { orderId });
      
      if (results.length === 0) {
        return null;
      }

      // Group items under the order
      const order = {
        OrderID: results[0].OrderID,
        OrderNumber: results[0].OrderNumber,
        CustomerID: results[0].CustomerID,
        CustomerEmail: results[0].CustomerEmail,
        CustomerName: results[0].CustomerName,
        CustomerPhone: results[0].CustomerPhone,
        ShippingAddress: results[0].ShippingAddress,
        BillingAddress: results[0].BillingAddress,
        OrderStatus: results[0].OrderStatus,
        PaymentStatus: results[0].PaymentStatus,
        PaymentMethod: results[0].PaymentMethod,
        PaymentReference: results[0].PaymentReference,
        SubTotal: results[0].SubTotal,
        TaxAmount: results[0].TaxAmount,
        ShippingAmount: results[0].ShippingAmount,
        DiscountAmount: results[0].DiscountAmount,
        TotalAmount: results[0].TotalAmount,
        Currency: results[0].Currency,
        Notes: results[0].Notes,
        CreatedAt: results[0].CreatedAt,
        UpdatedAt: results[0].UpdatedAt,
        items: []
      };

      // Add items if they exist
      results.forEach(row => {
        if (row.OrderItemID) {
          order.items.push({
            OrderItemID: row.OrderItemID,
            VariantID: row.VariantID,
            Quantity: row.Quantity,
            UnitPrice: row.UnitPrice,
            TotalPrice: row.TotalPrice,
            CustomConfiguration: row.CustomConfiguration,
            product: {
              ProductID: row.ProductID,
              ProductCode: row.ProductCode,
              ProductName: row.ProductName,
              ImageURL: row.ImageURL,
              CategoryName: row.CategoryName,
              variant: {
                VariantCode: row.VariantCode,
                VariantName: row.VariantName,
                SKU: row.SKU
              }
            }
          });
        }
      });

      return order;
    } catch (error) {
      logger.error('Error getting order with details:', error);
      throw error;
    }
  }

  // Update order status
  async updateOrderStatus(orderId, newStatus, notes = null) {
    try {
      const updateData = { 
        OrderStatus: newStatus,
        UpdatedAt: new Date()
      };
      
      if (notes) {
        updateData.Notes = notes;
      }

      // Handle inventory based on status change
      const order = await this.findById(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      const oldStatus = order.OrderStatus;

      // Update order
      const updatedOrder = await this.updateById(orderId, updateData);

      // Handle inventory changes based on status
      if (oldStatus !== newStatus) {
        await this.handleInventoryOnStatusChange(orderId, oldStatus, newStatus);
      }

      return updatedOrder;
    } catch (error) {
      logger.error('Error updating order status:', error);
      throw error;
    }
  }

  // Handle inventory changes when order status changes
  async handleInventoryOnStatusChange(orderId, oldStatus, newStatus) {
    try {
      // When order is confirmed, keep reservation
      if (newStatus === 'Confirmed' && oldStatus === 'Pending') {
        // Inventory is already reserved, no action needed
        logger.info(`Order ${orderId} confirmed, inventory remains reserved`);
      }
      
      // When order is shipped, deduct from inventory
      else if (newStatus === 'Shipped' && ['Confirmed', 'Processing'].includes(oldStatus)) {
        // Get order items
        const orderItems = await this.executeQuery(
          'SELECT * FROM OrderItems WHERE OrderID = @orderId',
          { orderId }
        );

        // Deduct inventory for each item
        for (const item of orderItems) {
          await this.inventory.updateInventoryLevels({
            variantId: item.VariantID,
            quantityChange: -item.Quantity, // Negative for deduction
            transactionType: 'Sale',
            referenceType: 'Order',
            referenceId: orderId
          });
        }

        logger.info(`Order ${orderId} shipped, inventory deducted`);
      }
      
      // When order is cancelled, release reserved inventory
      else if (newStatus === 'Cancelled' && oldStatus !== 'Cancelled') {
        await this.inventory.releaseReservedInventory(orderId);
        logger.info(`Order ${orderId} cancelled, reserved inventory released`);
      }
      
    } catch (error) {
      logger.error('Error handling inventory on status change:', error);
      throw error;
    }
  }

  // Get orders with filters and enhanced pagination
  async getOrdersWithFilters(filters = {}) {
    try {
      let query = `
        SELECT
          o.OrderID, o.OrderNumber, o.CustomerID, o.CustomerEmail, o.CustomerName, o.CustomerPhone,
          o.ShippingAddress, o.BillingAddress, o.OrderStatus, o.PaymentStatus, o.PaymentMethod,
          o.PaymentReference, o.SubTotal, o.TaxAmount, o.ShippingAmount, o.DiscountAmount,
          o.TotalAmount, o.Currency, o.Notes, o.CreatedAt, o.UpdatedAt,
          COUNT(oi.OrderItemID) as ItemCount,
          CASE
            WHEN u.UserID IS NOT NULL THEN u.FirstName + ' ' + u.LastName
            ELSE o.CustomerName
          END as CustomerFullName,
          SUM(oi.TotalPrice) as CalculatedTotal
        FROM Orders o
        LEFT JOIN OrderItems oi ON o.OrderID = oi.OrderID
        LEFT JOIN Users u ON o.CustomerID = u.UserID
        WHERE 1=1
      `;

      const params = {};

      // Add filters
      if (filters.status) {
        query += ` AND o.OrderStatus = @status`;
        params.status = filters.status;
      }

      if (filters.paymentStatus) {
        query += ` AND o.PaymentStatus = @paymentStatus`;
        params.paymentStatus = filters.paymentStatus;
      }

      if (filters.customerId) {
        query += ` AND o.CustomerID = @customerId`;
        params.customerId = filters.customerId;
      }

      if (filters.startDate) {
        query += ` AND o.CreatedAt >= @startDate`;
        params.startDate = filters.startDate;
      }

      if (filters.endDate) {
        query += ` AND o.CreatedAt <= @endDate`;
        params.endDate = filters.endDate;
      }

      if (filters.search) {
        query += ` AND (o.OrderNumber LIKE @search OR o.CustomerEmail LIKE @search OR o.CustomerName LIKE @search)`;
        params.search = `%${filters.search}%`;
      }

      if (filters.minAmount) {
        query += ` AND o.TotalAmount >= @minAmount`;
        params.minAmount = filters.minAmount;
      }

      if (filters.maxAmount) {
        query += ` AND o.TotalAmount <= @maxAmount`;
        params.maxAmount = filters.maxAmount;
      }

      query += ` GROUP BY o.OrderID, o.OrderNumber, o.CustomerID, o.CustomerEmail, o.CustomerName, o.CustomerPhone, o.ShippingAddress, o.BillingAddress, o.OrderStatus, o.PaymentStatus, o.PaymentMethod, o.PaymentReference, o.SubTotal, o.TaxAmount, o.ShippingAmount, o.DiscountAmount, o.TotalAmount, o.Currency, o.Notes, o.CreatedAt, o.UpdatedAt, u.FirstName, u.LastName, u.UserID`;

      query += ` ORDER BY o.CreatedAt DESC`;

      if (filters.limit) {
        query = query.replace('SELECT', `SELECT TOP ${filters.limit * 2}`); // Get more for better filtering
      }

      return await this.executeQuery(query, params);
    } catch (error) {
      logger.error('Error getting orders with filters:', error);
      throw error;
    }
  }

  // Update payment status
  async updatePaymentStatus(orderId, paymentStatus, paymentReference = null, paymentMethod = null) {
    try {
      const updateData = {
        PaymentStatus: paymentStatus,
        UpdatedAt: new Date()
      };

      if (paymentReference) {
        updateData.PaymentReference = paymentReference;
      }

      if (paymentMethod) {
        updateData.PaymentMethod = paymentMethod;
      }

      const updatedOrder = await this.updateById(orderId, updateData);

      // If payment is successful and order is pending, move to confirmed
      if (paymentStatus === 'Paid') {
        const order = await this.findById(orderId);
        if (order && order.OrderStatus === 'Pending') {
          await this.updateOrderStatus(orderId, 'Confirmed', 'Payment confirmed, order ready for processing');
        }
      }

      logger.info(`Order ${orderId} payment status updated to ${paymentStatus}`);
      return updatedOrder;
    } catch (error) {
      logger.error('Error updating payment status:', error);
      throw error;
    }
  }

  // Get order summary statistics
  async getOrderSummary(customerId = null) {
    try {
      let query = `
        SELECT
          COUNT(*) as TotalOrders,
          SUM(CASE WHEN OrderStatus = 'Pending' THEN 1 ELSE 0 END) as PendingOrders,
          SUM(CASE WHEN OrderStatus = 'Processing' THEN 1 ELSE 0 END) as ProcessingOrders,
          SUM(CASE WHEN OrderStatus = 'Shipped' THEN 1 ELSE 0 END) as ShippedOrders,
          SUM(CASE WHEN OrderStatus = 'Delivered' THEN 1 ELSE 0 END) as DeliveredOrders,
          SUM(CASE WHEN OrderStatus = 'Cancelled' THEN 1 ELSE 0 END) as CancelledOrders,
          SUM(TotalAmount) as TotalValue,
          AVG(TotalAmount) as AverageOrderValue
        FROM Orders
        WHERE 1=1
      `;

      const params = {};

      if (customerId) {
        query += ` AND CustomerID = @customerId`;
        params.customerId = customerId;
      }

      const result = await this.executeQuery(query, params);
      return result[0];
    } catch (error) {
      logger.error('Error getting order summary:', error);
      throw error;
    }
  }

  // Get order analytics
  async getOrderAnalytics(startDate, endDate) {
    try {
      const query = `
        SELECT 
          COUNT(*) as TotalOrders,
          SUM(CASE WHEN OrderStatus = 'Pending' THEN 1 ELSE 0 END) as PendingOrders,
          SUM(CASE WHEN OrderStatus = 'Confirmed' THEN 1 ELSE 0 END) as ConfirmedOrders,
          SUM(CASE WHEN OrderStatus = 'Processing' THEN 1 ELSE 0 END) as ProcessingOrders,
          SUM(CASE WHEN OrderStatus = 'Shipped' THEN 1 ELSE 0 END) as ShippedOrders,
          SUM(CASE WHEN OrderStatus = 'Delivered' THEN 1 ELSE 0 END) as DeliveredOrders,
          SUM(CASE WHEN OrderStatus = 'Cancelled' THEN 1 ELSE 0 END) as CancelledOrders,
          SUM(CASE WHEN PaymentStatus = 'Paid' THEN TotalAmount ELSE 0 END) as TotalRevenue,
          AVG(CASE WHEN PaymentStatus = 'Paid' THEN TotalAmount ELSE NULL END) as AverageOrderValue,
          SUM(CASE WHEN PaymentStatus = 'Paid' THEN 1 ELSE 0 END) as PaidOrders,
          SUM(CASE WHEN PaymentStatus = 'Pending' THEN 1 ELSE 0 END) as PendingPayments,
          SUM(CASE WHEN PaymentStatus = 'Failed' THEN 1 ELSE 0 END) as FailedPayments
        FROM Orders
        WHERE CreatedAt BETWEEN @startDate AND @endDate
      `;
      
      const result = await this.executeQuery(query, { startDate, endDate });
      return result[0];
    } catch (error) {
      logger.error('Error getting order analytics:', error);
      throw error;
    }
  }

  // Cancel order
  async cancelOrder(orderId, reason, cancelledBy) {
    try {
      // Update order status
      const updatedOrder = await this.updateOrderStatus(orderId, 'Cancelled', reason);

      // Log the cancellation
      logger.info(`Order ${orderId} cancelled by user ${cancelledBy}. Reason: ${reason}`);

      return updatedOrder;
    } catch (error) {
      logger.error('Error cancelling order:', error);
      throw error;
    }
  }

  // Get recent orders
  async getRecentOrders(limit = 10) {
    try {
      const query = `
        SELECT TOP ${limit}
          o.OrderID, o.OrderNumber, o.CustomerID, o.CustomerEmail, o.CustomerName, o.CustomerPhone,
          o.ShippingAddress, o.BillingAddress, o.OrderStatus, o.PaymentStatus, o.PaymentMethod,
          o.PaymentReference, o.SubTotal, o.TaxAmount, o.ShippingAmount, o.DiscountAmount,
          o.TotalAmount, o.Currency, o.Notes, o.CreatedAt, o.UpdatedAt,
          COUNT(oi.OrderItemID) as ItemCount,
          CASE
            WHEN u.UserID IS NOT NULL THEN u.FirstName + ' ' + u.LastName
            ELSE o.CustomerName
          END as CustomerFullName
        FROM Orders o
        LEFT JOIN OrderItems oi ON o.OrderID = oi.OrderID
        LEFT JOIN Users u ON o.CustomerID = u.UserID
        GROUP BY o.OrderID, o.OrderNumber, o.CustomerID, o.CustomerEmail, o.CustomerName, o.CustomerPhone, o.ShippingAddress, o.BillingAddress, o.OrderStatus, o.PaymentStatus, o.PaymentMethod, o.PaymentReference, o.SubTotal, o.TaxAmount, o.ShippingAmount, o.DiscountAmount, o.TotalAmount, o.Currency, o.Notes, o.CreatedAt, o.UpdatedAt, u.FirstName, u.LastName, u.UserID
        ORDER BY o.CreatedAt DESC
      `;

      return await this.executeQuery(query);
    } catch (error) {
      logger.error('Error getting recent orders:', error);
      throw error;
    }
  }

  // Get orders by status
  async getOrdersByStatus(status) {
    try {
      return await this.getOrdersWithFilters({ status });
    } catch (error) {
      logger.error('Error getting orders by status:', error);
      throw error;
    }
  }

  // Validate order data
  validateOrderData(orderData, orderItems) {
    const errors = [];

    // Validate order data
    if (!orderData.CustomerEmail || !orderData.CustomerEmail.includes('@')) {
      errors.push('Valid customer email is required');
    }

    if (!orderData.CustomerName || orderData.CustomerName.length < 2) {
      errors.push('Customer name must be at least 2 characters');
    }

    if (!orderData.ShippingAddress || orderData.ShippingAddress.length < 10) {
      errors.push('Shipping address must be at least 10 characters');
    }

    if (!orderData.TotalAmount || orderData.TotalAmount <= 0) {
      errors.push('Total amount must be greater than 0');
    }

    // Validate order items
    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      errors.push('At least one order item is required');
    } else {
      orderItems.forEach((item, index) => {
        if (!item.VariantID) {
          errors.push(`Item ${index + 1}: Variant ID is required`);
        }
        if (!item.Quantity || item.Quantity <= 0) {
          errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.UnitPrice || item.UnitPrice <= 0) {
          errors.push(`Item ${index + 1}: Unit price must be greater than 0`);
        }
        if (!item.TotalPrice || item.TotalPrice <= 0) {
          errors.push(`Item ${index + 1}: Total price must be greater than 0`);
        }
      });
    }

    return errors;
  }

  // Calculate order totals
  calculateOrderTotals(items, taxRate = 0.12, shippingAmount = 0, discountAmount = 0) {
    const subTotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const taxAmount = subTotal * taxRate;
    const totalAmount = subTotal + taxAmount + shippingAmount - discountAmount;

    return {
      subTotal: Math.round(subTotal * 100) / 100,
      taxAmount: Math.round(taxAmount * 100) / 100,
      shippingAmount: Math.round(shippingAmount * 100) / 100,
      discountAmount: Math.round(discountAmount * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100
    };
  }
}

module.exports = Order;
