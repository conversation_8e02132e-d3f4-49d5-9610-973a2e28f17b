import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuth';
import { CartProvider } from './contexts/CartContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Header from './components/common/Header';
import Footer from './components/common/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import ProductCatalog from './pages/ProductCatalog';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import CheckoutPage from './pages/CheckoutPage';
import OrderSuccessPage from './pages/OrderSuccessPage';
import Payment from './pages/Payment';
import About from './pages/About';
import Gallery from './pages/Gallery';
import ProductCardDemo from './components/demo/ProductCardDemo';
import AdminDashboard from './pages/admin/AdminDashboard';

// Import route protection components
import ProtectedRoute from './components/auth/ProtectedRoute';
import RoleBasedRoute from './components/auth/RoleBasedRoute';
import AdminRoute from './components/auth/AdminRoute';

import './styles/globals.css';

// Layout component to conditionally render Header and Footer
function Layout({ children }) {
    const location = useLocation();
    const isAdminRoute = location.pathname.startsWith('/admin');

    return (
        <div className="App">
            {!isAdminRoute && <Header />}
            <main>
                {children}
            </main>
            {!isAdminRoute && <Footer />}
        </div>
    );
}

function App() {
    return (
        <AuthProvider>
            <CurrencyProvider>
                <LanguageProvider>
                    <CartProvider>
                        <Router>
                            <Layout>
                                <Routes>
                                    {/* Public Routes - Accessible to all users */}
                                    <Route path="/" element={<Home />} />
                                    <Route path="/login" element={<Login />} />
                                    <Route path="/products" element={<ProductCatalog />} />
                                    <Route path="/product/:id" element={<ProductDetail />} />
                                    <Route path="/products/:id" element={<ProductDetail />} />
                                    <Route path="/about" element={<About />} />
                                    <Route path="/gallery" element={<Gallery />} />
                                    <Route path="/demo" element={<ProductCardDemo />} />

                                    {/* Protected Routes - Require authentication */}
                                    <Route
                                        path="/cart"
                                        element={
                                            <ProtectedRoute>
                                                <Cart />
                                            </ProtectedRoute>
                                        }
                                    />
                                    <Route
                                        path="/checkout"
                                        element={
                                            <div style={{ padding: '20px', textAlign: 'center' }}>
                                                <h1>🛒 Checkout Page Test</h1>
                                                <p>If you can see this, routing is working!</p>
                                                <button onClick={() => window.location.href = '/cart'}>
                                                    Back to Cart
                                                </button>
                                            </div>
                                        }
                                    />
                                    <Route
                                        path="/order-success"
                                        element={
                                            <ProtectedRoute>
                                                <OrderSuccessPage />
                                            </ProtectedRoute>
                                        }
                                    />
                                    <Route
                                        path="/payment"
                                        element={
                                            <ProtectedRoute>
                                                <Payment />
                                            </ProtectedRoute>
                                        }
                                    />

                                    {/* Admin Routes - Require Employee or Admin role */}
                                    <Route
                                        path="/admin"
                                        element={
                                            <AdminRoute allowEmployee={true}>
                                                <AdminDashboard />
                                            </AdminRoute>
                                        }
                                    />
                                </Routes>
                            </Layout>
                        </Router>
                    </CartProvider>
                </LanguageProvider>
            </CurrencyProvider>
        </AuthProvider>
    );
}

export default App;