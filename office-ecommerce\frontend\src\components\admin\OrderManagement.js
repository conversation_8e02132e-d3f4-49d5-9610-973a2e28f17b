import React, { useState, useEffect, useCallback } from 'react';
import apiClient from '../../services/apiClient';
import { useWebSocket } from '../../hooks/useWebSocket';
import {
  PackageIcon,
  SearchIcon,
  FilterIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  ExportIcon,
  RefreshIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from './icons/AdminIcons';

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);

  // WebSocket integration for real-time updates
  const { isConnected } = useWebSocket();

  // Filters and pagination
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    paymentStatus: '',
    startDate: '',
    endDate: ''
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });



  // Fetch orders from API
  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      });

      const response = await apiClient.get(`/api/orders?${queryParams}`);

      if (response.success) {
        setOrders(response.data.orders);
        setPagination(prev => ({
          ...prev,
          ...response.data.pagination
        }));
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching orders:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.currentPage, pagination.itemsPerPage]);

  // Fetch order details
  const fetchOrderDetails = async (orderId) => {
    try {
      const response = await apiClient.get(`/api/orders/${orderId}`);
      if (response.success) {
        setSelectedOrder(response.data);
        setShowOrderModal(true);
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching order details:', err);
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus, notes = '') => {
    try {
      const response = await apiClient.patch(`/api/orders/${orderId}/status`, {
        status: newStatus,
        notes
      });

      if (response.success) {
        await fetchOrders(); // Refresh orders list
        setShowStatusModal(false);
        setSelectedOrder(null);
      }
    } catch (err) {
      setError(err.message);
      console.error('Error updating order status:', err);
    }
  };

  // Delete/Cancel order
  const cancelOrder = async (orderId, reason) => {
    if (!window.confirm('Are you sure you want to cancel this order?')) return;

    try {
      const response = await apiClient.delete(`/api/orders/${orderId}`, {
        data: { reason }
      });

      if (response.success) {
        await fetchOrders(); // Refresh orders list
      }
    } catch (err) {
      setError(err.message);
      console.error('Error cancelling order:', err);
    }
  };

  // Load orders on component mount and filter changes
  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  // WebSocket event listeners for real-time updates
  useEffect(() => {
    const handleNewOrder = () => {
      console.log('New order received via WebSocket, refreshing orders list');
      fetchOrders(); // Refresh the orders list when a new order is received
    };

    const handleOrderUpdate = () => {
      console.log('Order updated via WebSocket, refreshing orders list');
      fetchOrders(); // Refresh the orders list when an order is updated
    };

    // Subscribe to WebSocket events
    if (isConnected) {
      const websocketService = require('../../services/websocketService').default;
      websocketService.on('newOrder', handleNewOrder);
      websocketService.on('orderUpdated', handleOrderUpdate);
      websocketService.subscribeToOrders(); // Subscribe to order updates

      return () => {
        websocketService.off('newOrder', handleNewOrder);
        websocketService.off('orderUpdated', handleOrderUpdate);
      };
    }
  }, [isConnected, fetchOrders]);

  // Utility functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending': return '#F59E0B';
      case 'confirmed': return '#3B82F6';
      case 'processing': return '#F0B21B';
      case 'shipped': return '#8B5CF6';
      case 'delivered': return '#10B981';
      case 'cancelled': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid': return '#10B981';
      case 'pending': return '#F59E0B';
      case 'failed': return '#EF4444';
      case 'refunded': return '#6B7280';
      default: return '#6B7280';
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  // Error display
  if (error) {
    return (
      <div className="admin-card">
        <div className="admin-card-header">
          <h2>Order Management</h2>
        </div>
        <div style={{ textAlign: 'center', padding: '2rem', color: '#EF4444' }}>
          <p>Error loading orders: {error}</p>
          <button
            className="btn btn-primary"
            onClick={() => fetchOrders()}
            style={{ marginTop: '1rem' }}
          >
            <RefreshIcon size={16} />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-card">
      <div className="admin-card-header">
        <h2>
          <PackageIcon size={24} />
          Order Management
        </h2>
        <div className="admin-card-actions">
          <button
            className="btn btn-outline"
            onClick={() => fetchOrders()}
            disabled={loading}
          >
            <RefreshIcon size={16} />
            Refresh
          </button>
          <button className="btn btn-primary">
            <ExportIcon size={16} />
            Export Orders
          </button>
        </div>
      </div>

      {/* Filters Section */}
      <div className="admin-card-content">
        <div className="filters-section" style={{ marginBottom: '1.5rem' }}>
          <div className="filters-grid">
            <div className="filter-group">
              <label>Search Orders</label>
              <div className="search-input-container">
                <SearchIcon size={16} />
                <input
                  type="text"
                  placeholder="Search by order number, customer..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

            <div className="filter-group">
              <label>Order Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="filter-select"
              >
                <option value="">All Statuses</option>
                <option value="Pending">Pending</option>
                <option value="Confirmed">Confirmed</option>
                <option value="Processing">Processing</option>
                <option value="Shipped">Shipped</option>
                <option value="Delivered">Delivered</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Payment Status</label>
              <select
                value={filters.paymentStatus}
                onChange={(e) => handleFilterChange('paymentStatus', e.target.value)}
                className="filter-select"
              >
                <option value="">All Payment Status</option>
                <option value="Pending">Pending</option>
                <option value="Paid">Paid</option>
                <option value="Failed">Failed</option>
                <option value="Refunded">Refunded</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Date Range</label>
              <div className="date-range-inputs">
                <input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange('startDate', e.target.value)}
                  className="date-input"
                />
                <span>to</span>
                <input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange('endDate', e.target.value)}
                  className="date-input"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="table-container">
          {loading ? (
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div className="loading-spinner"></div>
              <p>Loading orders...</p>
            </div>
          ) : orders.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#6B7280' }}>
              <PackageIcon size={48} />
              <p>No orders found</p>
            </div>
          ) : (
            <>
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Order Number</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Order Status</th>
                    <th>Payment Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map(order => (
                    <tr key={order.OrderID}>
                      <td>
                        <strong>{order.OrderNumber}</strong>
                      </td>
                      <td>
                        <div>
                          <div>{order.CustomerFullName || order.CustomerName}</div>
                          <small style={{ color: '#6B7280' }}>{order.CustomerEmail}</small>
                        </div>
                      </td>
                      <td>
                        <span className="item-count-badge">
                          {order.ItemCount} item{order.ItemCount !== 1 ? 's' : ''}
                        </span>
                      </td>
                      <td>
                        <strong>{formatCurrency(order.TotalAmount)}</strong>
                        <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>
                          {order.Currency}
                        </div>
                      </td>
                      <td>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(order.OrderStatus) }}
                        >
                          {order.OrderStatus}
                        </span>
                      </td>
                      <td>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getPaymentStatusColor(order.PaymentStatus) }}
                        >
                          {order.PaymentStatus}
                        </span>
                      </td>
                      <td>{formatDate(order.CreatedAt)}</td>
                      <td>
                        <div className="action-buttons">
                          <button
                            className="btn btn-sm btn-outline"
                            onClick={() => fetchOrderDetails(order.OrderID)}
                            title="View Details"
                          >
                            <EyeIcon size={14} />
                          </button>
                          <button
                            className="btn btn-sm btn-outline"
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowStatusModal(true);
                            }}
                            title="Update Status"
                          >
                            <EditIcon size={14} />
                          </button>
                          <button
                            className="btn btn-sm btn-outline btn-danger"
                            onClick={() => cancelOrder(order.OrderID, 'Cancelled by administrator')}
                            title="Cancel Order"
                            disabled={order.OrderStatus === 'Cancelled' || order.OrderStatus === 'Delivered'}
                          >
                            <TrashIcon size={14} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </>
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => {
            setShowOrderModal(false);
            setSelectedOrder(null);
          }}
          onStatusUpdate={(orderId, status, notes) => updateOrderStatus(orderId, status, notes)}
        />
      )}

      {/* Status Update Modal */}
      {showStatusModal && selectedOrder && (
        <StatusUpdateModal
          order={selectedOrder}
          onClose={() => {
            setShowStatusModal(false);
            setSelectedOrder(null);
          }}
          onUpdate={(orderId, status, notes) => updateOrderStatus(orderId, status, notes)}
        />
      )}
    </div>
  );
};

// Order Details Modal Component
const OrderDetailsModal = ({ order, onClose, onStatusUpdate }) => {
  const [newStatus, setNewStatus] = useState(order.OrderStatus);
  const [notes, setNotes] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending': return '#F59E0B';
      case 'confirmed': return '#3B82F6';
      case 'processing': return '#F0B21B';
      case 'shipped': return '#8B5CF6';
      case 'delivered': return '#10B981';
      case 'cancelled': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid': return '#10B981';
      case 'pending': return '#F59E0B';
      case 'failed': return '#EF4444';
      case 'refunded': return '#6B7280';
      default: return '#6B7280';
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content large" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Order Details - {order.OrderNumber}</h3>
          <button className="modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-body">
          <div className="order-details-grid">
            {/* Customer Information */}
            <div className="detail-section">
              <h4>Customer Information</h4>
              <div className="detail-item">
                <label>Name:</label>
                <span>{order.CustomerFullName || order.CustomerName}</span>
              </div>
              <div className="detail-item">
                <label>Email:</label>
                <span>{order.CustomerEmail}</span>
              </div>
              {order.CustomerPhone && (
                <div className="detail-item">
                  <label>Phone:</label>
                  <span>{order.CustomerPhone}</span>
                </div>
              )}
            </div>

            {/* Order Information */}
            <div className="detail-section">
              <h4>Order Information</h4>
              <div className="detail-item">
                <label>Order Number:</label>
                <span>{order.OrderNumber}</span>
              </div>
              <div className="detail-item">
                <label>Order Date:</label>
                <span>{formatDate(order.CreatedAt)}</span>
              </div>
              <div className="detail-item">
                <label>Status:</label>
                <span className="status-badge" style={{ backgroundColor: getStatusColor(order.OrderStatus) }}>
                  {order.OrderStatus}
                </span>
              </div>
              <div className="detail-item">
                <label>Payment Status:</label>
                <span className="status-badge" style={{ backgroundColor: getPaymentStatusColor(order.PaymentStatus) }}>
                  {order.PaymentStatus}
                </span>
              </div>
            </div>

            {/* Shipping Information */}
            <div className="detail-section full-width">
              <h4>Shipping Address</h4>
              <p>{order.ShippingAddress}</p>
              {order.BillingAddress && order.BillingAddress !== order.ShippingAddress && (
                <>
                  <h4>Billing Address</h4>
                  <p>{order.BillingAddress}</p>
                </>
              )}
            </div>

            {/* Order Items */}
            {order.items && order.items.length > 0 && (
              <div className="detail-section full-width">
                <h4>Order Items</h4>
                <table className="order-items-table">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Quantity</th>
                      <th>Unit Price</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {order.items.map((item, index) => (
                      <tr key={index}>
                        <td>{item.ProductName || item.VariantID}</td>
                        <td>{item.Quantity}</td>
                        <td>{formatCurrency(item.UnitPrice)}</td>
                        <td>{formatCurrency(item.TotalPrice)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Order Totals */}
            <div className="detail-section">
              <h4>Order Summary</h4>
              <div className="order-totals">
                <div className="total-line">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(order.SubTotal)}</span>
                </div>
                {order.TaxAmount > 0 && (
                  <div className="total-line">
                    <span>Tax:</span>
                    <span>{formatCurrency(order.TaxAmount)}</span>
                  </div>
                )}
                {order.ShippingAmount > 0 && (
                  <div className="total-line">
                    <span>Shipping:</span>
                    <span>{formatCurrency(order.ShippingAmount)}</span>
                  </div>
                )}
                {order.DiscountAmount > 0 && (
                  <div className="total-line discount">
                    <span>Discount:</span>
                    <span>-{formatCurrency(order.DiscountAmount)}</span>
                  </div>
                )}
                <div className="total-line total">
                  <span>Total:</span>
                  <span>{formatCurrency(order.TotalAmount)}</span>
                </div>
              </div>
            </div>

            {/* Notes */}
            {order.Notes && (
              <div className="detail-section full-width">
                <h4>Notes</h4>
                <p>{order.Notes}</p>
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-outline" onClick={onClose}>
            Close
          </button>
          <button
            className="btn btn-primary"
            onClick={() => {
              // Open status update modal
              onStatusUpdate(order.OrderID, newStatus, notes);
            }}
          >
            Update Status
          </button>
        </div>
      </div>
    </div>
  );
};

// Status Update Modal Component
const StatusUpdateModal = ({ order, onClose, onUpdate }) => {
  const [newStatus, setNewStatus] = useState(order.OrderStatus);
  const [notes, setNotes] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdate(order.OrderID, newStatus, notes);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending': return '#F59E0B';
      case 'confirmed': return '#3B82F6';
      case 'processing': return '#F0B21B';
      case 'shipped': return '#8B5CF6';
      case 'delivered': return '#10B981';
      case 'cancelled': return '#EF4444';
      default: return '#6B7280';
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Update Order Status</h3>
          <button className="modal-close" onClick={onClose}>&times;</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <div className="form-group">
              <label>Order Number:</label>
              <input type="text" value={order.OrderNumber} disabled />
            </div>

            <div className="form-group">
              <label>Current Status:</label>
              <span className="status-badge" style={{ backgroundColor: getStatusColor(order.OrderStatus) }}>
                {order.OrderStatus}
              </span>
            </div>

            <div className="form-group">
              <label>New Status:</label>
              <select
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
                required
              >
                <option value="Pending">Pending</option>
                <option value="Confirmed">Confirmed</option>
                <option value="Processing">Processing</option>
                <option value="Shipped">Shipped</option>
                <option value="Delivered">Delivered</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>

            <div className="form-group">
              <label>Notes (Optional):</label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add notes about this status change..."
                rows="3"
              />
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-outline" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Update Status
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default OrderManagement;
