import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import PayMongoCheckout from '../components/payment/PayMongoCheckout';
import './CheckoutPage.css';

const CheckoutPage = () => {
    const navigate = useNavigate();
    const { items, getSubtotal, getTax, getShipping, getTotal, clearCart } = useCart();
    
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('paymongo');
    
    const [shippingAddress, setShippingAddress] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        province: '',
        postalCode: '',
        country: 'Philippines'
    });

    const [billingAddress, setBillingAddress] = useState({
        sameAsShipping: true,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        province: '',
        postalCode: '',
        country: 'Philippines'
    });

    useEffect(() => {
        // Redirect if cart is empty
        if (!items || items.length === 0) {
            navigate('/cart');
        }
    }, [items, navigate]);

    const handleInputChange = (section, field, value) => {
        if (section === 'shipping') {
            setShippingAddress(prev => ({ ...prev, [field]: value }));
        } else if (section === 'billing') {
            setBillingAddress(prev => ({ ...prev, [field]: value }));
        }
    };

    const handlePaymentSuccess = (paymentData) => {
        console.log('Payment successful:', paymentData);
        clearCart();
        navigate('/order-confirmation', { state: { paymentData } });
    };

    const handlePaymentError = (error) => {
        console.error('Payment error:', error);
        setError('Payment failed. Please try again.');
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(price);
    };

    if (!items || items.length === 0) {
        return (
            <div className="checkout-page">
                <div className="empty-cart">
                    <h2>Your cart is empty</h2>
                    <button onClick={() => navigate('/products')} className="btn btn-primary">
                        Continue Shopping
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="checkout-page">
            <div className="checkout-container">
                {/* Main Checkout Form */}
                <div className="checkout-form-area">
                    {error && (
                        <div className="error-alert">
                            <span className="error-icon">⚠️</span>
                            <span className="error-text">{error}</span>
                        </div>
                    )}

                    {/* Shipping Address Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">1</div>
                            <span>Shipping Address</span>
                        </div>
                        <div className="section-content">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        value={shippingAddress.firstName}
                                        onChange={(e) => handleInputChange('shipping', 'firstName', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        value={shippingAddress.lastName}
                                        onChange={(e) => handleInputChange('shipping', 'lastName', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">Email *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={shippingAddress.email}
                                        onChange={(e) => handleInputChange('shipping', 'email', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phone">Phone *</label>
                                    <input
                                        type="tel"
                                        id="phone"
                                        value={shippingAddress.phone}
                                        onChange={(e) => handleInputChange('shipping', 'phone', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row single">
                                <div className="form-group">
                                    <label htmlFor="address">Address *</label>
                                    <input
                                        type="text"
                                        id="address"
                                        value={shippingAddress.address}
                                        onChange={(e) => handleInputChange('shipping', 'address', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="city">City *</label>
                                    <input
                                        type="text"
                                        id="city"
                                        value={shippingAddress.city}
                                        onChange={(e) => handleInputChange('shipping', 'city', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="province">Province *</label>
                                    <input
                                        type="text"
                                        id="province"
                                        value={shippingAddress.province}
                                        onChange={(e) => handleInputChange('shipping', 'province', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="postalCode">Postal Code *</label>
                                    <input
                                        type="text"
                                        id="postalCode"
                                        value={shippingAddress.postalCode}
                                        onChange={(e) => handleInputChange('shipping', 'postalCode', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="country">Country *</label>
                                    <select
                                        id="country"
                                        value={shippingAddress.country}
                                        onChange={(e) => handleInputChange('shipping', 'country', e.target.value)}
                                        required
                                        disabled={loading}
                                    >
                                        <option value="Philippines">Philippines</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Shipping Methods Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">2</div>
                            <span>Shipping Methods</span>
                        </div>
                        <div className="section-content">
                            <div className="payment-methods">
                                <div className="payment-method selected">
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <span className="payment-method-name">Manual Shipping Costs Calculation</span>
                                    </div>
                                    <div className="payment-method-description">
                                        To keep costs as low as possible, our customer service will manually calculate 
                                        shipping costs after an order is placed (at the moment they show ₱0.00). After 
                                        calculating costs, you will decide whether to confirm an order or change.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Additional Information Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">3</div>
                            <span>Additional Information</span>
                        </div>
                        <div className="section-content">
                            <div className="form-row single">
                                <div className="form-group">
                                    <label htmlFor="orderComments">Order Comments</label>
                                    <textarea
                                        id="orderComments"
                                        rows="4"
                                        placeholder="Notes about your order, e.g. special notes for delivery."
                                        disabled={loading}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Payment Method Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">4</div>
                            <span>Payment Method</span>
                        </div>
                        <div className="section-content">
                            <div className="payment-methods">
                                <div 
                                    className={`payment-method ${paymentMethod === 'bank' ? 'selected' : ''}`}
                                    onClick={() => setPaymentMethod('bank')}
                                >
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <span className="payment-method-name">Bank Transfer</span>
                                    </div>
                                    <div className="payment-method-description">
                                        Transfer the money to the account indicated on the invoice. Your order will be 
                                        processed after the payment is credited.
                                    </div>
                                </div>
                                
                                <div 
                                    className={`payment-method ${paymentMethod === 'paymongo' ? 'selected' : ''}`}
                                    onClick={() => setPaymentMethod('paymongo')}
                                >
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <span className="payment-method-name">PayMongo</span>
                                    </div>
                                    <div className="payment-method-description">
                                        Pay securely using PayMongo payment gateway with credit/debit cards, GCash, and other payment methods.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Order Summary Sidebar */}
                <div className="order-summary">
                    <div className="order-summary-header">
                        <h3>
                            <span className="summary-icon">₱</span>
                            ORDER SUMMARY
                        </h3>
                    </div>
                    <div className="order-summary-content">
                        {/* Cart Items */}
                        <div className="cart-items">
                            {items.map((item) => (
                                <div key={item.id} className="cart-item">
                                    <img 
                                        src={item.image || '/api/placeholder/60/60'} 
                                        alt={item.name}
                                        className="cart-item-image"
                                    />
                                    <div className="cart-item-details">
                                        <div className="cart-item-name">{item.name}</div>
                                        <div className="cart-item-quantity">Qty: {item.quantity}</div>
                                    </div>
                                    <div className="cart-item-price">
                                        {formatPrice(item.price * item.quantity)}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Order Totals */}
                        <div className="order-totals">
                            <div className="total-row">
                                <span>Cart Subtotal:</span>
                                <span>{formatPrice(getSubtotal())}</span>
                            </div>
                            <div className="total-row">
                                <span>Shipping:</span>
                                <span>{formatPrice(getShipping())}</span>
                            </div>
                            <div className="total-row final">
                                <span>Order Total Excl. Tax:</span>
                                <span>{formatPrice(getTotal())}</span>
                            </div>
                            <div className="total-row final">
                                <span>Order Total Incl. Tax:</span>
                                <span>{formatPrice(getTotal())}</span>
                            </div>
                        </div>

                        {/* PayMongo Checkout */}
                        {paymentMethod === 'paymongo' && (
                            <PayMongoCheckout
                                items={items}
                                shippingAddress={shippingAddress}
                                total={getTotal()}
                                onSuccess={handlePaymentSuccess}
                                onError={handlePaymentError}
                                loading={loading}
                                setLoading={setLoading}
                            />
                        )}

                        {/* Place Order Button for Bank Transfer */}
                        {paymentMethod === 'bank' && (
                            <button 
                                className="place-order-btn"
                                disabled={loading}
                                onClick={() => {
                                    // Handle bank transfer order placement
                                    console.log('Bank transfer order placed');
                                }}
                            >
                                {loading ? 'Processing...' : 'Place Order'}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CheckoutPage;
