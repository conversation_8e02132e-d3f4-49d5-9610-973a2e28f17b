const axios = require('axios');

/**
 * PayMongo Configuration and API Client
 * Handles all PayMongo API interactions for DesignXcel office furniture e-commerce
 */

class PayMongoClient {
  constructor() {
    this.baseURL = 'https://api.paymongo.com/v1';
    this.publicKey = process.env.PAYMONGO_PUBLIC_KEY;
    this.secretKey = process.env.PAYMONGO_SECRET_KEY;
    
    if (!this.publicKey || !this.secretKey) {
      console.warn('PayMongo API keys not configured. Payment functionality will be limited.');
    }
    
    // Create axios instance with default configuration
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000 // 30 seconds timeout
    });
    
    // Add request interceptor for authentication
    this.client.interceptors.request.use(
      (config) => {
        // Use secret key for all server-side operations including payment links
        // Public key is only for client-side operations
        config.auth = {
          username: this.secretKey,
          password: ''
        };
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('PayMongo API Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Create a PayMongo payment link
   * @param {Object} options - Payment link options
   * @param {number} options.amount - Amount in centavos (PHP)
   * @param {string} options.description - Payment description
   * @param {string} options.remarks - Payment remarks
   * @param {Object} options.metadata - Additional metadata
   * @returns {Promise<Object>} Payment link data
   */
  async createPaymentLink(options) {
    try {
      const {
        amount,
        description = 'DesignXcel Office Furniture Purchase',
        remarks = 'Payment for office furniture order',
        metadata = {}
      } = options;
      
      // Validate amount (must be in centavos)
      if (!amount || amount < 100) {
        throw new Error('Amount must be at least 100 centavos (PHP 1.00)');
      }
      
      const payload = {
        data: {
          attributes: {
            amount: Math.round(amount), // Ensure integer centavos
            description,
            remarks,
            metadata: {
              ...metadata,
              merchant: 'DesignXcel',
              platform: 'office-ecommerce'
            }
          }
        }
      };
      
      const response = await this.client.post('/links', payload);
      return response.data;
      
    } catch (error) {
      console.error('Create payment link error:', error);
      throw new Error(`Failed to create payment link: ${error.message}`);
    }
  }
  
  /**
   * Retrieve payment link details
   * @param {string} linkId - Payment link ID
   * @returns {Promise<Object>} Payment link data
   */
  async getPaymentLink(linkId) {
    try {
      const response = await this.client.get(`/links/${linkId}`);
      return response.data;
    } catch (error) {
      console.error('Get payment link error:', error);
      throw new Error(`Failed to retrieve payment link: ${error.message}`);
    }
  }
  
  /**
   * Archive (disable) a payment link
   * @param {string} linkId - Payment link ID
   * @returns {Promise<Object>} Archived payment link data
   */
  async archivePaymentLink(linkId) {
    try {
      const response = await this.client.post(`/links/${linkId}/archive`);
      return response.data;
    } catch (error) {
      console.error('Archive payment link error:', error);
      throw new Error(`Failed to archive payment link: ${error.message}`);
    }
  }
  
  /**
   * Retrieve payment details
   * @param {string} paymentId - Payment ID
   * @returns {Promise<Object>} Payment data
   */
  async getPayment(paymentId) {
    try {
      const response = await this.client.get(`/payments/${paymentId}`);
      return response.data;
    } catch (error) {
      console.error('Get payment error:', error);
      throw new Error(`Failed to retrieve payment: ${error.message}`);
    }
  }
  
  /**
   * List payments with optional filters
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Payments list
   */
  async listPayments(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.before) params.append('before', filters.before);
      if (filters.after) params.append('after', filters.after);
      
      const response = await this.client.get(`/payments?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('List payments error:', error);
      throw new Error(`Failed to list payments: ${error.message}`);
    }
  }
  
  /**
   * Convert PHP amount to centavos
   * @param {number} phpAmount - Amount in PHP
   * @returns {number} Amount in centavos
   */
  static phpToCentavos(phpAmount) {
    return Math.round(phpAmount * 100);
  }
  
  /**
   * Convert centavos to PHP amount
   * @param {number} centavos - Amount in centavos
   * @returns {number} Amount in PHP
   */
  static centavosToPHP(centavos) {
    return centavos / 100;
  }
  
  /**
   * Validate webhook signature using HMAC-SHA256
   * @param {string} payload - Raw webhook payload
   * @param {string} signature - PayMongo signature header
   * @returns {boolean} Is signature valid
   */
  validateWebhookSignature(payload, signature) {
    try {
      if (!this.webhookSecret) {
        console.warn('⚠️ Webhook secret not configured - skipping signature validation');
        return true; // Allow in development if webhook secret not set
      }

      if (!signature) {
        console.error('❌ No signature provided in webhook request');
        return false;
      }

      // PayMongo signature format: "t=timestamp,v1=signature"
      const crypto = require('crypto');

      // Extract timestamp and signature from header
      const elements = signature.split(',');
      const timestampElement = elements.find(el => el.startsWith('t='));
      const signatureElement = elements.find(el => el.startsWith('v1='));

      if (!timestampElement || !signatureElement) {
        console.error('❌ Invalid signature format');
        return false;
      }

      const timestamp = timestampElement.split('=')[1];
      const providedSignature = signatureElement.split('=')[1];

      // Create expected signature
      const signedPayload = timestamp + '.' + payload;
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(signedPayload, 'utf8')
        .digest('hex');

      // Compare signatures using constant-time comparison
      const isValid = crypto.timingSafeEqual(
        Buffer.from(providedSignature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );

      if (!isValid) {
        console.error('❌ Webhook signature validation failed');
        console.log('Expected:', expectedSignature);
        console.log('Provided:', providedSignature);
      } else {
        console.log('✅ Webhook signature validated successfully');
      }

      return isValid;

    } catch (error) {
      console.error('❌ Webhook signature validation error:', error);
      return false;
    }
  }
}

// Create singleton instance
const paymongoClient = new PayMongoClient();

module.exports = {
  PayMongoClient,
  paymongoClient
};
