const { paymongoClient } = require('../config/paymongo');
const { v4: uuidv4 } = require('uuid');

/**
 * Payment Service for DesignXcel Office Furniture E-commerce
 * Handles payment processing, order integration, and transaction management
 */

class PaymentService {
  constructor() {
    this.paymongo = paymongoClient;
  }
  
  /**
   * Create payment link for an order
   * @param {Object} orderData - Order information
   * @param {string} orderData.orderId - Order ID
   * @param {number} orderData.totalAmount - Total amount in PHP
   * @param {Array} orderData.items - Order items
   * @param {Object} orderData.customer - Customer information
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Payment link and transaction data
   */
  async createOrderPaymentLink(orderData, options = {}) {
    try {
      const {
        orderId,
        totalAmount,
        items = [],
        customer = {},
        shippingAddress = {}
      } = orderData;
      
      // Validate required fields
      if (!orderId || !totalAmount) {
        throw new Error('Order ID and total amount are required');
      }
      
      if (totalAmount < 1) {
        throw new Error('Minimum payment amount is PHP 1.00');
      }
      
      // Convert PHP to centavos
      const amountInCentavos = this.paymongo.constructor.phpToCentavos(totalAmount);
      
      // Create detailed description
      const itemsDescription = items.length > 0 
        ? items.map(item => `${item.name} (${item.quantity}x)`).join(', ')
        : 'Office furniture order';
      
      const description = `DesignXcel Order #${orderId} - ${itemsDescription}`;
      
      // Prepare metadata
      const metadata = {
        orderId,
        customerEmail: customer.email || '',
        customerName: customer.name || '',
        itemCount: items.length,
        currency: 'PHP',
        platform: 'office-ecommerce',
        version: '1.0',
        createdAt: new Date().toISOString(),
        ...options.metadata
      };
      
      // Create payment link
      const paymentLinkResponse = await this.paymongo.createPaymentLink({
        amount: amountInCentavos,
        description,
        remarks: `Payment for DesignXcel order ${orderId}`,
        metadata
      });
      
      // Extract payment link data
      const paymentLink = paymentLinkResponse.data;
      
      // Create transaction record
      const transaction = {
        id: uuidv4(),
        orderId,
        paymentLinkId: paymentLink.id,
        paymentUrl: paymentLink.attributes.checkout_url,
        amount: totalAmount,
        amountInCentavos,
        currency: 'PHP',
        status: 'pending',
        paymentMethod: null,
        transactionId: null,
        metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return {
        success: true,
        data: {
          transaction,
          paymentLink: {
            id: paymentLink.id,
            url: paymentLink.attributes.checkout_url,
            reference: paymentLink.attributes.reference_number,
            status: paymentLink.attributes.status,
            amount: totalAmount,
            currency: 'PHP'
          }
        }
      };
      
    } catch (error) {
      console.error('Create order payment link error:', error);
      return {
        success: false,
        error: error.message,
        code: 'PAYMENT_LINK_CREATION_FAILED'
      };
    }
  }
  
  /**
   * Process payment completion webhook
   * @param {Object} webhookData - PayMongo webhook data
   * @returns {Promise<Object>} Processing result
   */
  async processPaymentWebhook(webhookData) {
    try {
      const { data: paymentData } = webhookData;

      if (!paymentData || !paymentData.attributes) {
        throw new Error('Invalid webhook data structure');
      }

      const payment = paymentData.attributes;
      const metadata = payment.metadata || {};

      // Extract order information from metadata
      const orderId = metadata.orderId;
      if (!orderId) {
        throw new Error('Order ID not found in payment metadata');
      }

      // Determine payment status
      const paymentStatus = this.mapPayMongoStatus(payment.status);
      
      // Create payment record
      const paymentRecord = {
        id: `PAY_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        orderId,
        paymentId: paymentData.id,
        amount: this.paymongo.constructor.centavosToPHP(payment.amount),
        amountInCentavos: payment.amount,
        currency: payment.currency,
        status: paymentStatus,
        paymentMethod: payment.source?.type || 'unknown',
        transactionId: payment.external_reference_number || paymentData.id,
        paymentDate: payment.paid_at || new Date().toISOString(),
        metadata: {
          ...metadata,
          paymongoPaymentId: paymentData.id,
          paymentSource: payment.source,
          fees: payment.fee,
          netAmount: payment.net_amount
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return {
        success: true,
        data: {
          paymentRecord,
          orderId,
          paymentStatus,
          paymentData: payment,
          shouldUpdateOrder: paymentStatus === 'paid',
          metadata
        }
      };
      
    } catch (error) {
      console.error('Process payment webhook error:', error);
      return {
        success: false,
        error: error.message,
        code: 'WEBHOOK_PROCESSING_FAILED'
      };
    }
  }
  
  /**
   * Get payment status for an order
   * @param {string} orderId - Order ID
   * @param {string} paymentLinkId - Payment link ID
   * @returns {Promise<Object>} Payment status
   */
  async getOrderPaymentStatus(orderId, paymentLinkId) {
    try {
      const paymentLinkData = await this.paymongo.getPaymentLink(paymentLinkId);
      const paymentLink = paymentLinkData.data;
      
      return {
        success: true,
        data: {
          orderId,
          paymentLinkId,
          status: paymentLink.attributes.status,
          amount: this.paymongo.constructor.centavosToPHP(paymentLink.attributes.amount),
          currency: 'PHP',
          reference: paymentLink.attributes.reference_number,
          checkoutUrl: paymentLink.attributes.checkout_url,
          updatedAt: paymentLink.attributes.updated_at
        }
      };
      
    } catch (error) {
      console.error('Get order payment status error:', error);
      return {
        success: false,
        error: error.message,
        code: 'PAYMENT_STATUS_RETRIEVAL_FAILED'
      };
    }
  }
  
  /**
   * Cancel/archive a payment link
   * @param {string} paymentLinkId - Payment link ID
   * @returns {Promise<Object>} Cancellation result
   */
  async cancelPaymentLink(paymentLinkId) {
    try {
      const result = await this.paymongo.archivePaymentLink(paymentLinkId);
      
      return {
        success: true,
        data: {
          paymentLinkId,
          status: 'archived',
          archivedAt: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error('Cancel payment link error:', error);
      return {
        success: false,
        error: error.message,
        code: 'PAYMENT_CANCELLATION_FAILED'
      };
    }
  }
  
  /**
   * Map PayMongo payment status to internal status
   * @param {string} paymongoStatus - PayMongo status
   * @returns {string} Internal status
   */
  mapPayMongoStatus(paymongoStatus) {
    const statusMap = {
      'paid': 'completed',
      'pending': 'pending',
      'failed': 'failed',
      'cancelled': 'cancelled',
      'expired': 'expired'
    };
    
    return statusMap[paymongoStatus] || 'unknown';
  }
  
  /**
   * Calculate payment fees (for display purposes)
   * @param {number} amount - Amount in PHP
   * @param {string} paymentMethod - Payment method
   * @returns {Object} Fee calculation
   */
  calculatePaymentFees(amount, paymentMethod = 'card') {
    // PayMongo fee structure (approximate)
    const feeRates = {
      card: 0.035, // 3.5% + PHP 15
      gcash: 0.025, // 2.5%
      grabpay: 0.025, // 2.5%
      bank: 0.015 // 1.5%
    };
    
    const rate = feeRates[paymentMethod] || feeRates.card;
    const percentageFee = amount * rate;
    const fixedFee = paymentMethod === 'card' ? 15 : 0;
    const totalFee = percentageFee + fixedFee;
    
    return {
      amount,
      percentageFee: Math.round(percentageFee * 100) / 100,
      fixedFee,
      totalFee: Math.round(totalFee * 100) / 100,
      netAmount: Math.round((amount - totalFee) * 100) / 100
    };
  }
}

// Create singleton instance
const paymentService = new PaymentService();

module.exports = {
  PaymentService,
  paymentService
};
