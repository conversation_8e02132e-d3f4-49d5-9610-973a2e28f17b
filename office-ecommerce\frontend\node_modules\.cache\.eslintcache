[{"C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "1", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "2", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "3", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "4", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "5", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "6", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "7", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "8", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "9", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "10", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "11", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "12", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "13", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "14", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "15", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "16", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "17", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "18", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "19", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "20", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "21", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "22", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "23", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "24", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "25", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "26", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "27", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "28", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "29", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "30", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "31", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "32", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "33", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "34", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "35", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "36", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "37", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "38", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "39", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "40", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "41", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "42", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "43", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js": "44", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "45", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "46", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "47", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "48", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "49", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "50", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "51", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "52", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "53", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "54", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "55", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "56", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "57", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\debug\\CheckoutDebug.js": "58"}, {"size": 535, "mtime": 1748532507943, "results": "59", "hashOfConfig": "60"}, {"size": 5028, "mtime": 1751282094105, "results": "61", "hashOfConfig": "60"}, {"size": 362, "mtime": 1748532508072, "results": "62", "hashOfConfig": "60"}, {"size": 18118, "mtime": 1751248889837, "results": "63", "hashOfConfig": "60"}, {"size": 9763, "mtime": 1751248889835, "results": "64", "hashOfConfig": "60"}, {"size": 3603, "mtime": 1751268810036, "results": "65", "hashOfConfig": "60"}, {"size": 22019, "mtime": 1751370337862, "results": "66", "hashOfConfig": "60"}, {"size": 8266, "mtime": 1748588851869, "results": "67", "hashOfConfig": "60"}, {"size": 14750, "mtime": 1751098406703, "results": "68", "hashOfConfig": "60"}, {"size": 9289, "mtime": 1751076835728, "results": "69", "hashOfConfig": "60"}, {"size": 11351, "mtime": 1751114550454, "results": "70", "hashOfConfig": "60"}, {"size": 902, "mtime": 1749564444973, "results": "71", "hashOfConfig": "60"}, {"size": 3580, "mtime": 1751268766298, "results": "72", "hashOfConfig": "60"}, {"size": 14768, "mtime": 1751099196771, "results": "73", "hashOfConfig": "60"}, {"size": 13424, "mtime": 1748592599251, "results": "74", "hashOfConfig": "60"}, {"size": 11059, "mtime": 1750776054404, "results": "75", "hashOfConfig": "60"}, {"size": 7100, "mtime": 1750777437332, "results": "76", "hashOfConfig": "60"}, {"size": 5674, "mtime": 1748533676070, "results": "77", "hashOfConfig": "60"}, {"size": 9980, "mtime": 1751289362867, "results": "78", "hashOfConfig": "60"}, {"size": 5553, "mtime": 1748536575807, "results": "79", "hashOfConfig": "60"}, {"size": 5314, "mtime": 1751292026071, "results": "80", "hashOfConfig": "60"}, {"size": 2001, "mtime": 1748586710767, "results": "81", "hashOfConfig": "60"}, {"size": 5487, "mtime": 1748536713270, "results": "82", "hashOfConfig": "60"}, {"size": 2632, "mtime": 1749573723202, "results": "83", "hashOfConfig": "60"}, {"size": 5719, "mtime": 1751076760895, "results": "84", "hashOfConfig": "60"}, {"size": 3375, "mtime": 1751249988052, "results": "85", "hashOfConfig": "60"}, {"size": 5949, "mtime": 1748749520301, "results": "86", "hashOfConfig": "60"}, {"size": 6574, "mtime": 1751098546647, "results": "87", "hashOfConfig": "60"}, {"size": 4139, "mtime": 1751076620437, "results": "88", "hashOfConfig": "60"}, {"size": 2725, "mtime": 1751076777483, "results": "89", "hashOfConfig": "60"}, {"size": 9556, "mtime": 1751175718397, "results": "90", "hashOfConfig": "60"}, {"size": 4182, "mtime": 1751175561430, "results": "91", "hashOfConfig": "60"}, {"size": 53614, "mtime": 1751251969971, "results": "92", "hashOfConfig": "60"}, {"size": 11069, "mtime": 1751278341909, "results": "93", "hashOfConfig": "60"}, {"size": 23206, "mtime": 1751291574026, "results": "94", "hashOfConfig": "60"}, {"size": 7098, "mtime": 1751277959504, "results": "95", "hashOfConfig": "60"}, {"size": 3302, "mtime": 1751251037241, "results": "96", "hashOfConfig": "60"}, {"size": 7721, "mtime": 1751275232194, "results": "97", "hashOfConfig": "60"}, {"size": 11918, "mtime": 1751270768445, "results": "98", "hashOfConfig": "60"}, {"size": 8611, "mtime": 1751273492029, "results": "99", "hashOfConfig": "60"}, {"size": 2536, "mtime": 1751268691259, "results": "100", "hashOfConfig": "60"}, {"size": 3006, "mtime": 1751268706616, "results": "101", "hashOfConfig": "60"}, {"size": 3026, "mtime": 1751268678190, "results": "102", "hashOfConfig": "60"}, {"size": 2359, "mtime": 1751268665631, "results": "103", "hashOfConfig": "60"}, {"size": 8949, "mtime": 1751275710034, "results": "104", "hashOfConfig": "60"}, {"size": 5223, "mtime": 1751272178383, "results": "105", "hashOfConfig": "60"}, {"size": 5117, "mtime": 1751270698706, "results": "106", "hashOfConfig": "60"}, {"size": 3823, "mtime": 1751272134180, "results": "107", "hashOfConfig": "60"}, {"size": 5749, "mtime": 1751272157563, "results": "108", "hashOfConfig": "60"}, {"size": 9536, "mtime": 1751273118201, "results": "109", "hashOfConfig": "60"}, {"size": 809, "mtime": 1751274434387, "results": "110", "hashOfConfig": "60"}, {"size": 1304, "mtime": 1751274450977, "results": "111", "hashOfConfig": "60"}, {"size": 1283, "mtime": 1751274443138, "results": "112", "hashOfConfig": "60"}, {"size": 3344, "mtime": 1751274468324, "results": "113", "hashOfConfig": "60"}, {"size": 4827, "mtime": 1751274508918, "results": "114", "hashOfConfig": "60"}, {"size": 2754, "mtime": 1751275214837, "results": "115", "hashOfConfig": "60"}, {"size": 9614, "mtime": 1751371679217, "results": "116", "hashOfConfig": "60"}, {"size": 5336, "mtime": 1751280048595, "results": "117", "hashOfConfig": "60"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbkk32", {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\debug\\CheckoutDebug.js", [], []]