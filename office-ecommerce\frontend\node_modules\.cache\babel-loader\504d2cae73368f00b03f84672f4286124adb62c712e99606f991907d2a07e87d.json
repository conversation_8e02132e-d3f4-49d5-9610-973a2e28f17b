{"ast": null, "code": "import _objectSpread from\"C:/DesignXcel/office-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{useCart}from'../../contexts/CartContext';import paymentService from'../../services/paymentService';import apiClient from'../../services/apiClient';import'./PayMongoCheckout.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PayMongoCheckout=_ref=>{let{orderData,onSuccess,onError,onCancel}=_ref;const navigate=useNavigate();const{clearCart}=useCart();const[loading,setLoading]=useState(false);const[paymentLink,setPaymentLink]=useState(null);const[error,setError]=useState('');const[fees,setFees]=useState(null);const[selectedMethod,setSelectedMethod]=useState('card');// Payment method options\nconst paymentMethods=[{id:'card',name:'Credit/Debit Card',icon:'💳',description:'Visa, Mastercard, JCB, American Express',fee:'3.5% + ₱15'},{id:'gcash',name:'GCash',icon:'📱',description:'Pay using your GCash wallet',fee:'2.5%'},{id:'grabpay',name:'GrabPay',icon:'🚗',description:'Pay using your GrabPay wallet',fee:'2.5%'},{id:'bank',name:'Online Banking',icon:'🏦',description:'Direct bank transfer',fee:'1.5%'}];// Calculate fees when component mounts or method changes\nuseEffect(()=>{if(orderData!==null&&orderData!==void 0&&orderData.totalAmount){calculatePaymentFees();}},[orderData===null||orderData===void 0?void 0:orderData.totalAmount,selectedMethod]);const calculatePaymentFees=async()=>{try{const amountInCentavos=Math.round(orderData.totalAmount*100);// Convert PHP to centavos\nconst result=await paymentService.calculateFees(amountInCentavos,selectedMethod);if(result.success){setFees(result.data);}}catch(error){console.error('Fee calculation error:',error);}};const handleCreatePaymentLink=async()=>{setLoading(true);setError('');try{var _orderData$shippingAd,_orderData$shippingAd2,_orderData$shippingAd3,_orderData$shippingAd4,_orderData$shippingAd5,_orderData$shippingAd6,_orderData$shippingAd7,_orderData$shippingAd8,_orderData$shippingAd9,_orderData$shippingAd0,_orderData$shippingAd1,_orderData$shippingAd10,_orderData$shippingAd11,_orderData$shippingAd12,_orderData$shippingAd13,_orderData$shippingAd14,_orderData$shippingAd15;// First, create a pending order in the backend\nconst shippingAddressString=\"\".concat(((_orderData$shippingAd=orderData.shippingAddress)===null||_orderData$shippingAd===void 0?void 0:_orderData$shippingAd.address)||'',\", \").concat(((_orderData$shippingAd2=orderData.shippingAddress)===null||_orderData$shippingAd2===void 0?void 0:_orderData$shippingAd2.city)||'',\", \").concat(((_orderData$shippingAd3=orderData.shippingAddress)===null||_orderData$shippingAd3===void 0?void 0:_orderData$shippingAd3.state)||'',\" \").concat(((_orderData$shippingAd4=orderData.shippingAddress)===null||_orderData$shippingAd4===void 0?void 0:_orderData$shippingAd4.zipCode)||'',\", \").concat(((_orderData$shippingAd5=orderData.shippingAddress)===null||_orderData$shippingAd5===void 0?void 0:_orderData$shippingAd5.country)||'').trim();const orderCreationData={customerEmail:(_orderData$shippingAd6=orderData.shippingAddress)===null||_orderData$shippingAd6===void 0?void 0:_orderData$shippingAd6.email,customerName:\"\".concat(((_orderData$shippingAd7=orderData.shippingAddress)===null||_orderData$shippingAd7===void 0?void 0:_orderData$shippingAd7.firstName)||'',\" \").concat(((_orderData$shippingAd8=orderData.shippingAddress)===null||_orderData$shippingAd8===void 0?void 0:_orderData$shippingAd8.lastName)||'').trim(),customerPhone:((_orderData$shippingAd9=orderData.shippingAddress)===null||_orderData$shippingAd9===void 0?void 0:_orderData$shippingAd9.phone)||undefined,// Use undefined instead of empty string\nshippingAddress:shippingAddressString.length>10?shippingAddressString:'Address not provided, will be updated from payment metadata',billingAddress:shippingAddressString.length>10?shippingAddressString:'Same as shipping address',items:orderData.items.map(item=>{var _item$product,_item$product2,_item$product3,_item$product4,_item$product5;return{variantId:((_item$product=item.product)===null||_item$product===void 0?void 0:_item$product.id)||item.variantId||item.id||'00000000-0000-0000-0000-000000000000',// Use product ID or fallback\nquantity:item.quantity||1,unitPrice:item.price||((_item$product2=item.product)===null||_item$product2===void 0?void 0:_item$product2.price)||((_item$product3=item.product)===null||_item$product3===void 0?void 0:_item$product3.discountPrice)||0,totalPrice:(item.quantity||1)*(item.price||((_item$product4=item.product)===null||_item$product4===void 0?void 0:_item$product4.price)||((_item$product5=item.product)===null||_item$product5===void 0?void 0:_item$product5.discountPrice)||0),customConfiguration:item.customization?JSON.stringify(item.customization):undefined};}),subTotal:orderData.subtotal||orderData.totalAmount||0,taxAmount:orderData.tax||0,shippingAmount:orderData.shipping||0,discountAmount:0,totalAmount:orderData.totalAmount||0,currency:'PHP',notes:'Order created for PayMongo payment processing'};// Debug: Log the order creation data\nconsole.log('Order creation data:',orderCreationData);console.log('Original order data:',orderData);// Create order in backend\nconst orderResult=await apiClient.post('/api/orders',orderCreationData);if(!orderResult.success){throw new Error(orderResult.message||'Failed to create order');}const createdOrder=orderResult.data.order;console.log('Order created in backend:',createdOrder.OrderNumber);// Prepare payment data with the created order ID\nconst paymentData={orderId:createdOrder.OrderNumber,totalAmount:Math.round(orderData.totalAmount*100),// Convert PHP to centavos\nitems:orderData.items||[],customer:{name:((_orderData$shippingAd0=orderData.shippingAddress)===null||_orderData$shippingAd0===void 0?void 0:_orderData$shippingAd0.firstName)+' '+((_orderData$shippingAd1=orderData.shippingAddress)===null||_orderData$shippingAd1===void 0?void 0:_orderData$shippingAd1.lastName),email:(_orderData$shippingAd10=orderData.shippingAddress)===null||_orderData$shippingAd10===void 0?void 0:_orderData$shippingAd10.email,phone:(_orderData$shippingAd11=orderData.shippingAddress)===null||_orderData$shippingAd11===void 0?void 0:_orderData$shippingAd11.phone},shippingAddress:orderData.shippingAddress||{},metadata:_objectSpread({paymentMethod:selectedMethod,source:'designxcel-checkout',backendOrderId:createdOrder.OrderID,items:JSON.stringify(orderData.items),shippingAddress:JSON.stringify(orderData.shippingAddress),customer:JSON.stringify({name:((_orderData$shippingAd12=orderData.shippingAddress)===null||_orderData$shippingAd12===void 0?void 0:_orderData$shippingAd12.firstName)+' '+((_orderData$shippingAd13=orderData.shippingAddress)===null||_orderData$shippingAd13===void 0?void 0:_orderData$shippingAd13.lastName),email:(_orderData$shippingAd14=orderData.shippingAddress)===null||_orderData$shippingAd14===void 0?void 0:_orderData$shippingAd14.email,phone:(_orderData$shippingAd15=orderData.shippingAddress)===null||_orderData$shippingAd15===void 0?void 0:_orderData$shippingAd15.phone}),taxAmount:orderData.tax||0,shippingAmount:orderData.shipping||0,discountAmount:0},orderData.metadata)};const result=await paymentService.createPaymentLink(paymentData);// Check if result has paymentLink (successful response structure)\nif(result&&result.paymentLink&&result.paymentLink.url){setPaymentLink(result.paymentLink);// Redirect to PayMongo checkout\nwindow.open(result.paymentLink.url,'_blank');// Start polling for payment status\nstartPaymentStatusPolling(createdOrder.OrderNumber,result.paymentLink.id);if(onSuccess){onSuccess(_objectSpread(_objectSpread({},result),{},{backendOrder:createdOrder}));}}else{throw new Error('Invalid payment link response structure');}}catch(error){console.error('Payment link creation error:',error);setError(error.message||'Failed to create payment link');if(onError){onError(error);}}finally{setLoading(false);}};const startPaymentStatusPolling=(orderId,paymentLinkId)=>{const pollInterval=setInterval(async()=>{try{const statusResult=await paymentService.getPaymentStatus(orderId,paymentLinkId);// Handle different response structures\nif(statusResult&&(statusResult.success||statusResult.status)){var _statusResult$data;const status=((_statusResult$data=statusResult.data)===null||_statusResult$data===void 0?void 0:_statusResult$data.status)||statusResult.status;if(status==='paid'){clearInterval(pollInterval);// Clear cart on successful payment\nclearCart();// Navigate to success page - the backend order will be updated via webhook\nnavigate('/order-success',{state:{order:{id:orderId,order_number:orderId,total_amount:orderData.totalAmount,currency:'PHP',status:'paid',created_at:new Date().toISOString(),shipping_address:orderData.shippingAddress,items:orderData.items},message:'Your payment has been processed successfully! Your order is being processed.',paymentStatus:'completed',paymentMethod:'PayMongo'}});}else if(status==='failed'||status==='cancelled'||status==='expired'){clearInterval(pollInterval);setError(\"Payment \".concat(status,\". Please try again.\"));}}}catch(error){console.error('Payment status polling error:',error);// If we get rate limited, stop polling to avoid further issues\nif(error.message&&error.message.includes('Too many requests')){console.log('Rate limited - stopping payment status polling');clearInterval(pollInterval);}}},15000);// Poll every 15 seconds to reduce rate limiting\n// Stop polling after 15 minutes\nsetTimeout(()=>{clearInterval(pollInterval);},900000);};const handleCancel=()=>{if(paymentLink){paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);}if(onCancel){onCancel();}};const formatCurrency=amount=>{return new Intl.NumberFormat('en-PH',{style:'currency',currency:'PHP'}).format(amount);};return/*#__PURE__*/_jsxs(\"div\",{className:\"paymongo-checkout\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"checkout-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Complete Your Payment\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Secure payment powered by PayMongo\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"order-summary\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Order Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Subtotal:\"}),/*#__PURE__*/_jsx(\"span\",{children:formatCurrency(orderData.totalAmount||0)})]}),fees&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Payment Fee:\"}),/*#__PURE__*/_jsx(\"span\",{children:formatCurrency(fees.totalFee)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-row total\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total Amount:\"}),/*#__PURE__*/_jsx(\"span\",{children:formatCurrency(fees.amount+fees.totalFee)})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"payment-methods\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Select Payment Method\"}),/*#__PURE__*/_jsx(\"div\",{className:\"method-grid\",children:paymentMethods.map(method=>/*#__PURE__*/_jsxs(\"div\",{className:\"payment-method \".concat(selectedMethod===method.id?'selected':''),onClick:()=>setSelectedMethod(method.id),children:[/*#__PURE__*/_jsx(\"div\",{className:\"method-icon\",children:method.icon}),/*#__PURE__*/_jsxs(\"div\",{className:\"method-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"method-name\",children:method.name}),/*#__PURE__*/_jsx(\"div\",{className:\"method-description\",children:method.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"method-fee\",children:[\"Fee: \",method.fee]})]})]},method.id))})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:error})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"payment-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-secondary\",onClick:handleCancel,disabled:loading,children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-primary\",onClick:handleCreatePaymentLink,disabled:loading||!(orderData!==null&&orderData!==void 0&&orderData.totalAmount),children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"loading-spinner\"}),\"Creating Payment Link...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"payment-icon\",children:\"\\uD83D\\uDD12\"}),\"Pay \",fees?formatCurrency(fees.amount+fees.totalFee):formatCurrency(orderData.totalAmount||0)]})})]}),paymentLink&&/*#__PURE__*/_jsxs(\"div\",{className:\"payment-link-status\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-icon\",children:\"\\u2705\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Payment link created successfully!\"})]}),/*#__PURE__*/_jsx(\"p\",{children:\"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"payment-link-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"info-row\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Reference:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"reference-number\",children:paymentLink.reference})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-row\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Status:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-badge\",children:paymentLink.status})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-notice\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"security-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"security-icon\",children:\"\\uD83D\\uDD10\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Secure Payment\"})]}),/*#__PURE__*/_jsx(\"p\",{children:\"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"})]})]});};export default PayMongoCheckout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}