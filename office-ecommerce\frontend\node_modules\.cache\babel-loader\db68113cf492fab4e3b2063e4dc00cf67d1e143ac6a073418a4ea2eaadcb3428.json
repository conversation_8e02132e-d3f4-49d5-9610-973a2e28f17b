{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\account\\\\ProfileManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfileManagement = () => {\n  _s();\n  const {\n    user,\n    updateProfile\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    company: '',\n    jobTitle: ''\n  });\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        firstName: user.firstName || '',\n        lastName: user.lastName || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        dateOfBirth: user.dateOfBirth || '',\n        company: user.company || '',\n        jobTitle: user.jobTitle || ''\n      });\n    }\n  }, [user]);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      const result = await updateProfile(formData);\n      if (result.success) {\n        setMessage('Profile updated successfully!');\n        setIsEditing(false);\n      } else {\n        setMessage(result.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      setMessage('An error occurred while updating your profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    // Reset form data to original user data\n    if (user) {\n      setFormData({\n        firstName: user.firstName || '',\n        lastName: user.lastName || '',\n        email: user.email || '',\n        phone: user.phone || '',\n        dateOfBirth: user.dateOfBirth || '',\n        company: user.company || '',\n        jobTitle: user.jobTitle || ''\n      });\n    }\n    setIsEditing(false);\n    setMessage('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Manage your personal information and contact details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this), !isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: () => setIsEditing(true),\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this), \"Edit Profile\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('success') ? 'success' : 'error'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"account-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"firstName\",\n            children: \"First Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"firstName\",\n            name: \"firstName\",\n            className: \"form-input\",\n            value: formData.firstName,\n            onChange: handleChange,\n            disabled: !isEditing,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"lastName\",\n            children: \"Last Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"lastName\",\n            name: \"lastName\",\n            className: \"form-input\",\n            value: formData.lastName,\n            onChange: handleChange,\n            disabled: !isEditing,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          htmlFor: \"email\",\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          name: \"email\",\n          className: \"form-input\",\n          value: formData.email,\n          onChange: handleChange,\n          disabled: !isEditing,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"phone\",\n            children: \"Phone Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            id: \"phone\",\n            name: \"phone\",\n            className: \"form-input\",\n            value: formData.phone,\n            onChange: handleChange,\n            disabled: !isEditing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"dateOfBirth\",\n            children: \"Date of Birth\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            id: \"dateOfBirth\",\n            name: \"dateOfBirth\",\n            className: \"form-input\",\n            value: formData.dateOfBirth,\n            onChange: handleChange,\n            disabled: !isEditing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"company\",\n            children: \"Company\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"company\",\n            name: \"company\",\n            className: \"form-input\",\n            value: formData.company,\n            onChange: handleChange,\n            disabled: !isEditing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            htmlFor: \"jobTitle\",\n            children: \"Job Title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"jobTitle\",\n            name: \"jobTitle\",\n            className: \"form-input\",\n            value: formData.jobTitle,\n            onChange: handleChange,\n            disabled: !isEditing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 17\n      }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-primary\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"spinner\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"4\",\n                fill: \"none\",\n                strokeDasharray: \"32\",\n                strokeDashoffset: \"32\",\n                children: /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"stroke-dashoffset\",\n                  dur: \"1s\",\n                  values: \"32;0;32\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 37\n            }, this), \"Saving...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"20,6 9,17 4,12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 37\n            }, this), \"Save Changes\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-secondary\",\n          onClick: handleCancel,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"18\",\n              y1: \"6\",\n              x2: \"6\",\n              y2: \"18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"6\",\n              y1: \"6\",\n              x2: \"18\",\n              y2: \"18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 29\n          }, this), \"Cancel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 9\n  }, this);\n};\n_s(ProfileManagement, \"h43RVupXgMs0TMWOeLAoFFJRea0=\", false, function () {\n  return [useAuth];\n});\n_c = ProfileManagement;\nexport default ProfileManagement;\nvar _c;\n$RefreshReg$(_c, \"ProfileManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileManagement", "_s", "user", "updateProfile", "isEditing", "setIsEditing", "loading", "setLoading", "message", "setMessage", "formData", "setFormData", "firstName", "lastName", "email", "phone", "dateOfBirth", "company", "jobTitle", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "error", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "d", "includes", "onSubmit", "htmlFor", "type", "id", "onChange", "disabled", "required", "cx", "cy", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "attributeName", "dur", "values", "repeatCount", "points", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/account/ProfileManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst ProfileManagement = () => {\n    const { user, updateProfile } = useAuth();\n    const [isEditing, setIsEditing] = useState(false);\n    const [loading, setLoading] = useState(false);\n    const [message, setMessage] = useState('');\n    const [formData, setFormData] = useState({\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        dateOfBirth: '',\n        company: '',\n        jobTitle: ''\n    });\n\n    useEffect(() => {\n        if (user) {\n            setFormData({\n                firstName: user.firstName || '',\n                lastName: user.lastName || '',\n                email: user.email || '',\n                phone: user.phone || '',\n                dateOfBirth: user.dateOfBirth || '',\n                company: user.company || '',\n                jobTitle: user.jobTitle || ''\n            });\n        }\n    }, [user]);\n\n    const handleChange = (e) => {\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setLoading(true);\n        setMessage('');\n\n        try {\n            const result = await updateProfile(formData);\n            if (result.success) {\n                setMessage('Profile updated successfully!');\n                setIsEditing(false);\n            } else {\n                setMessage(result.error || 'Failed to update profile');\n            }\n        } catch (error) {\n            setMessage('An error occurred while updating your profile');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleCancel = () => {\n        // Reset form data to original user data\n        if (user) {\n            setFormData({\n                firstName: user.firstName || '',\n                lastName: user.lastName || '',\n                email: user.email || '',\n                phone: user.phone || '',\n                dateOfBirth: user.dateOfBirth || '',\n                company: user.company || '',\n                jobTitle: user.jobTitle || ''\n            });\n        }\n        setIsEditing(false);\n        setMessage('');\n    };\n\n    return (\n        <div className=\"profile-management\">\n            <div className=\"section-header\">\n                <div>\n                    <h2 className=\"section-title\">Profile Information</h2>\n                    <p className=\"section-subtitle\">\n                        Manage your personal information and contact details\n                    </p>\n                </div>\n                {!isEditing && (\n                    <button\n                        className=\"btn-primary\"\n                        onClick={() => setIsEditing(true)}\n                    >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                            <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/>\n                            <path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"/>\n                        </svg>\n                        Edit Profile\n                    </button>\n                )}\n            </div>\n\n            {message && (\n                <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>\n                    {message}\n                </div>\n            )}\n\n            <form className=\"account-form\" onSubmit={handleSubmit}>\n                <div className=\"form-row\">\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"firstName\">\n                            First Name\n                        </label>\n                        <input\n                            type=\"text\"\n                            id=\"firstName\"\n                            name=\"firstName\"\n                            className=\"form-input\"\n                            value={formData.firstName}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                            required\n                        />\n                    </div>\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"lastName\">\n                            Last Name\n                        </label>\n                        <input\n                            type=\"text\"\n                            id=\"lastName\"\n                            name=\"lastName\"\n                            className=\"form-input\"\n                            value={formData.lastName}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                            required\n                        />\n                    </div>\n                </div>\n\n                <div className=\"form-group\">\n                    <label className=\"form-label\" htmlFor=\"email\">\n                        Email Address\n                    </label>\n                    <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        className=\"form-input\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        disabled={!isEditing}\n                        required\n                    />\n                </div>\n\n                <div className=\"form-row\">\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"phone\">\n                            Phone Number\n                        </label>\n                        <input\n                            type=\"tel\"\n                            id=\"phone\"\n                            name=\"phone\"\n                            className=\"form-input\"\n                            value={formData.phone}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                        />\n                    </div>\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"dateOfBirth\">\n                            Date of Birth\n                        </label>\n                        <input\n                            type=\"date\"\n                            id=\"dateOfBirth\"\n                            name=\"dateOfBirth\"\n                            className=\"form-input\"\n                            value={formData.dateOfBirth}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                        />\n                    </div>\n                </div>\n\n                <div className=\"form-row\">\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"company\">\n                            Company\n                        </label>\n                        <input\n                            type=\"text\"\n                            id=\"company\"\n                            name=\"company\"\n                            className=\"form-input\"\n                            value={formData.company}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                        />\n                    </div>\n                    <div className=\"form-group\">\n                        <label className=\"form-label\" htmlFor=\"jobTitle\">\n                            Job Title\n                        </label>\n                        <input\n                            type=\"text\"\n                            id=\"jobTitle\"\n                            name=\"jobTitle\"\n                            className=\"form-input\"\n                            value={formData.jobTitle}\n                            onChange={handleChange}\n                            disabled={!isEditing}\n                        />\n                    </div>\n                </div>\n\n                {isEditing && (\n                    <div className=\"form-actions\">\n                        <button\n                            type=\"submit\"\n                            className=\"btn-primary\"\n                            disabled={loading}\n                        >\n                            {loading ? (\n                                <>\n                                    <svg className=\"spinner\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" strokeDasharray=\"32\" strokeDashoffset=\"32\">\n                                            <animate attributeName=\"stroke-dashoffset\" dur=\"1s\" values=\"32;0;32\" repeatCount=\"indefinite\"/>\n                                        </circle>\n                                    </svg>\n                                    Saving...\n                                </>\n                            ) : (\n                                <>\n                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                        <polyline points=\"20,6 9,17 4,12\"/>\n                                    </svg>\n                                    Save Changes\n                                </>\n                            )}\n                        </button>\n                        <button\n                            type=\"button\"\n                            className=\"btn-secondary\"\n                            onClick={handleCancel}\n                            disabled={loading}\n                        >\n                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n                                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n                            </svg>\n                            Cancel\n                        </button>\n                    </div>\n                )}\n            </form>\n        </div>\n    );\n};\n\nexport default ProfileManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAGR,OAAO,CAAC,CAAC;EACzC,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACrCmB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACZ,IAAIQ,IAAI,EAAE;MACNS,WAAW,CAAC;QACRC,SAAS,EAAEV,IAAI,CAACU,SAAS,IAAI,EAAE;QAC/BC,QAAQ,EAAEX,IAAI,CAACW,QAAQ,IAAI,EAAE;QAC7BC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,EAAE;QACvBC,WAAW,EAAEd,IAAI,CAACc,WAAW,IAAI,EAAE;QACnCC,OAAO,EAAEf,IAAI,CAACe,OAAO,IAAI,EAAE;QAC3BC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ,IAAI;MAC/B,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAAChB,IAAI,CAAC,CAAC;EAEV,MAAMiB,YAAY,GAAIC,CAAC,IAAK;IACxBT,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAC9BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBlB,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA,MAAMiB,MAAM,GAAG,MAAMvB,aAAa,CAACO,QAAQ,CAAC;MAC5C,IAAIgB,MAAM,CAACC,OAAO,EAAE;QAChBlB,UAAU,CAAC,+BAA+B,CAAC;QAC3CJ,YAAY,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACHI,UAAU,CAACiB,MAAM,CAACE,KAAK,IAAI,0BAA0B,CAAC;MAC1D;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZnB,UAAU,CAAC,+CAA+C,CAAC;IAC/D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACvB;IACA,IAAI3B,IAAI,EAAE;MACNS,WAAW,CAAC;QACRC,SAAS,EAAEV,IAAI,CAACU,SAAS,IAAI,EAAE;QAC/BC,QAAQ,EAAEX,IAAI,CAACW,QAAQ,IAAI,EAAE;QAC7BC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,EAAE;QACvBC,WAAW,EAAEd,IAAI,CAACc,WAAW,IAAI,EAAE;QACnCC,OAAO,EAAEf,IAAI,CAACe,OAAO,IAAI,EAAE;QAC3BC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ,IAAI;MAC/B,CAAC,CAAC;IACN;IACAb,YAAY,CAAC,KAAK,CAAC;IACnBI,UAAU,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,oBACIZ,OAAA;IAAKiC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAC/BlC,OAAA;MAAKiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BlC,OAAA;QAAAkC,QAAA,gBACIlC,OAAA;UAAIiC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDtC,OAAA;UAAGiC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL,CAAC/B,SAAS,iBACPP,OAAA;QACIiC,SAAS,EAAC,aAAa;QACvBM,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,IAAI,CAAE;QAAA0B,QAAA,gBAElClC,OAAA;UAAKwC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAX,QAAA,gBAC7FlC,OAAA;YAAM8C,CAAC,EAAC;UAA4D;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtEtC,OAAA;YAAM8C,CAAC,EAAC;UAAyD;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,gBAEV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAEL3B,OAAO,iBACJX,OAAA;MAAKiC,SAAS,EAAE,WAAWtB,OAAO,CAACoC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,OAAO,EAAG;MAAAb,QAAA,EAC1EvB;IAAO;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAEDtC,OAAA;MAAMiC,SAAS,EAAC,cAAc;MAACe,QAAQ,EAAErB,YAAa;MAAAO,QAAA,gBAClDlC,OAAA;QAAKiC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBlC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,WAAW;YAAAf,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,WAAW;YACd1B,IAAI,EAAC,WAAW;YAChBQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACE,SAAU;YAC1BqC,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C,SAAU;YACrB+C,QAAQ;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,UAAU;YAAAf,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACb1B,IAAI,EAAC,UAAU;YACfQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACG,QAAS;YACzBoC,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C,SAAU;YACrB+C,QAAQ;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtC,OAAA;QAAKiC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBlC,OAAA;UAAOiC,SAAS,EAAC,YAAY;UAACgB,OAAO,EAAC,OAAO;UAAAf,QAAA,EAAC;QAE9C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UACIkD,IAAI,EAAC,OAAO;UACZC,EAAE,EAAC,OAAO;UACV1B,IAAI,EAAC,OAAO;UACZQ,SAAS,EAAC,YAAY;UACtBP,KAAK,EAAEb,QAAQ,CAACI,KAAM;UACtBmC,QAAQ,EAAE9B,YAAa;UACvB+B,QAAQ,EAAE,CAAC9C,SAAU;UACrB+C,QAAQ;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtC,OAAA;QAAKiC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBlC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,OAAO;YAAAf,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,KAAK;YACVC,EAAE,EAAC,OAAO;YACV1B,IAAI,EAAC,OAAO;YACZQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACK,KAAM;YACtBkC,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,aAAa;YAAAf,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,aAAa;YAChB1B,IAAI,EAAC,aAAa;YAClBQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACM,WAAY;YAC5BiC,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtC,OAAA;QAAKiC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrBlC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,SAAS;YAAAf,QAAA,EAAC;UAEhD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,SAAS;YACZ1B,IAAI,EAAC,SAAS;YACdQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACO,OAAQ;YACxBgC,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtC,OAAA;UAAKiC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBlC,OAAA;YAAOiC,SAAS,EAAC,YAAY;YAACgB,OAAO,EAAC,UAAU;YAAAf,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACb1B,IAAI,EAAC,UAAU;YACfQ,SAAS,EAAC,YAAY;YACtBP,KAAK,EAAEb,QAAQ,CAACQ,QAAS;YACzB+B,QAAQ,EAAE9B,YAAa;YACvB+B,QAAQ,EAAE,CAAC9C;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAEL/B,SAAS,iBACNP,OAAA;QAAKiC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBlC,OAAA;UACIkD,IAAI,EAAC,QAAQ;UACbjB,SAAS,EAAC,aAAa;UACvBoB,QAAQ,EAAE5C,OAAQ;UAAAyB,QAAA,EAEjBzB,OAAO,gBACJT,OAAA,CAAAE,SAAA;YAAAgC,QAAA,gBACIlC,OAAA;cAAKiC,SAAS,EAAC,SAAS;cAACO,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAAR,QAAA,eAC/DlC,OAAA;gBAAQuD,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACb,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACF,IAAI,EAAC,MAAM;gBAACe,eAAe,EAAC,IAAI;gBAACC,gBAAgB,EAAC,IAAI;gBAAAzB,QAAA,eACvHlC,OAAA;kBAAS4D,aAAa,EAAC,mBAAmB;kBAACC,GAAG,EAAC,IAAI;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAY;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,aAEV;UAAA,eAAE,CAAC,gBAEHtC,OAAA,CAAAE,SAAA;YAAAgC,QAAA,gBACIlC,OAAA;cAAKwC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAX,QAAA,eAC7FlC,OAAA;gBAAUgE,MAAM,EAAC;cAAgB;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,gBAEV;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACTtC,OAAA;UACIkD,IAAI,EAAC,QAAQ;UACbjB,SAAS,EAAC,eAAe;UACzBM,OAAO,EAAEP,YAAa;UACtBqB,QAAQ,EAAE5C,OAAQ;UAAAyB,QAAA,gBAElBlC,OAAA;YAAKwC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAX,QAAA,gBAC7FlC,OAAA;cAAMiE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAI;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACrCtC,OAAA;cAAMiE,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,UAEV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAClC,EAAA,CAhQID,iBAAiB;EAAA,QACaL,OAAO;AAAA;AAAAuE,EAAA,GADrClE,iBAAiB;AAkQvB,eAAeA,iBAAiB;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}