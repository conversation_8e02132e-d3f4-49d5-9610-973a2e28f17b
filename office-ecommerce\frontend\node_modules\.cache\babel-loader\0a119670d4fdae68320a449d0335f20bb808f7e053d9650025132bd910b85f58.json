{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\payment\\\\PayMongoCheckout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PayMongoCheckout = ({\n  orderData,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    clearCart\n  } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [{\n    id: 'card',\n    name: 'Credit/Debit Card',\n    icon: '💳',\n    description: 'Visa, Mastercard, JCB, American Express',\n    fee: '3.5% + ₱15'\n  }, {\n    id: 'gcash',\n    name: 'GCash',\n    icon: '📱',\n    description: 'Pay using your GCash wallet',\n    fee: '2.5%'\n  }, {\n    id: 'grabpay',\n    name: 'GrabPay',\n    icon: '🚗',\n    description: 'Pay using your GrabPay wallet',\n    fee: '2.5%'\n  }, {\n    id: 'bank',\n    name: 'Online Banking',\n    icon: '🏦',\n    description: 'Direct bank transfer',\n    fee: '1.5%'\n  }];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData !== null && orderData !== void 0 && orderData.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData === null || orderData === void 0 ? void 0 : orderData.totalAmount, selectedMethod]);\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      var _orderData$shippingAd, _orderData$shippingAd2, _orderData$shippingAd3, _orderData$shippingAd4, _orderData$shippingAd5, _orderData$shippingAd6, _orderData$shippingAd7, _orderData$shippingAd8, _orderData$shippingAd9, _orderData$shippingAd0, _orderData$shippingAd1, _orderData$shippingAd10, _orderData$shippingAd11, _orderData$shippingAd12, _orderData$shippingAd13, _orderData$shippingAd14, _orderData$shippingAd15;\n      // First, create a pending order in the backend\n      const shippingAddressString = `${((_orderData$shippingAd = orderData.shippingAddress) === null || _orderData$shippingAd === void 0 ? void 0 : _orderData$shippingAd.address) || ''}, ${((_orderData$shippingAd2 = orderData.shippingAddress) === null || _orderData$shippingAd2 === void 0 ? void 0 : _orderData$shippingAd2.city) || ''}, ${((_orderData$shippingAd3 = orderData.shippingAddress) === null || _orderData$shippingAd3 === void 0 ? void 0 : _orderData$shippingAd3.state) || ''} ${((_orderData$shippingAd4 = orderData.shippingAddress) === null || _orderData$shippingAd4 === void 0 ? void 0 : _orderData$shippingAd4.zipCode) || ''}, ${((_orderData$shippingAd5 = orderData.shippingAddress) === null || _orderData$shippingAd5 === void 0 ? void 0 : _orderData$shippingAd5.country) || ''}`.trim();\n      const orderCreationData = {\n        customerEmail: (_orderData$shippingAd6 = orderData.shippingAddress) === null || _orderData$shippingAd6 === void 0 ? void 0 : _orderData$shippingAd6.email,\n        customerName: `${((_orderData$shippingAd7 = orderData.shippingAddress) === null || _orderData$shippingAd7 === void 0 ? void 0 : _orderData$shippingAd7.firstName) || ''} ${((_orderData$shippingAd8 = orderData.shippingAddress) === null || _orderData$shippingAd8 === void 0 ? void 0 : _orderData$shippingAd8.lastName) || ''}`.trim(),\n        customerPhone: ((_orderData$shippingAd9 = orderData.shippingAddress) === null || _orderData$shippingAd9 === void 0 ? void 0 : _orderData$shippingAd9.phone) || undefined,\n        // Use undefined instead of empty string\n        shippingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Address not provided, will be updated from payment metadata',\n        billingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Same as shipping address',\n        items: orderData.items.map(item => ({\n          variantId: item.variantId || item.id || '00000000-0000-0000-0000-000000000000',\n          // Use a default UUID if not available\n          quantity: item.quantity || 1,\n          unitPrice: item.price || item.unitPrice || 0,\n          totalPrice: (item.quantity || 1) * (item.price || item.unitPrice || 0),\n          customConfiguration: item.customConfiguration ? JSON.stringify(item.customConfiguration) : undefined\n        })),\n        subTotal: orderData.subtotal || orderData.totalAmount || 0,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount || 0,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Debug: Log the order creation data\n      console.log('Order creation data:', orderCreationData);\n      console.log('Original order data:', orderData);\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100),\n        // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: ((_orderData$shippingAd0 = orderData.shippingAddress) === null || _orderData$shippingAd0 === void 0 ? void 0 : _orderData$shippingAd0.firstName) + ' ' + ((_orderData$shippingAd1 = orderData.shippingAddress) === null || _orderData$shippingAd1 === void 0 ? void 0 : _orderData$shippingAd1.lastName),\n          email: (_orderData$shippingAd10 = orderData.shippingAddress) === null || _orderData$shippingAd10 === void 0 ? void 0 : _orderData$shippingAd10.email,\n          phone: (_orderData$shippingAd11 = orderData.shippingAddress) === null || _orderData$shippingAd11 === void 0 ? void 0 : _orderData$shippingAd11.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: ((_orderData$shippingAd12 = orderData.shippingAddress) === null || _orderData$shippingAd12 === void 0 ? void 0 : _orderData$shippingAd12.firstName) + ' ' + ((_orderData$shippingAd13 = orderData.shippingAddress) === null || _orderData$shippingAd13 === void 0 ? void 0 : _orderData$shippingAd13.lastName),\n            email: (_orderData$shippingAd14 = orderData.shippingAddress) === null || _orderData$shippingAd14 === void 0 ? void 0 : _orderData$shippingAd14.email,\n            phone: (_orderData$shippingAd15 = orderData.shippingAddress) === null || _orderData$shippingAd15 === void 0 ? void 0 : _orderData$shippingAd15.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n\n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          var _statusResult$data;\n          const status = ((_statusResult$data = statusResult.data) === null || _statusResult$data === void 0 ? void 0 : _statusResult$data.status) || statusResult.status;\n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page - the backend order will be updated via webhook\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: orderId,\n                  total_amount: orderData.totalAmount,\n                  currency: 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully! Your order is being processed.',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paymongo-checkout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Complete Your Payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Secure payment powered by PayMongo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Order Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subtotal:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatCurrency(orderData.totalAmount || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), fees && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Payment Fee:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-row total\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Amount:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(fees.amount + fees.totalFee)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-methods\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Select Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"method-grid\",\n        children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `payment-method ${selectedMethod === method.id ? 'selected' : ''}`,\n          onClick: () => setSelectedMethod(method.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-icon\",\n            children: method.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: method.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-description\",\n              children: method.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-fee\",\n              children: [\"Fee: \", method.fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, method.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: handleCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleCreatePaymentLink,\n        disabled: loading || !(orderData !== null && orderData !== void 0 && orderData.totalAmount),\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), \"Creating Payment Link...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"payment-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), \"Pay \", fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), paymentLink && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-link-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Payment link created successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"A new tab has opened with your secure payment page. Complete your payment there and return here to see the confirmation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-link-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Reference:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reference-number\",\n            children: paymentLink.reference\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            children: paymentLink.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"security-icon\",\n          children: \"\\uD83D\\uDD10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Secure Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your payment information is encrypted and secure. PayMongo is PCI DSS compliant and follows international security standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_s(PayMongoCheckout, \"86+uGYKNFyWUeaqIj86xKtQyg4w=\", false, function () {\n  return [useNavigate, useCart];\n});\n_c = PayMongoCheckout;\nexport default PayMongoCheckout;\nvar _c;\n$RefreshReg$(_c, \"PayMongoCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCart", "paymentService", "apiClient", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PayMongoCheckout", "orderData", "onSuccess", "onError", "onCancel", "_s", "navigate", "clearCart", "loading", "setLoading", "paymentLink", "setPaymentLink", "error", "setError", "fees", "setFees", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "paymentMethods", "id", "name", "icon", "description", "fee", "totalAmount", "calculatePaymentFees", "amountInCentavos", "Math", "round", "result", "calculateFees", "success", "data", "console", "handleCreatePaymentLink", "_orderData$shippingAd", "_orderData$shippingAd2", "_orderData$shippingAd3", "_orderData$shippingAd4", "_orderData$shippingAd5", "_orderData$shippingAd6", "_orderData$shippingAd7", "_orderData$shippingAd8", "_orderData$shippingAd9", "_orderData$shippingAd0", "_orderData$shippingAd1", "_orderData$shippingAd10", "_orderData$shippingAd11", "_orderData$shippingAd12", "_orderData$shippingAd13", "_orderData$shippingAd14", "_orderData$shippingAd15", "shippingAddressString", "shippingAddress", "address", "city", "state", "zipCode", "country", "trim", "orderCreationData", "customerEmail", "email", "customerName", "firstName", "lastName", "customerPhone", "phone", "undefined", "length", "billing<PERSON><PERSON>ress", "items", "map", "item", "variantId", "quantity", "unitPrice", "price", "totalPrice", "customConfiguration", "JSON", "stringify", "subTotal", "subtotal", "taxAmount", "tax", "shippingAmount", "shipping", "discountAmount", "currency", "notes", "log", "orderResult", "post", "Error", "message", "createdOrder", "order", "OrderNumber", "paymentData", "orderId", "customer", "metadata", "paymentMethod", "source", "backendOrderId", "OrderID", "createPaymentLink", "url", "window", "open", "startPaymentStatusPolling", "backendOrder", "paymentLinkId", "pollInterval", "setInterval", "statusResult", "getPaymentStatus", "status", "_statusResult$data", "clearInterval", "order_number", "total_amount", "created_at", "Date", "toISOString", "shipping_address", "paymentStatus", "includes", "setTimeout", "handleCancel", "cancelPaymentLink", "catch", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalFee", "method", "onClick", "disabled", "reference", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/payment/PayMongoCheckout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCart } from '../../contexts/CartContext';\nimport paymentService from '../../services/paymentService';\nimport apiClient from '../../services/apiClient';\nimport './PayMongoCheckout.css';\n\nconst PayMongoCheckout = ({ orderData, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const { clearCart } = useCart();\n  const [loading, setLoading] = useState(false);\n  const [paymentLink, setPaymentLink] = useState(null);\n  const [error, setError] = useState('');\n  const [fees, setFees] = useState(null);\n  const [selectedMethod, setSelectedMethod] = useState('card');\n\n  // Payment method options\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      icon: '💳',\n      description: 'Visa, Mastercard, JCB, American Express',\n      fee: '3.5% + ₱15'\n    },\n    {\n      id: 'gcash',\n      name: 'GCash',\n      icon: '📱',\n      description: 'Pay using your GCash wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'grabpay',\n      name: 'GrabPay',\n      icon: '🚗',\n      description: 'Pay using your GrabPay wallet',\n      fee: '2.5%'\n    },\n    {\n      id: 'bank',\n      name: 'Online Banking',\n      icon: '🏦',\n      description: 'Direct bank transfer',\n      fee: '1.5%'\n    }\n  ];\n\n  // Calculate fees when component mounts or method changes\n  useEffect(() => {\n    if (orderData?.totalAmount) {\n      calculatePaymentFees();\n    }\n  }, [orderData?.totalAmount, selectedMethod]);\n\n  const calculatePaymentFees = async () => {\n    try {\n      const amountInCentavos = Math.round(orderData.totalAmount * 100); // Convert PHP to centavos\n      const result = await paymentService.calculateFees(amountInCentavos, selectedMethod);\n      if (result.success) {\n        setFees(result.data);\n      }\n    } catch (error) {\n      console.error('Fee calculation error:', error);\n    }\n  };\n\n  const handleCreatePaymentLink = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // First, create a pending order in the backend\n      const shippingAddressString = `${orderData.shippingAddress?.address || ''}, ${orderData.shippingAddress?.city || ''}, ${orderData.shippingAddress?.state || ''} ${orderData.shippingAddress?.zipCode || ''}, ${orderData.shippingAddress?.country || ''}`.trim();\n\n      const orderCreationData = {\n        customerEmail: orderData.shippingAddress?.email,\n        customerName: `${orderData.shippingAddress?.firstName || ''} ${orderData.shippingAddress?.lastName || ''}`.trim(),\n        customerPhone: orderData.shippingAddress?.phone || undefined, // Use undefined instead of empty string\n        shippingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Address not provided, will be updated from payment metadata',\n        billingAddress: shippingAddressString.length > 10 ? shippingAddressString : 'Same as shipping address',\n        items: orderData.items.map(item => ({\n          variantId: item.variantId || item.id || '00000000-0000-0000-0000-000000000000', // Use a default UUID if not available\n          quantity: item.quantity || 1,\n          unitPrice: item.price || item.unitPrice || 0,\n          totalPrice: (item.quantity || 1) * (item.price || item.unitPrice || 0),\n          customConfiguration: item.customConfiguration ? JSON.stringify(item.customConfiguration) : undefined\n        })),\n        subTotal: orderData.subtotal || orderData.totalAmount || 0,\n        taxAmount: orderData.tax || 0,\n        shippingAmount: orderData.shipping || 0,\n        discountAmount: 0,\n        totalAmount: orderData.totalAmount || 0,\n        currency: 'PHP',\n        notes: 'Order created for PayMongo payment processing'\n      };\n\n      // Debug: Log the order creation data\n      console.log('Order creation data:', orderCreationData);\n      console.log('Original order data:', orderData);\n\n      // Create order in backend\n      const orderResult = await apiClient.post('/api/orders', orderCreationData);\n\n      if (!orderResult.success) {\n        throw new Error(orderResult.message || 'Failed to create order');\n      }\n\n      const createdOrder = orderResult.data.order;\n      console.log('Order created in backend:', createdOrder.OrderNumber);\n\n      // Prepare payment data with the created order ID\n      const paymentData = {\n        orderId: createdOrder.OrderNumber,\n        totalAmount: Math.round(orderData.totalAmount * 100), // Convert PHP to centavos\n        items: orderData.items || [],\n        customer: {\n          name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n          email: orderData.shippingAddress?.email,\n          phone: orderData.shippingAddress?.phone\n        },\n        shippingAddress: orderData.shippingAddress || {},\n        metadata: {\n          paymentMethod: selectedMethod,\n          source: 'designxcel-checkout',\n          backendOrderId: createdOrder.OrderID,\n          items: JSON.stringify(orderData.items),\n          shippingAddress: JSON.stringify(orderData.shippingAddress),\n          customer: JSON.stringify({\n            name: orderData.shippingAddress?.firstName + ' ' + orderData.shippingAddress?.lastName,\n            email: orderData.shippingAddress?.email,\n            phone: orderData.shippingAddress?.phone\n          }),\n          taxAmount: orderData.tax || 0,\n          shippingAmount: orderData.shipping || 0,\n          discountAmount: 0,\n          ...orderData.metadata\n        }\n      };\n\n      const result = await paymentService.createPaymentLink(paymentData);\n\n      // Check if result has paymentLink (successful response structure)\n      if (result && result.paymentLink && result.paymentLink.url) {\n        setPaymentLink(result.paymentLink);\n\n        // Redirect to PayMongo checkout\n        window.open(result.paymentLink.url, '_blank');\n\n        // Start polling for payment status\n        startPaymentStatusPolling(createdOrder.OrderNumber, result.paymentLink.id);\n\n        if (onSuccess) {\n          onSuccess({\n            ...result,\n            backendOrder: createdOrder\n          });\n        }\n      } else {\n        throw new Error('Invalid payment link response structure');\n      }\n    } catch (error) {\n      console.error('Payment link creation error:', error);\n      setError(error.message || 'Failed to create payment link');\n      if (onError) {\n        onError(error);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startPaymentStatusPolling = (orderId, paymentLinkId) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResult = await paymentService.getPaymentStatus(orderId, paymentLinkId);\n        \n        // Handle different response structures\n        if (statusResult && (statusResult.success || statusResult.status)) {\n          const status = statusResult.data?.status || statusResult.status;\n          \n          if (status === 'paid') {\n            clearInterval(pollInterval);\n\n            // Clear cart on successful payment\n            clearCart();\n\n            // Navigate to success page - the backend order will be updated via webhook\n            navigate('/order-success', {\n              state: {\n                order: {\n                  id: orderId,\n                  order_number: orderId,\n                  total_amount: orderData.totalAmount,\n                  currency: 'PHP',\n                  status: 'paid',\n                  created_at: new Date().toISOString(),\n                  shipping_address: orderData.shippingAddress,\n                  items: orderData.items\n                },\n                message: 'Your payment has been processed successfully! Your order is being processed.',\n                paymentStatus: 'completed',\n                paymentMethod: 'PayMongo'\n              }\n            });\n          } else if (status === 'failed' || status === 'cancelled' || status === 'expired') {\n            clearInterval(pollInterval);\n            setError(`Payment ${status}. Please try again.`);\n          }\n        }\n      } catch (error) {\n        console.error('Payment status polling error:', error);\n        // If we get rate limited, stop polling to avoid further issues\n        if (error.message && error.message.includes('Too many requests')) {\n          console.log('Rate limited - stopping payment status polling');\n          clearInterval(pollInterval);\n        }\n      }\n    }, 15000); // Poll every 15 seconds to reduce rate limiting\n\n    // Stop polling after 15 minutes\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 900000);\n  };\n\n  const handleCancel = () => {\n    if (paymentLink) {\n      paymentService.cancelPaymentLink(paymentLink.id).catch(console.error);\n    }\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paymongo-checkout\">\n      <div className=\"checkout-header\">\n        <h2>Complete Your Payment</h2>\n        <p>Secure payment powered by PayMongo</p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div className=\"summary-row\">\n          <span>Subtotal:</span>\n          <span>{formatCurrency(orderData.totalAmount || 0)}</span>\n        </div>\n        {fees && (\n          <>\n            <div className=\"summary-row\">\n              <span>Payment Fee:</span>\n              <span>{formatCurrency(fees.totalFee)}</span>\n            </div>\n            <div className=\"summary-row total\">\n              <span>Total Amount:</span>\n              <span>{formatCurrency(fees.amount + fees.totalFee)}</span>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"payment-methods\">\n        <h3>Select Payment Method</h3>\n        <div className=\"method-grid\">\n          {paymentMethods.map((method) => (\n            <div\n              key={method.id}\n              className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}\n              onClick={() => setSelectedMethod(method.id)}\n            >\n              <div className=\"method-icon\">{method.icon}</div>\n              <div className=\"method-info\">\n                <div className=\"method-name\">{method.name}</div>\n                <div className=\"method-description\">{method.description}</div>\n                <div className=\"method-fee\">Fee: {method.fee}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* Payment Actions */}\n      <div className=\"payment-actions\">\n        <button\n          className=\"btn btn-secondary\"\n          onClick={handleCancel}\n          disabled={loading}\n        >\n          Cancel\n        </button>\n        <button\n          className=\"btn btn-primary\"\n          onClick={handleCreatePaymentLink}\n          disabled={loading || !orderData?.totalAmount}\n        >\n          {loading ? (\n            <>\n              <span className=\"loading-spinner\"></span>\n              Creating Payment Link...\n            </>\n          ) : (\n            <>\n              <span className=\"payment-icon\">🔒</span>\n              Pay {fees ? formatCurrency(fees.amount + fees.totalFee) : formatCurrency(orderData.totalAmount || 0)}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Payment Link Status */}\n      {paymentLink && (\n        <div className=\"payment-link-status\">\n          <div className=\"status-header\">\n            <span className=\"status-icon\">✅</span>\n            <span>Payment link created successfully!</span>\n          </div>\n          <p>\n            A new tab has opened with your secure payment page. \n            Complete your payment there and return here to see the confirmation.\n          </p>\n          <div className=\"payment-link-info\">\n            <div className=\"info-row\">\n              <span>Reference:</span>\n              <span className=\"reference-number\">{paymentLink.reference}</span>\n            </div>\n            <div className=\"info-row\">\n              <span>Status:</span>\n              <span className=\"status-badge\">{paymentLink.status}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Security Notice */}\n      <div className=\"security-notice\">\n        <div className=\"security-header\">\n          <span className=\"security-icon\">🔐</span>\n          <span>Secure Payment</span>\n        </div>\n        <p>\n          Your payment information is encrypted and secure. \n          PayMongo is PCI DSS compliant and follows international security standards.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default PayMongoCheckout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAU,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC;;EAE5D;EACA,MAAM4B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,yCAAyC;IACtDC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,6BAA6B;IAC1CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sBAAsB;IACnCC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,EAAE;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,EAAER,cAAc,CAAC,CAAC;EAE5C,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;MAClE,MAAMK,MAAM,GAAG,MAAMnC,cAAc,CAACoC,aAAa,CAACJ,gBAAgB,EAAEV,cAAc,CAAC;MACnF,IAAIa,MAAM,CAACE,OAAO,EAAE;QAClBhB,OAAO,CAACc,MAAM,CAACG,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMsB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CzB,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAsB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACF;MACA,MAAMC,qBAAqB,GAAG,GAAG,EAAAjB,qBAAA,GAAAlC,SAAS,CAACoD,eAAe,cAAAlB,qBAAA,uBAAzBA,qBAAA,CAA2BmB,OAAO,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAAnC,SAAS,CAACoD,eAAe,cAAAjB,sBAAA,uBAAzBA,sBAAA,CAA2BmB,IAAI,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAApC,SAAS,CAACoD,eAAe,cAAAhB,sBAAA,uBAAzBA,sBAAA,CAA2BmB,KAAK,KAAI,EAAE,IAAI,EAAAlB,sBAAA,GAAArC,SAAS,CAACoD,eAAe,cAAAf,sBAAA,uBAAzBA,sBAAA,CAA2BmB,OAAO,KAAI,EAAE,KAAK,EAAAlB,sBAAA,GAAAtC,SAAS,CAACoD,eAAe,cAAAd,sBAAA,uBAAzBA,sBAAA,CAA2BmB,OAAO,KAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;MAEhQ,MAAMC,iBAAiB,GAAG;QACxBC,aAAa,GAAArB,sBAAA,GAAEvC,SAAS,CAACoD,eAAe,cAAAb,sBAAA,uBAAzBA,sBAAA,CAA2BsB,KAAK;QAC/CC,YAAY,EAAE,GAAG,EAAAtB,sBAAA,GAAAxC,SAAS,CAACoD,eAAe,cAAAZ,sBAAA,uBAAzBA,sBAAA,CAA2BuB,SAAS,KAAI,EAAE,IAAI,EAAAtB,sBAAA,GAAAzC,SAAS,CAACoD,eAAe,cAAAX,sBAAA,uBAAzBA,sBAAA,CAA2BuB,QAAQ,KAAI,EAAE,EAAE,CAACN,IAAI,CAAC,CAAC;QACjHO,aAAa,EAAE,EAAAvB,sBAAA,GAAA1C,SAAS,CAACoD,eAAe,cAAAV,sBAAA,uBAAzBA,sBAAA,CAA2BwB,KAAK,KAAIC,SAAS;QAAE;QAC9Df,eAAe,EAAED,qBAAqB,CAACiB,MAAM,GAAG,EAAE,GAAGjB,qBAAqB,GAAG,6DAA6D;QAC1IkB,cAAc,EAAElB,qBAAqB,CAACiB,MAAM,GAAG,EAAE,GAAGjB,qBAAqB,GAAG,0BAA0B;QACtGmB,KAAK,EAAEtE,SAAS,CAACsE,KAAK,CAACC,GAAG,CAACC,IAAI,KAAK;UAClCC,SAAS,EAAED,IAAI,CAACC,SAAS,IAAID,IAAI,CAACtD,EAAE,IAAI,sCAAsC;UAAE;UAChFwD,QAAQ,EAAEF,IAAI,CAACE,QAAQ,IAAI,CAAC;UAC5BC,SAAS,EAAEH,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACG,SAAS,IAAI,CAAC;UAC5CE,UAAU,EAAE,CAACL,IAAI,CAACE,QAAQ,IAAI,CAAC,KAAKF,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACG,SAAS,IAAI,CAAC,CAAC;UACtEG,mBAAmB,EAAEN,IAAI,CAACM,mBAAmB,GAAGC,IAAI,CAACC,SAAS,CAACR,IAAI,CAACM,mBAAmB,CAAC,GAAGX;QAC7F,CAAC,CAAC,CAAC;QACHc,QAAQ,EAAEjF,SAAS,CAACkF,QAAQ,IAAIlF,SAAS,CAACuB,WAAW,IAAI,CAAC;QAC1D4D,SAAS,EAAEnF,SAAS,CAACoF,GAAG,IAAI,CAAC;QAC7BC,cAAc,EAAErF,SAAS,CAACsF,QAAQ,IAAI,CAAC;QACvCC,cAAc,EAAE,CAAC;QACjBhE,WAAW,EAAEvB,SAAS,CAACuB,WAAW,IAAI,CAAC;QACvCiE,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE;MACT,CAAC;;MAED;MACAzD,OAAO,CAAC0D,GAAG,CAAC,sBAAsB,EAAE/B,iBAAiB,CAAC;MACtD3B,OAAO,CAAC0D,GAAG,CAAC,sBAAsB,EAAE1F,SAAS,CAAC;;MAE9C;MACA,MAAM2F,WAAW,GAAG,MAAMjG,SAAS,CAACkG,IAAI,CAAC,aAAa,EAAEjC,iBAAiB,CAAC;MAE1E,IAAI,CAACgC,WAAW,CAAC7D,OAAO,EAAE;QACxB,MAAM,IAAI+D,KAAK,CAACF,WAAW,CAACG,OAAO,IAAI,wBAAwB,CAAC;MAClE;MAEA,MAAMC,YAAY,GAAGJ,WAAW,CAAC5D,IAAI,CAACiE,KAAK;MAC3ChE,OAAO,CAAC0D,GAAG,CAAC,2BAA2B,EAAEK,YAAY,CAACE,WAAW,CAAC;;MAElE;MACA,MAAMC,WAAW,GAAG;QAClBC,OAAO,EAAEJ,YAAY,CAACE,WAAW;QACjC1E,WAAW,EAAEG,IAAI,CAACC,KAAK,CAAC3B,SAAS,CAACuB,WAAW,GAAG,GAAG,CAAC;QAAE;QACtD+C,KAAK,EAAEtE,SAAS,CAACsE,KAAK,IAAI,EAAE;QAC5B8B,QAAQ,EAAE;UACRjF,IAAI,EAAE,EAAAwB,sBAAA,GAAA3C,SAAS,CAACoD,eAAe,cAAAT,sBAAA,uBAAzBA,sBAAA,CAA2BoB,SAAS,IAAG,GAAG,KAAAnB,sBAAA,GAAG5C,SAAS,CAACoD,eAAe,cAAAR,sBAAA,uBAAzBA,sBAAA,CAA2BoB,QAAQ;UACtFH,KAAK,GAAAhB,uBAAA,GAAE7C,SAAS,CAACoD,eAAe,cAAAP,uBAAA,uBAAzBA,uBAAA,CAA2BgB,KAAK;UACvCK,KAAK,GAAApB,uBAAA,GAAE9C,SAAS,CAACoD,eAAe,cAAAN,uBAAA,uBAAzBA,uBAAA,CAA2BoB;QACpC,CAAC;QACDd,eAAe,EAAEpD,SAAS,CAACoD,eAAe,IAAI,CAAC,CAAC;QAChDiD,QAAQ,EAAE;UACRC,aAAa,EAAEvF,cAAc;UAC7BwF,MAAM,EAAE,qBAAqB;UAC7BC,cAAc,EAAET,YAAY,CAACU,OAAO;UACpCnC,KAAK,EAAES,IAAI,CAACC,SAAS,CAAChF,SAAS,CAACsE,KAAK,CAAC;UACtClB,eAAe,EAAE2B,IAAI,CAACC,SAAS,CAAChF,SAAS,CAACoD,eAAe,CAAC;UAC1DgD,QAAQ,EAAErB,IAAI,CAACC,SAAS,CAAC;YACvB7D,IAAI,EAAE,EAAA4B,uBAAA,GAAA/C,SAAS,CAACoD,eAAe,cAAAL,uBAAA,uBAAzBA,uBAAA,CAA2BgB,SAAS,IAAG,GAAG,KAAAf,uBAAA,GAAGhD,SAAS,CAACoD,eAAe,cAAAJ,uBAAA,uBAAzBA,uBAAA,CAA2BgB,QAAQ;YACtFH,KAAK,GAAAZ,uBAAA,GAAEjD,SAAS,CAACoD,eAAe,cAAAH,uBAAA,uBAAzBA,uBAAA,CAA2BY,KAAK;YACvCK,KAAK,GAAAhB,uBAAA,GAAElD,SAAS,CAACoD,eAAe,cAAAF,uBAAA,uBAAzBA,uBAAA,CAA2BgB;UACpC,CAAC,CAAC;UACFiB,SAAS,EAAEnF,SAAS,CAACoF,GAAG,IAAI,CAAC;UAC7BC,cAAc,EAAErF,SAAS,CAACsF,QAAQ,IAAI,CAAC;UACvCC,cAAc,EAAE,CAAC;UACjB,GAAGvF,SAAS,CAACqG;QACf;MACF,CAAC;MAED,MAAMzE,MAAM,GAAG,MAAMnC,cAAc,CAACiH,iBAAiB,CAACR,WAAW,CAAC;;MAElE;MACA,IAAItE,MAAM,IAAIA,MAAM,CAACnB,WAAW,IAAImB,MAAM,CAACnB,WAAW,CAACkG,GAAG,EAAE;QAC1DjG,cAAc,CAACkB,MAAM,CAACnB,WAAW,CAAC;;QAElC;QACAmG,MAAM,CAACC,IAAI,CAACjF,MAAM,CAACnB,WAAW,CAACkG,GAAG,EAAE,QAAQ,CAAC;;QAE7C;QACAG,yBAAyB,CAACf,YAAY,CAACE,WAAW,EAAErE,MAAM,CAACnB,WAAW,CAACS,EAAE,CAAC;QAE1E,IAAIjB,SAAS,EAAE;UACbA,SAAS,CAAC;YACR,GAAG2B,MAAM;YACTmF,YAAY,EAAEhB;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAACD,KAAK,CAACmF,OAAO,IAAI,+BAA+B,CAAC;MAC1D,IAAI5F,OAAO,EAAE;QACXA,OAAO,CAACS,KAAK,CAAC;MAChB;IACF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,yBAAyB,GAAGA,CAACX,OAAO,EAAEa,aAAa,KAAK;IAC5D,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,YAAY,GAAG,MAAM1H,cAAc,CAAC2H,gBAAgB,CAACjB,OAAO,EAAEa,aAAa,CAAC;;QAElF;QACA,IAAIG,YAAY,KAAKA,YAAY,CAACrF,OAAO,IAAIqF,YAAY,CAACE,MAAM,CAAC,EAAE;UAAA,IAAAC,kBAAA;UACjE,MAAMD,MAAM,GAAG,EAAAC,kBAAA,GAAAH,YAAY,CAACpF,IAAI,cAAAuF,kBAAA,uBAAjBA,kBAAA,CAAmBD,MAAM,KAAIF,YAAY,CAACE,MAAM;UAE/D,IAAIA,MAAM,KAAK,MAAM,EAAE;YACrBE,aAAa,CAACN,YAAY,CAAC;;YAE3B;YACA3G,SAAS,CAAC,CAAC;;YAEX;YACAD,QAAQ,CAAC,gBAAgB,EAAE;cACzBkD,KAAK,EAAE;gBACLyC,KAAK,EAAE;kBACL9E,EAAE,EAAEiF,OAAO;kBACXqB,YAAY,EAAErB,OAAO;kBACrBsB,YAAY,EAAEzH,SAAS,CAACuB,WAAW;kBACnCiE,QAAQ,EAAE,KAAK;kBACf6B,MAAM,EAAE,MAAM;kBACdK,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACpCC,gBAAgB,EAAE7H,SAAS,CAACoD,eAAe;kBAC3CkB,KAAK,EAAEtE,SAAS,CAACsE;gBACnB,CAAC;gBACDwB,OAAO,EAAE,8EAA8E;gBACvFgC,aAAa,EAAE,WAAW;gBAC1BxB,aAAa,EAAE;cACjB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIe,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,SAAS,EAAE;YAChFE,aAAa,CAACN,YAAY,CAAC;YAC3BrG,QAAQ,CAAC,WAAWyG,MAAM,qBAAqB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAO1G,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAIA,KAAK,CAACmF,OAAO,IAAInF,KAAK,CAACmF,OAAO,CAACiC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAChE/F,OAAO,CAAC0D,GAAG,CAAC,gDAAgD,CAAC;UAC7D6B,aAAa,CAACN,YAAY,CAAC;QAC7B;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACAe,UAAU,CAAC,MAAM;MACfT,aAAa,CAACN,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxH,WAAW,EAAE;MACfhB,cAAc,CAACyI,iBAAiB,CAACzH,WAAW,CAACS,EAAE,CAAC,CAACiH,KAAK,CAACnG,OAAO,CAACrB,KAAK,CAAC;IACvE;IACA,IAAIR,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMiI,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBhD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACiD,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACEzI,OAAA;IAAK8I,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC/I,OAAA;MAAK8I,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/I,OAAA;QAAA+I,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BnJ,OAAA;QAAA+I,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGNnJ,OAAA;MAAK8I,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/I,OAAA;QAAA+I,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBnJ,OAAA;QAAK8I,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/I,OAAA;UAAA+I,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtBnJ,OAAA;UAAA+I,QAAA,EAAOP,cAAc,CAACpI,SAAS,CAACuB,WAAW,IAAI,CAAC;QAAC;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACLlI,IAAI,iBACHjB,OAAA,CAAAE,SAAA;QAAA6I,QAAA,gBACE/I,OAAA;UAAK8I,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/I,OAAA;YAAA+I,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBnJ,OAAA;YAAA+I,QAAA,EAAOP,cAAc,CAACvH,IAAI,CAACmI,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNnJ,OAAA;UAAK8I,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/I,OAAA;YAAA+I,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BnJ,OAAA;YAAA+I,QAAA,EAAOP,cAAc,CAACvH,IAAI,CAACwH,MAAM,GAAGxH,IAAI,CAACmI,QAAQ;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnJ,OAAA;MAAK8I,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/I,OAAA;QAAA+I,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BnJ,OAAA;QAAK8I,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB1H,cAAc,CAACsD,GAAG,CAAE0E,MAAM,iBACzBrJ,OAAA;UAEE8I,SAAS,EAAE,kBAAkB3H,cAAc,KAAKkI,MAAM,CAAC/H,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9EgI,OAAO,EAAEA,CAAA,KAAMlI,iBAAiB,CAACiI,MAAM,CAAC/H,EAAE,CAAE;UAAAyH,QAAA,gBAE5C/I,OAAA;YAAK8I,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,MAAM,CAAC7H;UAAI;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDnJ,OAAA;YAAK8I,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/I,OAAA;cAAK8I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEM,MAAM,CAAC9H;YAAI;cAAAyH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDnJ,OAAA;cAAK8I,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEM,MAAM,CAAC5H;YAAW;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,OAAK,EAACM,MAAM,CAAC3H,GAAG;YAAA;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA,GATDE,MAAM,CAAC/H,EAAE;UAAA0H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpI,KAAK,iBACJf,OAAA;MAAK8I,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/I,OAAA;QAAM8I,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCnJ,OAAA;QAAA+I,QAAA,EAAOhI;MAAK;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDnJ,OAAA;MAAK8I,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/I,OAAA;QACE8I,SAAS,EAAC,mBAAmB;QAC7BQ,OAAO,EAAEjB,YAAa;QACtBkB,QAAQ,EAAE5I,OAAQ;QAAAoI,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnJ,OAAA;QACE8I,SAAS,EAAC,iBAAiB;QAC3BQ,OAAO,EAAEjH,uBAAwB;QACjCkH,QAAQ,EAAE5I,OAAO,IAAI,EAACP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,WAAW,CAAC;QAAAoH,QAAA,EAE5CpI,OAAO,gBACNX,OAAA,CAAAE,SAAA;UAAA6I,QAAA,gBACE/I,OAAA;YAAM8I,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA,eAAE,CAAC,gBAEHnJ,OAAA,CAAAE,SAAA;UAAA6I,QAAA,gBACE/I,OAAA;YAAM8I,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,QACpC,EAAClI,IAAI,GAAGuH,cAAc,CAACvH,IAAI,CAACwH,MAAM,GAAGxH,IAAI,CAACmI,QAAQ,CAAC,GAAGZ,cAAc,CAACpI,SAAS,CAACuB,WAAW,IAAI,CAAC,CAAC;QAAA,eACpG;MACH;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLtI,WAAW,iBACVb,OAAA;MAAK8I,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC/I,OAAA;QAAK8I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/I,OAAA;UAAM8I,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtCnJ,OAAA;UAAA+I,QAAA,EAAM;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNnJ,OAAA;QAAA+I,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnJ,OAAA;QAAK8I,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/I,OAAA;UAAK8I,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/I,OAAA;YAAA+I,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvBnJ,OAAA;YAAM8I,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAElI,WAAW,CAAC2I;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNnJ,OAAA;UAAK8I,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/I,OAAA;YAAA+I,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpBnJ,OAAA;YAAM8I,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAElI,WAAW,CAAC4G;UAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnJ,OAAA;MAAK8I,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/I,OAAA;QAAK8I,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/I,OAAA;UAAM8I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCnJ,OAAA;UAAA+I,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNnJ,OAAA;QAAA+I,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3I,EAAA,CArWIL,gBAAgB;EAAA,QACHR,WAAW,EACNC,OAAO;AAAA;AAAA6J,EAAA,GAFzBtJ,gBAAgB;AAuWtB,eAAeA,gBAAgB;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}