{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\common\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    // Add confirmation dialog for better UX\n    if (window.confirm('Are you sure you want to logout?')) {\n      logout();\n      navigate('/');\n      setIsMenuOpen(false);\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"special-offer-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"offer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-icon\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-text\",\n            children: t('specialOffer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"offer-details\",\n            children: t('offerText')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-shop-btn\",\n            children: t('shopNow')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"offer-close\",\n            onClick: () => document.querySelector('.special-offer-banner').style.display = 'none',\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"top-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"top-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 33\n              }, this), \"<EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"14\",\n                height: \"14\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                style: {\n                  marginRight: '0.5rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 33\n              }, this), \"(02) 413-6682\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"location-info\",\n            children: \"#1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(CurrencyLanguageSelector, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"main-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-search\",\n            children: /*#__PURE__*/_jsxDEV(SearchInput, {\n              className: \"header-search-input\",\n              placeholder: t('searchProducts')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-logo\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"logo\",\n              children: /*#__PURE__*/_jsxDEV(Logo, {\n                size: \"default\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-tagline\",\n              children: \"EXCELLENCE IN DESIGN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-actions\",\n            children: [isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-menu\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: \"action-btn user-btn\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-greeting\",\n                children: [\"Hello, \", user === null || user === void 0 ? void 0 : user.firstName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"logout-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"16,17 21,12 16,7\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"21\",\n                    y1: \"12\",\n                    x2: \"9\",\n                    y2: \"12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"action-btn user-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(CartIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"action-btn contact-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\",\n                  stroke: \"#F0B21B\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"mobile-menu-toggle\",\n              onClick: toggleMenu,\n              \"aria-label\": \"Toggle menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navigation-bar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"main-navigation\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-link\",\n            children: t('home')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"nav-link\",\n            children: t('products')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/configurator\",\n            className: \"nav-link\",\n            children: t('customFurniture')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/gallery\",\n            className: \"nav-link\",\n            children: t('gallery')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"nav-link\",\n            children: t('about')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"nav-link\",\n            children: t('contact')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/payment\",\n            className: \"nav-link\",\n            children: t('payments')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `mobile-nav ${isMenuOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('home')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('products')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/configurator\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('customFurniture')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/gallery\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('gallery')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('about')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/contact\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('contact')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/payment\",\n        className: \"mobile-nav-link\",\n        onClick: () => setIsMenuOpen(false),\n        children: t('payments')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 17\n      }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"mobile-nav-link login\",\n        onClick: () => setIsMenuOpen(false),\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 9\n  }, this);\n};\n_s(Header, \"nwOr1waml6pnUxh8Xn3Ye/rKzp8=\", false, function () {\n  return [useAuth, useLanguage, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "useLanguage", "CartIcon", "Logo", "CurrencyLanguageSelector", "SearchInput", "jsxDEV", "_jsxDEV", "Header", "_s", "user", "logout", "isAuthenticated", "t", "isMenuOpen", "setIsMenuOpen", "navigate", "handleLogout", "window", "confirm", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "document", "querySelector", "style", "display", "width", "height", "viewBox", "fill", "xmlns", "marginRight", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "points", "placeholder", "to", "size", "firstName", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/common/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CartIcon from '../cart/CartIcon';\nimport Logo from './Logo';\nimport CurrencyLanguageSelector from './CurrencyLanguageSelector';\nimport SearchInput from '../search/SearchInput';\nimport '../../styles/components.css';\n\nconst Header = () => {\n    const { user, logout, isAuthenticated } = useAuth();\n    const { t } = useLanguage();\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const navigate = useNavigate();\n\n    const handleLogout = () => {\n        // Add confirmation dialog for better UX\n        if (window.confirm('Are you sure you want to logout?')) {\n            logout();\n            navigate('/');\n            setIsMenuOpen(false);\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <header className=\"header\">\n            {/* Special Offer Banner */}\n            <div className=\"special-offer-banner\">\n                <div className=\"container\">\n                    <div className=\"offer-content\">\n                        <span className=\"offer-icon\">⚡</span>\n                        <span className=\"offer-text\">{t('specialOffer')}</span>\n                        <span className=\"offer-details\">{t('offerText')}</span>\n                        <button className=\"offer-shop-btn\">{t('shopNow')}</button>\n                        <button className=\"offer-close\" onClick={() => document.querySelector('.special-offer-banner').style.display = 'none'}>×</button>\n                    </div>\n                </div>\n            </div>\n\n            {/* Top Header Info */}\n            <div className=\"top-header\">\n                <div className=\"container\">\n                    <div className=\"top-header-content\">\n                        <div className=\"contact-info\">\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                <EMAIL>\n                            </span>\n                            <span className=\"contact-item\">\n                                <svg\n                                    width=\"14\"\n                                    height=\"14\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    style={{ marginRight: '0.5rem' }}\n                                >\n                                    <path\n                                        d=\"M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                                (02) 413-6682\n                            </span>\n                        </div>\n                        <div className=\"location-info\">\n                            #1 Binakla Street Cor Biak na Bato Brgy, Manresa, Quezon City\n                        </div>\n                        <CurrencyLanguageSelector />\n                    </div>\n                </div>\n            </div>\n\n            {/* Main Header */}\n            <div className=\"main-header\">\n                <div className=\"container\">\n                    <div className=\"main-header-content\">\n                        {/* Search */}\n                        <div className=\"header-search\">\n                            <SearchInput\n                                className=\"header-search-input\"\n                                placeholder={t('searchProducts')}\n                            />\n                        </div>\n\n                        {/* Centered Logo */}\n                        <div className=\"header-logo\">\n                            <Link to=\"/\" className=\"logo\">\n                                <Logo size=\"default\" />\n                            </Link>\n                            <div className=\"logo-tagline\">EXCELLENCE IN DESIGN</div>\n                        </div>\n\n                        {/* User Actions */}\n                        <div className=\"header-actions\">\n                            {/* User Account */}\n                            {isAuthenticated ? (\n                                <div className=\"user-menu\">\n                                    <Link to=\"/account\" className=\"action-btn user-btn\">\n                                        <svg\n                                            width=\"20\"\n                                            height=\"20\"\n                                            viewBox=\"0 0 24 24\"\n                                            fill=\"none\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                        >\n                                            <path\n                                                d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                                stroke=\"#F0B21B\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                        </svg>\n                                    </Link>\n                                    <span className=\"user-greeting\">\n                                        Hello, {user?.firstName}\n                                    </span>\n                                    <button onClick={handleLogout} className=\"logout-btn\">\n                                        <svg\n                                            width=\"16\"\n                                            height=\"16\"\n                                            viewBox=\"0 0 24 24\"\n                                            fill=\"none\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                        >\n                                            <path\n                                                d=\"M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                            <polyline\n                                                points=\"16,17 21,12 16,7\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                            <line\n                                                x1=\"21\"\n                                                y1=\"12\"\n                                                x2=\"9\"\n                                                y2=\"12\"\n                                                stroke=\"currentColor\"\n                                                strokeWidth=\"2\"\n                                                strokeLinecap=\"round\"\n                                                strokeLinejoin=\"round\"\n                                            />\n                                        </svg>\n                                        <span>Logout</span>\n                                    </button>\n                                </div>\n                            ) : (\n                                <Link to=\"/login\" className=\"action-btn user-btn\">\n                                    <svg\n                                        width=\"20\"\n                                        height=\"20\"\n                                        viewBox=\"0 0 24 24\"\n                                        fill=\"none\"\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                    >\n                                        <path\n                                            d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z\"\n                                            stroke=\"#F0B21B\"\n                                            strokeWidth=\"2\"\n                                            strokeLinecap=\"round\"\n                                            strokeLinejoin=\"round\"\n                                        />\n                                    </svg>\n                                </Link>\n                            )}\n\n                            {/* Shopping Cart */}\n                            <CartIcon />\n\n                            {/* Contact */}\n                            <Link to=\"/contact\" className=\"action-btn contact-btn\">\n                                <svg\n                                    width=\"20\"\n                                    height=\"20\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                >\n                                    <path\n                                        d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                    <polyline\n                                        points=\"22,6 12,13 2,6\"\n                                        stroke=\"#F0B21B\"\n                                        strokeWidth=\"2\"\n                                        strokeLinecap=\"round\"\n                                        strokeLinejoin=\"round\"\n                                    />\n                                </svg>\n                            </Link>\n\n                            {/* Mobile Menu Toggle */}\n                            <button\n                                className=\"mobile-menu-toggle\"\n                                onClick={toggleMenu}\n                                aria-label=\"Toggle menu\"\n                            >\n                                <span></span>\n                                <span></span>\n                                <span></span>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Navigation Bar */}\n            <div className=\"navigation-bar\">\n                <div className=\"container\">\n                    <nav className=\"main-navigation\">\n                        <Link to=\"/\" className=\"nav-link\">{t('home')}</Link>\n                        <Link to=\"/products\" className=\"nav-link\">{t('products')}</Link>\n                        <Link to=\"/configurator\" className=\"nav-link\">{t('customFurniture')}</Link>\n                        <Link to=\"/gallery\" className=\"nav-link\">{t('gallery')}</Link>\n                        <Link to=\"/about\" className=\"nav-link\">{t('about')}</Link>\n                        <Link to=\"/contact\" className=\"nav-link\">{t('contact')}</Link>\n                        <Link to=\"/payment\" className=\"nav-link\">{t('payments')}</Link>\n                    </nav>\n                </div>\n            </div>\n\n            {/* Mobile Navigation */}\n            <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>\n                <Link\n                    to=\"/\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('home')}\n                </Link>\n                <Link\n                    to=\"/products\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('products')}\n                </Link>\n                <Link\n                    to=\"/configurator\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('customFurniture')}\n                </Link>\n                <Link\n                    to=\"/gallery\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('gallery')}\n                </Link>\n                <Link\n                    to=\"/about\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('about')}\n                </Link>\n                <Link\n                    to=\"/contact\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('contact')}\n                </Link>\n                <Link\n                    to=\"/payment\"\n                    className=\"mobile-nav-link\"\n                    onClick={() => setIsMenuOpen(false)}\n                >\n                    {t('payments')}\n                </Link>\n\n                {!isAuthenticated && (\n                    <Link\n                        to=\"/login\"\n                        className=\"mobile-nav-link login\"\n                        onClick={() => setIsMenuOpen(false)}\n                    >\n                        Login\n                    </Link>\n                )}\n            </nav>\n        </header>\n    );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEa;EAAE,CAAC,GAAGZ,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACvB;IACA,IAAIC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACpDR,MAAM,CAAC,CAAC;MACRK,QAAQ,CAAC,GAAG,CAAC;MACbD,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACrBL,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIP,OAAA;IAAQc,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAEtBf,OAAA;MAAKc,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACjCf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBf,OAAA;UAAKc,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1Bf,OAAA;YAAMc,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCnB,OAAA;YAAMc,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAET,CAAC,CAAC,cAAc;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDnB,OAAA;YAAMc,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAET,CAAC,CAAC,WAAW;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDnB,OAAA;YAAQc,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAET,CAAC,CAAC,SAAS;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC1DnB,OAAA;YAAQc,SAAS,EAAC,aAAa;YAACM,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAC,CAACC,KAAK,CAACC,OAAO,GAAG,MAAO;YAAAT,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvBf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBf,OAAA;UAAKc,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/Bf,OAAA;YAAKc,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBf,OAAA;cAAMc,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1Bf,OAAA;gBACIyB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCN,KAAK,EAAE;kBAAEO,WAAW,EAAE;gBAAS,CAAE;gBAAAf,QAAA,gBAEjCf,OAAA;kBACI+B,CAAC,EAAC,6FAA6F;kBAC/FC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFnB,OAAA;kBACIoC,MAAM,EAAC,gBAAgB;kBACvBJ,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,+BAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnB,OAAA;cAAMc,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1Bf,OAAA;gBACIyB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAClCN,KAAK,EAAE;kBAAEO,WAAW,EAAE;gBAAS,CAAE;gBAAAf,QAAA,eAEjCf,OAAA;kBACI+B,CAAC,EAAC,+hCAA+hC;kBACjiCC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,iBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnB,OAAA;YAAKc,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnB,OAAA,CAACH,wBAAwB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBf,OAAA;UAAKc,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAEhCf,OAAA;YAAKc,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1Bf,OAAA,CAACF,WAAW;cACRgB,SAAS,EAAC,qBAAqB;cAC/BuB,WAAW,EAAE/B,CAAC,CAAC,gBAAgB;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNnB,OAAA;YAAKc,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBf,OAAA,CAACT,IAAI;cAAC+C,EAAE,EAAC,GAAG;cAACxB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACzBf,OAAA,CAACJ,IAAI;gBAAC2C,IAAI,EAAC;cAAS;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACPnB,OAAA;cAAKc,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAGNnB,OAAA;YAAKc,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAE1BV,eAAe,gBACZL,OAAA;cAAKc,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBf,OAAA,CAACT,IAAI;gBAAC+C,EAAE,EAAC,UAAU;gBAACxB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAC/Cf,OAAA;kBACIyB,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,eAElCf,OAAA;oBACI+B,CAAC,EAAC,oRAAoR;oBACtRC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACPnB,OAAA;gBAAMc,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,SACrB,EAACZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,SAAS;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACPnB,OAAA;gBAAQoB,OAAO,EAAEV,YAAa;gBAACI,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACjDf,OAAA;kBACIyB,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,gBAElCf,OAAA;oBACI+B,CAAC,EAAC,+JAA+J;oBACjKC,MAAM,EAAC,cAAc;oBACrBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACFnB,OAAA;oBACIoC,MAAM,EAAC,kBAAkB;oBACzBJ,MAAM,EAAC,cAAc;oBACrBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACFnB,OAAA;oBACIyC,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,GAAG;oBACNC,EAAE,EAAC,IAAI;oBACPZ,MAAM,EAAC,cAAc;oBACrBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNnB,OAAA;kBAAAe,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,gBAENnB,OAAA,CAACT,IAAI;cAAC+C,EAAE,EAAC,QAAQ;cAACxB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAC7Cf,OAAA;gBACIyB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAAAd,QAAA,eAElCf,OAAA;kBACI+B,CAAC,EAAC,oRAAoR;kBACtRC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACT,eAGDnB,OAAA,CAACL,QAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGZnB,OAAA,CAACT,IAAI;cAAC+C,EAAE,EAAC,UAAU;cAACxB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eAClDf,OAAA;gBACIyB,KAAK,EAAC,IAAI;gBACVC,MAAM,EAAC,IAAI;gBACXC,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,4BAA4B;gBAAAd,QAAA,gBAElCf,OAAA;kBACI+B,CAAC,EAAC,6FAA6F;kBAC/FC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFnB,OAAA;kBACIoC,MAAM,EAAC,gBAAgB;kBACvBJ,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGPnB,OAAA;cACIc,SAAS,EAAC,oBAAoB;cAC9BM,OAAO,EAAEP,UAAW;cACpB,cAAW,aAAa;cAAAE,QAAA,gBAExBf,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnB,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnB,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3Bf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBf,OAAA;UAAKc,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5Bf,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,GAAG;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,MAAM;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,WAAW;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,UAAU;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChEnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,eAAe;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,iBAAiB;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3EnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,SAAS;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,QAAQ;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,OAAO;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1DnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,SAAS;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DnB,OAAA,CAACT,IAAI;YAAC+C,EAAE,EAAC,UAAU;YAACxB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAET,CAAC,CAAC,UAAU;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNnB,OAAA;MAAKc,SAAS,EAAE,cAAcP,UAAU,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAQ,QAAA,gBACrDf,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,GAAG;QACNxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,MAAM;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,WAAW;QACdxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,UAAU;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,eAAe;QAClBxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,iBAAiB;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,SAAS;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,QAAQ;QACXxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,OAAO;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,SAAS;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACPnB,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,UAAU;QACbxB,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EAEnCT,CAAC,CAAC,UAAU;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EAEN,CAACd,eAAe,iBACbL,OAAA,CAACT,IAAI;QACD+C,EAAE,EAAC,QAAQ;QACXxB,SAAS,EAAC,uBAAuB;QACjCM,OAAO,EAAEA,CAAA,KAAMZ,aAAa,CAAC,KAAK,CAAE;QAAAO,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEjB,CAAC;AAACjB,EAAA,CA7TID,MAAM;EAAA,QACkCR,OAAO,EACnCC,WAAW,EAERF,WAAW;AAAA;AAAAqD,EAAA,GAJ1B5C,MAAM;AA+TZ,eAAeA,MAAM;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}