import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';

const CartItem = ({ item }) => {
    const { updateQuantity, removeFromCart } = useCart();

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const handleQuantityChange = (newQuantity) => {
        if (newQuantity < 1) {
            removeFromCart(item.id);
        } else {
            updateQuantity(item.id, newQuantity);
        }
    };

    const getCustomizationDisplay = () => {
        const { customization } = item;
        if (!customization || Object.keys(customization).length === 0) {
            return null;
        }

        const customizations = [];
        if (customization.color) {
            customizations.push(`Color: ${customization.color}`);
        }
        if (customization.material) {
            customizations.push(`Material: ${customization.material}`);
        }
        if (customization.dimensions) {
            const { width, height, depth } = customization.dimensions;
            customizations.push(`Size: ${width}×${height}×${depth}cm`);
        }

        return customizations.length > 0 ? customizations.join(', ') : null;
    };

    const customizationText = getCustomizationDisplay();

    return (
        <div className="cart-item">
            <div className="cart-item-image">
                <Link to={`/products/${item.product.id}`}>
                    <img 
                        src={item.product.images?.[0] || 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150'} 
                        alt={item.product.name}
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';
                        }}
                    />
                </Link>
            </div>

            <div className="cart-item-details">
                <div className="cart-item-info">
                    <Link 
                        to={`/products/${item.product.id}`}
                        className="cart-item-name"
                    >
                        {item.product.name}
                    </Link>
                    
                    {customizationText && (
                        <div className="cart-item-customization">
                            {customizationText}
                        </div>
                    )}

                    <div className="cart-item-price">
                        {formatPrice(item.price)}
                    </div>
                </div>

                <div className="cart-item-controls">
                    <div className="quantity-controls">
                        <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.quantity - 1)}
                            aria-label="Decrease quantity"
                        >
                            −
                        </button>
                        <span className="quantity-display">
                            {item.quantity}
                        </span>
                        <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.quantity + 1)}
                            aria-label="Increase quantity"
                        >
                            +
                        </button>
                    </div>

                    <div className="cart-item-actions">
                        <div className="cart-item-total">
                            {formatPrice(item.price * item.quantity)}
                        </div>
                        <button
                            className="remove-btn"
                            onClick={() => removeFromCart(item.id)}
                            aria-label="Remove item from cart"
                            title="Remove from cart"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <path d="M10 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <path d="M14 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CartItem;
